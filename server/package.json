{"name": "exchanger-server", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"build": "tsc", "lint": "eslint src --ignore-path .eslint<PERSON>ore --ext .ts", "lint:fix": "npx eslint src --fix", "prettier": "prettier --ignore-path .gitignore --write \"./src/**/*.+(js|ts|json)\"", "prettier:fix": "npx prettier --write src", "start:dev": "ts-node-dev --respawn --transpile-only ./src/server.ts", "start:prod": "node ./dist/server.js", "launch": "pnpm build && vercel --prod", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "<PERSON><PERSON>", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.717.0", "@aws-sdk/s3-request-presigner": "^3.721.0", "aws-sdk": "^2.1692.0", "axios": "^1.7.8", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "crypto": "^1.0.1", "date-fns": "^4.1.0", "dotenv": "^16.3.1", "express": "^4.18.2", "http-status": "^1.7.4", "ioredis": "^5.5.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.3", "multer": "^1.4.5-lts.1", "multer-s3": "^3.0.1", "slugify": "^1.6.6", "zod": "^3.22.4"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/jsonwebtoken": "^9.0.7", "@types/multer": "^1.4.12", "@types/multer-s3": "^3.0.3", "@typescript-eslint/eslint-plugin": "^6.13.2", "@typescript-eslint/parser": "^6.13.2", "eslint": "^8.55.0", "eslint-config-prettier": "^9.1.0", "prettier": "^3.1.0", "ts-node-dev": "^2.0.0", "typescript": "^5.3.3"}}