import cors from "cors";
import express, { Application, Request, Response } from "express";
import globalErrorHandler from "./app/middlewares/globalErrorHandler";
import notFound from "./app/middlewares/notFound";
import router from "./app/routes";
const app: Application = express();

// parsers
app.use(express.json());

app.use(express.urlencoded({ extended: true })); //

const whitelist = [
	"http://localhost:3000",
	"http://localhost:3001",
	"https://exchanger-dashboard.vercel.app",
	"https://exchanger-client.vercel.app"
];
app.use(
	cors({
		origin: whitelist,
		credentials: true
	})
);

app.use("/api", router);

app.get("/", (req: Request, res: Response) => {
	res.send({
		success: true,
		message: "The server is running"
	});
});

// global error handler middleware
app.use(globalErrorHandler);

// not found middleware
app.use(notFound);

export default app;
