/* eslint-disable @typescript-eslint/no-explicit-any */
import { FilterQuery, Query } from "mongoose";
import { Brand } from "../modules/Brand/brand.model";
import PrimaryCategory from "../modules/PrimaryCategory/primaryCategory.model";
import SecondaryCategory from "../modules/SecondaryCategory/secondaryCategory.model";
import TertiaryCategory from "../modules/TertiaryCategory/tertiaryCategory.model";
import isValidMongoId from "../utils/isValidMongoId";

interface QueryParams {
	search?: string;
	page?: number;
	limit?: number;
	sort?: string;
	sortOrder?: string;
	fields?: string;
	startDate?: string;
	endDate?: string;
	minPrice?: string;
	maxPrice?: string;
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	[key: string]: any;
}

class QueryBuilder<T> {
	public modelQuery: Query<T[], T>;
	private query: QueryParams;
	private defaultLimit = 30;

	constructor(modelQuery: Query<T[], T>, query: QueryParams) {
		this.modelQuery = modelQuery;
		this.query = query;
	}

	/**
	 * Adds a search functionality to the query by matching against provided fields.
	 * @param searchableFields - List of fields to search in.
	 */
	public search(searchableFields: string[]) {
		const search = this.query.search;

		if (search) {
			this.modelQuery = this.modelQuery.find({
				$or: searchableFields.map(
					(key) =>
						({
							[key]: {
								$regex: search,
								$options: "i"
							}
						}) as FilterQuery<T>
				)
			});
		}

		return this;
	}

	/**
	 * Filters the query based on query parameters excluding reserved fields.
	 * Translates category and sub_category slugs to IDs before filtering.
	 */
	public async filter() {
		const queryObj = { ...this.query };
		const excludeFields = ["search", "page", "limit", "sort", "sortOrder", "fields", "minPrice", "maxPrice"];
		excludeFields.forEach((el) => delete queryObj[el]);
		const filterableFields = Object.keys(queryObj).filter((key) => key.startsWith("field_"));

		if (filterableFields.length > 0) {
			const filterableFieldKeys = Object.keys(queryObj).filter((key) => key.startsWith("field_"));
			const filterConditions = [];
			for (const key of filterableFieldKeys) {
				const fieldSlug = key.replace("field_", "");
				const fieldValue = queryObj[key];
				// Handle comma-separated values as OR conditions
				if (typeof fieldValue === "string" && fieldValue.includes(",")) {
					const values = fieldValue.split(",");
					filterConditions.push({
						additionalInfo: {
							$elemMatch: {
								key: fieldSlug,
								value: { $in: values }
							}
						}
					});
				} else {
					if (fieldSlug === "minPrice") {
						filterConditions.push({
							price: { $gte: Number(fieldValue) }
						});
					} else if (fieldSlug === "maxPrice") {
						filterConditions.push({
							price: { $lte: Number(fieldValue) }
						});
					} else {
						filterConditions.push({
							additionalInfo: {
								$elemMatch: {
									key: fieldSlug,
									value: fieldValue
								}
							}
						});
					}
				}

				// Remove the field_ parameter from queryObj
				delete queryObj[key];
			}
			// Add the filter conditions to the query
			if (filterConditions.length > 0) {
				this.modelQuery = this.modelQuery.find({ $and: filterConditions });
			}
		}

		// Translate primary category slug to ID
		if (queryObj.primaryCategory && !isValidMongoId(queryObj.primaryCategory)) {
			const primaryCategory = await PrimaryCategory.findOne({
				slug: queryObj.primaryCategory
			});

			queryObj.primaryCategory = primaryCategory?._id || null;
		}

		// Translate secondary category slug to ID
		if (queryObj.secondaryCategory && !isValidMongoId(queryObj.secondaryCategory)) {
			const secondaryCategory = await SecondaryCategory.findOne({
				slug: queryObj.secondaryCategory
			});

			queryObj.secondaryCategory = secondaryCategory?._id || null;
		}

		// Translate Tertiary Category category slug to ID
		if (queryObj.tertiaryCategory && !isValidMongoId(queryObj.tertiaryCategory)) {
			const tertiaryCategory = await TertiaryCategory.findOne({
				slug: queryObj.tertiaryCategory
			});

			queryObj.tertiaryCategory = tertiaryCategory?._id || null;
		}

		// Translate brand slug to ID
		if (queryObj.brand) {
			const brand = await Brand.findOne({
				slug: queryObj.brand
			});

			queryObj.brand = brand?._id || null;
		}

		// Translate date range to createdAt
		if (queryObj.startDate && queryObj.endDate) {
			queryObj.createdAt = {
				$gte: queryObj.startDate,
				$lte: queryObj.endDate
			};
			delete queryObj.startDate;
			delete queryObj.endDate;
		}

		// Handle price range filtering
		if (this.query.field_minPrice || this.query.field_maxPrice) {
			queryObj.price = {};
			if (this.query.field_minPrice) {
				queryObj.price.$gte = Number(this.query.field_minPrice);
			}
			if (this.query.field_maxPrice) {
				queryObj.price.$lte = Number(this.query.field_maxPrice);
			}
		}

		// Handle multiple _ids filtering
		if (queryObj._ids) {
			const ids = queryObj._ids.split(",").filter(isValidMongoId);
			if (ids.length > 0) {
				queryObj._id = { $in: ids };
			}
			delete queryObj._ids; // remove to avoid confusion later
		}

		// Apply the rest of the filters
		this.modelQuery = this.modelQuery.find(queryObj as FilterQuery<T>);

		return this;
	}

	/**
	 * Sorts the query results based on the sort parameter or defaults to '-createdAt'.
	 */

	public sort() {
		const sort = this.query.sort ? this.query.sort.split(",").join(" ") : "-createdAt";
		const sortOrder = this.query.sortOrder ?? "asc";

		this.modelQuery = this.modelQuery.sort({
			[sort]: sortOrder === "asc" ? 1 : -1
		});

		return this;
	}

	/**
	 * Limits the fields returned in the query results.
	 */

	public fields() {
		const fields = this.query.fields ? this.query.fields.split(",").join(" ") : "-__v";
		this.modelQuery = this.modelQuery.select(fields);

		return this;
	}

	/**
	 * Paginates the query results based on page and limit parameters.
	 */
	public paginate() {
		const limit = this.query.limit ? Number(this.query.limit) : this.defaultLimit;
		const page = this.query.page ? Number(this.query.page) : 1;
		const skip = (page - 1) * limit;

		this.modelQuery = this.modelQuery.skip(skip).limit(limit);

		return this;
	}

	/**
	 * Counts the total number of documents matching the query.
	 */
	public async countTotal() {
		const limit = this.query.limit ? Number(this.query.limit) : this.defaultLimit;
		const page = this.query.page ? Number(this.query.page) : 1;
		const filter = this.modelQuery.getFilter();
		const totalItems = await this.modelQuery.model.countDocuments(filter);
		const totalPages = Math.ceil(totalItems / limit);

		return {
			page,
			limit,
			totalItems,
			totalPages
		};
	}

	/**
	 * Executes the query and returns the results.
	 */
	public async exec() {
		return await this.modelQuery.exec();
	}

	/**
	 * Resets the query builder state, useful for reusing the same builder instance.
	 */
	public reset(modelQuery: Query<T[], T>, query: QueryParams) {
		this.modelQuery = modelQuery;
		this.query = query;
		return this;
	}
}

export default QueryBuilder;
