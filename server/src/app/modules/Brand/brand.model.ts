import { model, Schema } from "mongoose";

import { deleteFile, updateImageUrl } from "../../utils/awsFileManager";
import { CreateSlug } from "../../utils/CreateSlug";
import { IBrand } from "./brand.type";

const brandSchema = new Schema<IBrand>(
	{
		name: { type: String, required: true },
		image: { type: String },
		slug: { type: String, unique: true }
	},
	{ timestamps: true }
);

brandSchema.pre("save", function (next) {
	this.slug = CreateSlug(this.name);
	next();
});

brandSchema.post("find", async function (result) {
	await updateImageUrl(result);
});

brandSchema.pre("findOneAndUpdate", async function (next) {
	const update = this.getUpdate() as Partial<IBrand>;
	if (update.name) {
		update.slug = CreateSlug(update.name);
		this.setUpdate(update);
	}

	// delete old image from s3 if new image is uploaded
	if (update.image) {
		const data = await this.model.findOne(this.getFilter());
		await deleteFile(data.image);
	}

	next();
});

brandSchema.post("findOneAndDelete", async function (res) {
	if (res?.image) {
		await deleteFile(res?.image);
	}
});

export const Brand = model<IBrand>("Brand", brandSchema);
