import httpStatus from "http-status";
import QueryBuilder from "../../builder/QueryBuilder";
import { imageFolders } from "../../constant";
import RedisClient from "../../DB/redisClient";
import AppError from "../../errors/AppError";
import { IFile } from "../../interface";
import { createFileId } from "../../utils";
import { uploadFile } from "../../utils/awsFileManager";
import Product from "../Product/product.model";
import { Brand } from "./brand.model";
import { IBrand } from "./brand.type";

const createBrand = async (brand: IBrand, file: IFile) => {
	if (!file) {
		throw new AppError(httpStatus.BAD_REQUEST, "Image is required!");
	}

	const image = createFileId(imageFolders.BRANDS, brand.name);

	const uploadedImage = await uploadFile(file, image);

	if (!uploadedImage) {
		throw new AppError(httpStatus.INTERNAL_SERVER_ERROR, "Error uploading image....");
	}
	const res = await Brand.create({
		...brand,
		image
	});
	return res;
};

const getBrands = async (query: Record<string, unknown>) => {
	const productBrandIds = await Product.distinct("brand");
	const brandsQuery = (
		await new QueryBuilder(Brand.find({ _id: { $in: productBrandIds } }), query).search(["name"]).filter()
	)
		.fields()
		.paginate()
		.sort();
	const result = await brandsQuery.modelQuery;
	const meta = await brandsQuery.countTotal();
	return { result, meta };
};

const getBrand = async (id: string) => {
	// check if brand is cached
	const cachedBrand = await RedisClient.get(`brand:${id}`);
	if (cachedBrand) {
		return JSON.parse(cachedBrand);
	}

	const brand = await Brand.findById(id);
	if (!brand) {
		throw new AppError(httpStatus.NOT_FOUND, "Brand not found");
	}

	// cache brand
	await RedisClient.set(`brand:${id}`, JSON.stringify(brand));

	return brand;
};

const getBrandBySlug = async (slug: string) => {
	// check if brand is cached
	const cachedBrand = await RedisClient.get(`brand:${slug}`);
	if (cachedBrand) {
		return JSON.parse(cachedBrand);
	}

	const brand = await Brand.findOne({ slug });
	if (!brand) {
		throw new AppError(httpStatus.NOT_FOUND, "Brand not found");
	}

	// cache brand
	await RedisClient.set(`brand:${slug}`, JSON.stringify(brand));
	return brand;
};

const updateBrand = async (id: string, brand: IBrand) => {
	// check if brand exists
	await getBrand(id);

	const updatedBrand = await Brand.findByIdAndUpdate(id, brand, { new: true });

	// update cache
	await RedisClient.set(`brand:${id}`, JSON.stringify(updatedBrand));
	return updatedBrand;
};

const updateBrandImage = async (id: string, file: IFile) => {
	//	check if category exists
	const brand = await getBrand(id);

	if (!file) {
		throw new AppError(httpStatus.BAD_REQUEST, "Image is required!");
	}

	// const image = `brands/${brand.name.replace(/\s/g, "")}-${Date.now()}`;
	const image = createFileId(imageFolders.BRANDS, brand.name);

	const uploadedImage = await uploadFile(file, image);
	if (!uploadedImage) {
		throw new AppError(httpStatus.INTERNAL_SERVER_ERROR, "Error uploading image!");
	}

	const updatedBrand = await Brand.findByIdAndUpdate(id, { image }, { new: true });

	// update cache
	await RedisClient.set(`brand:${id}`, JSON.stringify(updatedBrand));

	return updatedBrand;
};

const deleteBrand = async (id: string) => {
	// check if brand exists
	const brand = await getBrand(id);

	// check if brand has products
	const products = await Product.find({ brand: id });
	if (products.length > 0) {
		throw new AppError(httpStatus.BAD_REQUEST, "Brand has products, cannot delete!");
	}

	const deletedBrand = await Brand.findByIdAndDelete(id);

	// delete cache
	await RedisClient.del(`brand:${id}`);
	await RedisClient.del(`brand:${brand.slug}`);

	return deletedBrand;
};

const BrandServices = {
	createBrand,
	getBrands,
	getBrand,
	getBrandBySlug,
	updateBrand,
	updateBrandImage,
	deleteBrand
};

export default BrandServices;
