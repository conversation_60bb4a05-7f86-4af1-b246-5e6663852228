import { endOfDay, startOfDay } from "date-fns";
import httpStatus from "http-status";
import { PipelineStage } from "mongoose";
import config from "../../config";
import AppError from "../../errors/AppError";
import { createToken } from "../../utils/JwtHelper";
import Order from "../Order/order.model";
import Product from "../Product/product.model";
import { Admin } from "./admin.model";

const loginAdmin = async (payload: { email: string; password: string }) => {
	const { email, password } = payload;

	// check if the admin is exist
	const admin = await Admin.findOne({ email }).select("+password");

	if (!admin) {
		throw new AppError(httpStatus.NOT_FOUND, "You have no access to the system! Please contact to the authority.");
	}

	const isPasswordMatch = await Admin.isPasswordMatched(password, admin.password);

	if (!isPasswordMatch) {
		throw new AppError(httpStatus.FORBIDDEN, "Wrong password! Please try again.");
	}

	const jwtPayload = {
		id: admin?._id.toString(),
		email: admin?.email,
		role: admin?.role
	};

	const accessToken = createToken(jwtPayload, config.jwt_secret, config.jwt_expiration);
	const adminWithoutPassword = await Admin.findById(admin._id);

	return { accessToken, admin: adminWithoutPassword };
};

// const getDashboardOverviewFromDB = async (query: Record<string, unknown>) => {
// 	const { start, end } = query;

// 	let startDate: Date;
// 	let endDate: Date;

// 	if (start && end) {
// 		startDate = new Date(start as string);
// 		endDate = new Date(end as string);
// 	} else {
// 		endDate = new Date();
// 		startDate = new Date();
// 		startDate.setDate(endDate.getDate() - 14); // Last 15 days including today
// 	}

// 	// --- Revenue & Orders ---
// 	const orders = await Order.find({
// 		orderDate: { $gte: startDate, $lte: endDate },
// 		status: { $in: ["shipped", "delivered"] }
// 	});

// 	const totalRevenue = orders.reduce((acc, order) => acc + order.totalAmount, 0);
// 	const totalOrders = orders.length;
// 	const averageOrderValue = totalOrders ? totalRevenue / totalOrders : 0;

// 	// --- Revenue Trend by Day ---
// 	const revenueTrend = await Order.aggregate([
// 		{
// 			$match: {
// 				orderDate: { $gte: startDate, $lte: endDate },
// 				status: { $in: ["shipped", "delivered"] }
// 			}
// 		},
// 		{
// 			$group: {
// 				_id: {
// 					$dateToString: { format: "%Y-%m-%d", date: "$orderDate" }
// 				},
// 				revenue: { $sum: "$totalAmount" }
// 			}
// 		},
// 		{ $sort: { _id: 1 } }
// 	]);

// 	// --- Top-Selling Products ---
// 	const topProducts = await Order.aggregate([
// 		{ $unwind: "$items" },
// 		{
// 			$group: {
// 				_id: "$items.productId",
// 				unitsSold: { $sum: "$items.quantity" },
// 				revenue: { $sum: "$items.price" }
// 			}
// 		},
// 		{
// 			$lookup: {
// 				from: "products",
// 				localField: "_id",
// 				foreignField: "_id",
// 				as: "product"
// 			}
// 		},
// 		{ $unwind: "$product" },
// 		{ $sort: { unitsSold: -1 } },
// 		{ $limit: 5 },
// 		{
// 			$project: {
// 				_id: 0,
// 				name: "$product.name",
// 				unitsSold: 1,
// 				revenue: 1
// 			}
// 		}
// 	]);

// 	// --- Low Stock Products ---
// 	const lowStockProducts = await Product.find({
// 		isActive: true,
// 		$or: [{ stock: { $lt: 5 } }, { variants: { $elemMatch: { stock: { $lt: 5 } } } }]
// 	})
// 		.select("name stock variants")
// 		.limit(10);

// 	// --- Inventory Value ---
// 	const products = await Product.find({ isActive: true }).select("price stock variants");

// 	let inventoryValue = 0;
// 	for (const product of products) {
// 		if (product.variants.length) {
// 			inventoryValue += product.variants.reduce((sum, v) => sum + v.price * v.stock, 0);
// 		} else {
// 			inventoryValue += (product.stock || 0) * product.price;
// 		}
// 	}

// 	return {
// 		sales: {
// 			totalRevenue,
// 			totalOrders,
// 			averageOrderValue,
// 			revenueTrend
// 		},
// 		topSellingProducts: topProducts,
// 		inventory: {
// 			inventoryValue,
// 			lowStockProducts
// 		}
// 	};
// };

const getDateFilter = (query: Record<string, unknown>) => {
	const { fromDate, toDate } = query;
	if (fromDate && toDate) {
		return {
			orderDate: {
				$gte: startOfDay(new Date(fromDate as string)),
				$lte: endOfDay(new Date(toDate as string))
			}
		};
	} else if (fromDate) {
		return {
			orderDate: { $gte: startOfDay(new Date(fromDate as string)) }
		};
	} else if (toDate) {
		return {
			orderDate: { $lte: endOfDay(new Date(toDate as string)) }
		};
	}
	return {};
};

const getDashboardOverviewFromDB = async (query: Record<string, unknown>) => {
	const filter: Record<string, unknown> = {};

	// Date filtering for orderDate
	if (query.fromDate && query.toDate) {
		filter.orderDate = {
			$gte: new Date(query.fromDate as string),
			$lte: new Date(query.toDate as string)
		};
	} else if (query.fromDate) {
		filter.orderDate = { $gte: new Date(query.fromDate as string) };
	} else if (query.toDate) {
		filter.orderDate = { $lte: new Date(query.toDate as string) };
	}

	// Total orders count in date range
	const totalOrders = await Order.countDocuments(filter);

	// Count pending orders
	const pendingOrders = await Order.countDocuments({ ...filter, status: "pending" });

	// Count delivered orders
	const deliveredOrders = await Order.countDocuments({ ...filter, status: "delivered" });

	// Count cancelled orders
	const cancelledOrders = await Order.countDocuments({ ...filter, status: "cancelled" });

	// Total revenue: sum of totalAmount of orders in date range
	const revenueAgg = await Order.aggregate([
		{ $match: filter },
		{
			$group: {
				_id: null,
				totalRevenue: { $sum: "$totalAmount" }
			}
		}
	]);

	const totalRevenue = revenueAgg.length > 0 ? revenueAgg[0].totalRevenue : 0;

	// Total number of products in inventory (active products)
	const totalProducts = await Product.countDocuments({ isActive: true });

	// Top selling products by quantity sold (in date range)
	const topSellingProductsAgg: PipelineStage[] = [
		{ $match: filter },
		{ $unwind: "$items" },
		{
			$group: {
				_id: "$items.productId",
				totalSold: { $sum: "$items.quantity" }
			}
		},
		{
			$lookup: {
				from: "products",
				localField: "_id",
				foreignField: "_id",
				as: "product"
			}
		},
		{ $unwind: "$product" },
		{
			$project: {
				_id: 1,
				totalSold: 1,
				name: "$product.name",
				slug: "$product.slug",
				images: "$product.images"
			}
		},
		{ $sort: { totalSold: -1 } },
		{ $limit: 5 }
	];

	const topSellingProducts = await Order.aggregate(topSellingProductsAgg);

	return {
		totalOrders,
		pendingOrders,
		deliveredOrders,
		cancelledOrders,
		totalRevenue,
		totalProducts,
		topSellingProducts
	};
};

export const getDashboardSalesFromDB = async (query: Record<string, unknown>) => {
	const dateFilter = getDateFilter(query);

	const [dailySales, ordersByStatus, revenueByStatus] = await Promise.all([
		// Daily sales chart
		Order.aggregate([
			{ $match: dateFilter },
			{
				$group: {
					_id: { $dateToString: { format: "%Y-%m-%d", date: "$orderDate" } },
					total: { $sum: "$totalAmount" },
					count: { $sum: 1 }
				}
			},
			{ $sort: { _id: 1 } }
		]),

		// Total revenue
		Order.aggregate([{ $match: dateFilter }, { $group: { _id: null, total: { $sum: "$totalAmount" } } }]),

		// Order count by status (for a bar or pie chart)
		Order.aggregate([
			{ $match: dateFilter },
			{
				$group: {
					_id: "$status", // status: pending, delivered, etc.
					count: { $sum: 1 }
				}
			}
		]),

		// Revenue grouped by order status
		Order.aggregate([
			{ $match: dateFilter },
			{
				$group: {
					_id: "$status",
					revenue: { $sum: "$totalAmount" }
				}
			}
		])
	]);
	const filter: Record<string, unknown> = {};

	// Date filtering for orderDate
	if (query.fromDate && query.toDate) {
		filter.orderDate = {
			$gte: new Date(query.fromDate as string),
			$lte: new Date(query.toDate as string)
		};
	} else if (query.fromDate) {
		filter.orderDate = { $gte: new Date(query.fromDate as string) };
	} else if (query.toDate) {
		filter.orderDate = { $lte: new Date(query.toDate as string) };
	}
	// Total orders count in date range
	const totalOrders = await Order.countDocuments(filter);

	// Count pending orders
	const pendingOrders = await Order.countDocuments({ ...filter, status: "pending" });

	// Count delivered orders
	const deliveredOrders = await Order.countDocuments({ ...filter, status: "delivered" });

	// Count cancelled orders
	const cancelledOrders = await Order.countDocuments({ ...filter, status: "cancelled" });

	// Count shipped orders
	const shippedOrders = await Order.countDocuments({ ...filter, status: "shipped" });

	return {
		overview: {
			totalOrders,
			pendingOrders,
			deliveredOrders,
			cancelledOrders,
			shippedOrders
		},
		dailySales, // for line chart
		ordersByStatus, // for pie/bar chart
		revenueByStatus // optional second chart or card display
	};
};

export const getDashboardInventoryFromDB = async (query: Record<string, unknown>) => {
	const filter: Record<string, unknown> = {};

	// Date filtering for orderDate
	if (query.fromDate && query.toDate) {
		filter.orderDate = {
			$gte: new Date(query.fromDate as string),
			$lte: new Date(query.toDate as string)
		};
	} else if (query.fromDate) {
		filter.orderDate = { $gte: new Date(query.fromDate as string) };
	} else if (query.toDate) {
		filter.orderDate = { $lte: new Date(query.toDate as string) };
	}
	const lowStockThreshold = 5;

	const [totalProducts, outOfStock, lowStock] = await Promise.all([
		Product.countDocuments({ ...filter }),
		Product.countDocuments({ ...filter, stock: 0 }),
		Product.countDocuments({ ...filter, stock: { $gt: 0, $lte: lowStockThreshold } })
	]);

	return {
		overview: { totalProducts, outOfStock, lowStock }
	};
};

// export const getDashboardData = async (req: Request, res: Response) => {
// 	try {
// 		const { tab, fromDate, toDate } = req.query;

// 		const dateFilter: any = {};
// 		if (fromDate || toDate) {
// 			dateFilter.orderDate = {};
// 			if (fromDate) dateFilter.orderDate.$gte = new Date(fromDate as string);
// 			if (toDate) dateFilter.orderDate.$lte = new Date(toDate as string);
// 		}

// 		let data: any = {};

// 		switch (tab) {
// 			case "overview":
// 				const totalOrders = await Order.countDocuments(dateFilter);
// 				const totalRevenue = await Order.aggregate([
// 					{ $match: dateFilter },
// 					{ $group: { _id: null, total: { $sum: "$totalAmount" } } }
// 				]);
// 				data = {
// 					totalOrders,
// 					totalRevenue: totalRevenue[0]?.total || 0
// 				};
// 				break;

// 			case "inventory":
// 				const allProducts = await Product.find({}, "name slug stock images");
// 				const lowStockProducts = allProducts.filter((p) => p.stock !== undefined && p.stock <= 5);
// 				data = {
// 					totalProducts: allProducts.length,
// 					lowStockCount: lowStockProducts.length,
// 					lowStockProducts
// 				};
// 				break;

// 			case "sales":
// 				const sales = await Order.aggregate([
// 					{ $match: dateFilter },
// 					{ $unwind: "$items" },
// 					{
// 						$group: {
// 							_id: "$items.productId",
// 							totalSold: { $sum: "$items.quantity" },
// 							totalRevenue: { $sum: { $multiply: ["$items.quantity", "$items.price"] } }
// 						}
// 					},
// 					{
// 						$lookup: {
// 							from: "products",
// 							localField: "_id",
// 							foreignField: "_id",
// 							as: "product"
// 						}
// 					},
// 					{ $unwind: "$product" },
// 					{
// 						$project: {
// 							name: "$product.name",
// 							slug: "$product.slug",
// 							images: "$product.images",
// 							totalSold: 1,
// 							totalRevenue: 1
// 						}
// 					},
// 					{ $sort: { totalSold: -1 } },
// 					{ $limit: 10 }
// 				]);
// 				data = { topSelling: sales };
// 				break;

// 			default:
// 				return res.status(400).json({ message: "Invalid tab" });
// 		}

// 		res.json({ tab, data, meta: { fromDate, toDate, generatedAt: new Date() } });
// 	} catch (err) {
// 		console.error(err);
// 		res.status(500).json({ message: "Internal server error" });
// 	}
// };

const AdminServices = {
	loginAdmin,
	getDashboardOverviewFromDB,
	getDashboardSalesFromDB,
	getDashboardInventoryFromDB
};

export default AdminServices;
