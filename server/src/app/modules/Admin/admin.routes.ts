import express from "express";
import validateRequest from "../../middlewares/validateRequest";
import AdminControllers from "./admin.controllers";
import AdminValidations from "./Admin.validations";

const router = express.Router();

router.post("/login", validateRequest(AdminValidations.LoginValidation), AdminControllers.loginAdmin);
router.get("/dashboard/overview", AdminControllers.getDashboardOverview);
router.get("/dashboard/sales", AdminControllers.getDashboardSales);
router.get("/dashboard/inventory", AdminControllers.getDashboardInventory);

const AdminRoutes = router;
export default AdminRoutes;
