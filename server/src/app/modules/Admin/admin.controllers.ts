import { Request, Response } from "express";
import httpStatus from "http-status";
import catchAsync from "../../utils/catchAsync";
import sendResponse from "../../utils/sendResponse";
import AdminServices from "./admin.services";

const loginAdmin = catchAsync(async (req: Request, res: Response) => {
	const payload = req.body;
	const { accessToken, admin } = await AdminServices.loginAdmin(payload);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		success: true,
		message: "Admin logged in successfully!",
		data: { accessToken, admin }
	});
});

const getDashboardOverview = catchAsync(async (req: Request, res: Response) => {
	const result = await AdminServices.getDashboardOverviewFromDB(req.query);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		success: true,
		message: "Dashboard overview retrieved successfully!",
		data: result
	});
});

const getDashboardSales = catchAsync(async (req: Request, res: Response) => {
	const result = await AdminServices.getDashboardSalesFromDB(req.query);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		success: true,
		message: "Dashboard sales retrieved successfully!",
		data: result
	});
});

const getDashboardInventory = catchAsync(async (req: Request, res: Response) => {
	const result = await AdminServices.getDashboardInventoryFromDB(req.query);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		success: true,
		message: "Dashboard inventory retrieved successfully!",
		data: result
	});
});

const AdminControllers = {
	loginAdmin,
	getDashboardOverview,
	getDashboardSales,
	getDashboardInventory
};

export default AdminControllers;
