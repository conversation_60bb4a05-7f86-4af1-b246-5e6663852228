import httpStatus from "http-status";
import QueryBuilder from "../../builder/QueryBuilder";
import { imageFolders } from "../../constant";
import AppError from "../../errors/AppError";
import { IFile } from "../../interface";
import { createFileId } from "../../utils";
import { uploadFile } from "../../utils/awsFileManager";
import TertiaryCategory from "./tertiaryCategory.model";
import { ITertiaryCategory } from "./tertiaryCategory.types";

const createTertiaryCategory = async (tertiaryCategory: ITertiaryCategory, file: IFile) => {
	if (!file) {
		throw new AppError(httpStatus.BAD_REQUEST, "Image is required!");
	}

	const image = createFileId(imageFolders.CATEGORIES, tertiaryCategory.name);

	const uploadedImage = await uploadFile(file, image);
	if (!uploadedImage) {
		throw new AppError(httpStatus.INTERNAL_SERVER_ERROR, "Error uploading image");
	}

	const newCategory = await TertiaryCategory.create({ ...tertiaryCategory, image });
	return newCategory;
};

const getTertiaryCategory = async (id: string) => {
	const res = await TertiaryCategory.findById(id).populate("productCount");

	if (!res) {
		throw new AppError(httpStatus.NOT_FOUND, "Tertiary category not found");
	}
	return res;
};

const getTertiaryCategoryBySlug = async (slug: string) => {
	const res = await TertiaryCategory.findOne({ slug }).populate("productCount");

	if (!res) {
		throw new AppError(httpStatus.NOT_FOUND, "Tertiary category not found");
	}

	return res;
};

const getTertiaryCategories = async (query: Record<string, unknown>) => {
	const categoryQuery = (
		await new QueryBuilder(TertiaryCategory.find().populate("productCount"), query).search(["name"]).filter()
	)
		.fields()
		.paginate()
		.sort();

	const categories = await categoryQuery.modelQuery;
	const meta = await categoryQuery.countTotal();

	return { categories, meta };
};

const updateTertiaryCategory = async (id: string, tertiaryCategory: ITertiaryCategory) => {
	const res = await TertiaryCategory.findByIdAndUpdate(id, tertiaryCategory, { new: true });
	if (!res) {
		throw new AppError(httpStatus.NOT_FOUND, "Tertiary category not found!");
	}
	return res;
};

const updateTertiaryCategoryImage = async (id: string, file: IFile) => {
	const category = await getTertiaryCategory(id);

	if (!file) {
		throw new AppError(httpStatus.BAD_REQUEST, "Image is required!");
	}

	const image = createFileId(imageFolders.CATEGORIES, category.name);

	const uploadedImage = await uploadFile(file, image);
	if (!uploadedImage) {
		throw new AppError(httpStatus.INTERNAL_SERVER_ERROR, "Error uploading image");
	}

	const res = await TertiaryCategory.findByIdAndUpdate(id, { image }, { new: true });
	return res;
};

const deleteTertiaryCategory = async (id: string) => {
	const res = await TertiaryCategory.findByIdAndDelete(id);
	return res;
};

const toggleFeatured = async (id: string) => {
	const tertiaryCategory = await TertiaryCategory.findById(id);
	if (!tertiaryCategory) return null;

	tertiaryCategory.isFeatured = !tertiaryCategory.isFeatured;
	const res = await tertiaryCategory.save();
	return res;
};

const TertiaryCategoryServices = {
	createTertiaryCategory,
	getTertiaryCategory,
	getTertiaryCategoryBySlug,
	getTertiaryCategories,
	updateTertiaryCategory,
	updateTertiaryCategoryImage,
	deleteTertiaryCategory,
	toggleFeatured
};

export default TertiaryCategoryServices;
