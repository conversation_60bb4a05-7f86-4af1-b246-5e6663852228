import express from "express";
import { userRoles } from "../../constant";
import AuthGuard from "../../middlewares/AuthGuard";
import validateRequest from "../../middlewares/validateRequest";
import { dataParser } from "../../utils";
import upload from "../../utils/awsFileManager";
import PrimaryCategoryControllers from "./primaryCategory.controllers";
import PrimaryCategoryValidations from "./primaryCategory.validation";

const router = express.Router();

router.post(
	"/create",
	AuthGuard(userRoles.ADMIN),
	upload.single("image"),
	dataParser(),
	validateRequest(PrimaryCategoryValidations.PrimaryCategoryCreateValidation),
	PrimaryCategoryControllers.createPrimaryCategory
);

router.get("/", PrimaryCategoryControllers.getPrimaryCategories);

router.get("/all", PrimaryCategoryControllers.getAllCategories);

router.get("/:id", PrimaryCategoryControllers.getPrimaryCategory);

router.get("/slug/:slug", PrimaryCategoryControllers.getPrimaryCategoryBySlug);

router.put(
	"/:id",
	AuthGuard(userRoles.ADMIN),
	validateRequest(PrimaryCategoryValidations.PrimaryCategoryUpdateValidation),
	PrimaryCategoryControllers.updatePrimaryCategory
);

router.patch(
	"/update-image/:id",
	AuthGuard(userRoles.ADMIN),
	upload.single("image"),
	PrimaryCategoryControllers.updatePrimaryCategoryImage
);

router.patch("/toggle-featured/:id", AuthGuard(userRoles.ADMIN), PrimaryCategoryControllers.toggleFeatured);

router.delete("/:id", AuthGuard(userRoles.ADMIN), PrimaryCategoryControllers.deletePrimaryCategory);

const PrimaryCategoryRoutes = router;

export default PrimaryCategoryRoutes;
