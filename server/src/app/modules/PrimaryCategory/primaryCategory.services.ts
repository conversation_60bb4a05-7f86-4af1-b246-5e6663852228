import httpStatus from "http-status";
import mongoose from "mongoose";
import QueryBuilder from "../../builder/QueryBuilder";
import { imageFolders } from "../../constant";
import RedisClient from "../../DB/redisClient";
import AppError from "../../errors/AppError";
import { IFile } from "../../interface";
import { createFileId } from "../../utils";
import { deleteFile, uploadFile } from "../../utils/awsFileManager";
import SecondaryCategory from "../SecondaryCategory/secondaryCategory.model";
import TertiaryCategory from "../TertiaryCategory/tertiaryCategory.model";
import PrimaryCategory from "./primaryCategory.model";
import { IPrimaryCategory } from "./primaryCategory.types";

const removeAllCategoryCaching = async () => {
	await RedisClient.del("allCategories");
};

const createPrimaryCategory = async (category: IPrimaryCategory, file: IFile) => {
	if (!file) {
		throw new AppError(httpStatus.BAD_REQUEST, "Image is required!");
	}

	const image = createFileId(imageFolders.CATEGORIES, category.name);

	const uploadedImage = await uploadFile(file, image);
	if (!uploadedImage) {
		throw new AppError(httpStatus.INTERNAL_SERVER_ERROR, "Error uploading image");
	}

	const newCategory = await PrimaryCategory.create({ ...category, image });

	removeAllCategoryCaching();

	return newCategory;
};

const getPrimaryCategories = async (query: Record<string, unknown>) => {
	const categoryQuery = (
		await new QueryBuilder(PrimaryCategory.find().populate("secondaryCategoryCount productCount"), query)
			.search(["name"])
			.filter()
	)
		.fields()
		.paginate()
		.sort();

	const categories = await categoryQuery.modelQuery;
	const meta = await categoryQuery.countTotal();

	return { categories, meta };
};

const getAllCategories = async () => {
	// 🔹 Check cache first
	const cachedCategories = await RedisClient.get("allCategories");
	if (cachedCategories) {
		return JSON.parse(cachedCategories);
	}

	try {
		const categories = await PrimaryCategory.aggregate([
			// Sort primary categories
			{ $sort: { createdAt: -1 } },

			// Lookup secondary categories
			{
				$lookup: {
					from: "secondarycategories",
					localField: "_id",
					foreignField: "primaryCategory",
					as: "secondaryCategories"
				}
			},

			// Lookup tertiary categories directly inside secondary
			{
				$unwind: {
					path: "$secondaryCategories",
					preserveNullAndEmptyArrays: true
				}
			},

			{
				$lookup: {
					from: "tertiarycategories",
					localField: "secondaryCategories._id",
					foreignField: "secondaryCategory",
					as: "secondaryCategories.tertiaryCategories"
				}
			},

			// Group back while filtering out null secondaryCategories
			{
				$group: {
					_id: "$_id",
					name: { $first: "$name" },
					slug: { $first: "$slug" },
					secondaryCategories: {
						$push: {
							$cond: [
								{ $gt: ["$secondaryCategories._id", null] },
								{
									_id: "$secondaryCategories._id",
									name: "$secondaryCategories.name",
									slug: "$secondaryCategories.slug",
									tertiaryCategories: {
										$map: {
											input: "$secondaryCategories.tertiaryCategories",
											as: "tertiary",
											in: {
												_id: "$$tertiary._id",
												name: "$$tertiary.name",
												slug: "$$tertiary.slug"
											}
										}
									}
								},
								// Else skip nulls
								null
							]
						}
					}
				}
			},

			// 🔥 Filter out nulls from secondaryCategories array
			{
				$project: {
					name: 1,
					slug: 1,
					secondaryCategories: {
						$filter: {
							input: "$secondaryCategories",
							as: "sec",
							cond: { $ne: ["$$sec", null] }
						}
					}
				}
			}
		]);

		// ✅ Save to Redis
		await RedisClient.set("allCategories", JSON.stringify(categories), "EX", 6 * 60 * 60);
		return categories;
	} catch (error) {
		throw new Error("Failed to fetch nested categories");
	}
};

const getPrimaryCategory = async (id: string) => {
	const category = await PrimaryCategory.findById(id).populate("secondaryCategoryCount productCount");
	if (!category) {
		throw new AppError(httpStatus.NOT_FOUND, "Category not found");
	}
	return category;
};

const getPrimaryCategoryBySlug = async (slug: string) => {
	const category = await PrimaryCategory.findOne({ slug }).populate("secondaryCategoryCount productCount");
	if (!category) {
		throw new AppError(httpStatus.NOT_FOUND, "Category not found");
	}
	return category;
};

const updatePrimaryCategory = async (id: string, category: IPrimaryCategory) => {
	// check if category exists
	await getPrimaryCategory(id);

	const updatedCategory = await PrimaryCategory.findByIdAndUpdate(id, category, { new: true });
	removeAllCategoryCaching();
	return updatedCategory;
};

const updatePrimaryCategoryImage = async (id: string, file: IFile) => {
	// check if category exists
	const category = await getPrimaryCategory(id);

	if (!file) {
		throw new AppError(httpStatus.BAD_REQUEST, "Image is required!");
	}

	const imageKey = createFileId(imageFolders.CATEGORIES, category.name);

	const uploadedImage = await uploadFile(file, imageKey);
	if (!uploadedImage) {
		throw new AppError(httpStatus.INTERNAL_SERVER_ERROR, "Error uploading image!");
	}

	// delete old image from s3
	await deleteFile(category.image);
	removeAllCategoryCaching();

	const res = await PrimaryCategory.findByIdAndUpdate(id, { image: imageKey }, { new: true });
	return res;
};

const deletePrimaryCategory = async (id: string) => {
	// check if category exists
	await getPrimaryCategory(id);

	const session = await mongoose.startSession();

	try {
		session.startTransaction();

		// Find and delete related tertiary categories
		const tertiaryCategories = await TertiaryCategory.find({ primaryCategory: id }).session(session);
		for (const category of tertiaryCategories) {
			if (category.image) {
				await deleteFile(category.image); // Delete associated AWS images
			}
		}
		await TertiaryCategory.deleteMany({ primaryCategory: id }, { session });

		// Find and delete related secondary categories
		const secondaryCategories = await SecondaryCategory.find({ primaryCategory: id }).session(session);
		for (const category of secondaryCategories) {
			if (category.image) {
				await deleteFile(category.image); // Delete associated AWS images
			}
		}
		await SecondaryCategory.deleteMany({ primaryCategory: id }, { session });

		const deletedCategory = await PrimaryCategory.findOneAndDelete({ _id: id }, { session });

		await session.commitTransaction();
		await session.endSession();

		removeAllCategoryCaching();

		return deletedCategory;
	} catch (error) {
		await session.abortTransaction();
		await session.endSession();
		throw new AppError(httpStatus.INTERNAL_SERVER_ERROR, "Error deleting category");
	}
};

const toggleFeatured = async (id: string) => {
	const category = await getPrimaryCategory(id);

	category.isFeatured = !category.isFeatured;
	await category.save();
	removeAllCategoryCaching();
	return category;
};

const PrimaryCategoryServices = {
	createPrimaryCategory,
	getPrimaryCategories,
	getPrimaryCategory,
	getPrimaryCategoryBySlug,
	updatePrimaryCategory,
	updatePrimaryCategoryImage,
	deletePrimaryCategory,
	toggleFeatured,
	getAllCategories
};

export default PrimaryCategoryServices;
