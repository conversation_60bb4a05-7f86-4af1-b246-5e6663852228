import { Request, RequestHandler, Response } from "express";
import httpStatus from "http-status";
import { IFile } from "../../interface";
import catchAsync from "../../utils/catchAsync";
import sendResponse from "../../utils/sendResponse";
import PrimaryCategoryServices from "./primaryCategory.services";

const createPrimaryCategory: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const image = req.file;
	const category = await PrimaryCategoryServices.createPrimaryCategory(req.body, image as IFile);

	sendResponse(res, {
		statusCode: httpStatus.CREATED,
		success: true,
		message: "Category created successfully!",
		data: category
	});
});

const getPrimaryCategories: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const { categories, meta } = await PrimaryCategoryServices.getPrimaryCategories(req.query);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		success: true,
		message: "Primary categories fetched successfully!",
		meta,
		data: categories
	});
});

const getAllCategories: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const categories = await PrimaryCategoryServices.getAllCategories();

	sendResponse(res, {
		statusCode: httpStatus.OK,
		success: true,
		message: "All categories fetched successfully!",
		data: categories
	});
});

const getPrimaryCategory: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const category = await PrimaryCategoryServices.getPrimaryCategory(req.params.id);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		message: "Category fetched successfully!",
		success: true,
		data: category
	});
});

const getPrimaryCategoryBySlug: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const category = await PrimaryCategoryServices.getPrimaryCategoryBySlug(req.params.slug);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		message: "Category by slug fetched successfully!",
		success: true,
		data: category
	});
});

const updatePrimaryCategory: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const category = await PrimaryCategoryServices.updatePrimaryCategory(req.params.id, req.body);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		success: true,
		message: "Category updated successfully!",
		data: category
	});
});

const updatePrimaryCategoryImage: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const image = req.file;

	const category = await PrimaryCategoryServices.updatePrimaryCategoryImage(req.params.id, image as IFile);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		success: true,
		message: "Category image updated successfully!",
		data: category
	});
});

const deletePrimaryCategory: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const category = await PrimaryCategoryServices.deletePrimaryCategory(req.params.id);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		success: true,
		message: "Category deleted successfully!",
		data: category
	});
});

const toggleFeatured: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const category = await PrimaryCategoryServices.toggleFeatured(req.params.id);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		success: true,
		message: "Category featured updated successfully!",
		data: category
	});
});

const PrimaryCategoryControllers = {
	createPrimaryCategory,
	getPrimaryCategories,
	getPrimaryCategory,
	getPrimaryCategoryBySlug,
	updatePrimaryCategory,
	updatePrimaryCategoryImage,
	deletePrimaryCategory,
	toggleFeatured,
	getAllCategories
};

export default PrimaryCategoryControllers;
