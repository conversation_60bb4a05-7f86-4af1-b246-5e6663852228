import { Request, RequestHandler, Response } from "express";
import httpStatus from "http-status";
import AppError from "../../errors/AppError";
import { IFile } from "../../interface";
import catchAsync from "../../utils/catchAsync";
import sendResponse from "../../utils/sendResponse";
import OfferServices from "./offer.services";

const createOffer: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const files = req.files as any;

	const profilePicture = files?.["profilePicture"]?.[0];
	const banner = files?.["banner"]?.[0];

	if (!profilePicture || !banner) {
		throw new AppError(httpStatus.BAD_REQUEST, "Both profilePicture and banner images are required!");
	}
	const product = await OfferServices.createOffer(req.body, profilePicture, banner);

	sendResponse(res, {
		statusCode: httpStatus.CREATED,
		success: true,
		message: "Offer created successfully!",
		data: product
	});
});
const getSingleOfferById: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const result = await OfferServices.getSingleOfferById(req.params.id);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		message: "Offer by ID fetched successfully!",
		success: true,
		data: result
	});
});

const getSingleOfferBySlug: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const result = await OfferServices.getSingleOfferBySlug(req.params.slug);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		message: "Offer by Slug fetched successfully!",
		success: true,
		data: result
	});
});

const getOffers: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const { offers, meta } = await OfferServices.getOffers(req.query);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		message: "Offers fetched successfully!",
		success: true,
		meta,
		data: offers
	});
});

const getOffersWithProducts: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const { offers, meta } = await OfferServices.getOffersWithProducts(req.query);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		message: "Offers with products fetched successfully!",
		success: true,
		meta,
		data: offers
	});
});

const updateOffer: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const product = await OfferServices.updateOffer(req.params.id, req.body);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		success: true,
		message: "Offer updated successfully!",
		data: product
	});
});

const updateOfferImages: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const newImages = req.files as IFile[];
	const product = await OfferServices.updateOfferImages(req.params.id, req.body.imagesToRemove, newImages);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		success: true,
		message: "Offer images updated successfully!",
		data: product
	});
});

const deleteOffer: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	await OfferServices.deleteOffer(req.params.id);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		success: true,
		message: "Offer deleted successfully!",
		data: null
	});
});

const OfferControllers = {
	createOffer,
	getOffers,
	getOffersWithProducts,
	getSingleOfferById,
	getSingleOfferBySlug,
	updateOffer,
	updateOfferImages,
	deleteOffer
};

export default OfferControllers;
