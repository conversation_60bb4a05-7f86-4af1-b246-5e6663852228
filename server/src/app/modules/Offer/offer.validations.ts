import { z } from "zod";

const offerCreateValidation = z.object({
	body: z.object({
		name: z
			.string({
				required_error: "Name is required!",
				invalid_type_error: "Name must be a string!"
			})
			.min(3, "Name must be at least 3 characters long!")
			.max(255, "Name must be at most 255 characters long!"),
		description: z.string({
			invalid_type_error: "Description must be a string!"
		}),
		startDate: z.string({
			invalid_type_error: "Started date must be a string!"
		}),
		endDate: z.string({
			invalid_type_error: "End date must be a string!"
		}),
		products: z
			.array(
				z.string({
					message: "Invalid product id!"
				})
			)
			.optional()
	})
});

const OfferValidations = {
	offerCreateValidation
};

export default OfferValidations;
