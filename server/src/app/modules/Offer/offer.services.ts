import httpStatus from "http-status";
import { Types } from "mongoose";
import QueryBuilder from "../../builder/QueryBuilder";
import { imageFolders } from "../../constant";
import RedisClient from "../../DB/redisClient";
import AppError from "../../errors/AppError";
import { IFile } from "../../interface";
import { createFileId } from "../../utils";
import { deleteFile, extractS3Key, uploadFile } from "../../utils/awsFileManager";
import Offer from "./offer.model";
import { IOffer } from "./offer.type";

const createOffer = async (offer: IOffer, profilePicture: IFile, banner: IFile) => {
	const imagesToUpload = [
		{ file: profilePicture, type: "profilePicture" },
		{ file: banner, type: "banner" }
	];

	const uploaded: Record<string, string> = {};

	for (const { file, type } of imagesToUpload) {
		const imageId = createFileId(imageFolders.OFFERS, `${offer.name}-${type}`);
		const uploadedImage = await uploadFile(file, imageId);
		if (!uploadedImage) {
			throw new AppError(httpStatus.INTERNAL_SERVER_ERROR, `Error uploading ${type}`);
		}
		uploaded[type] = imageId;
	}

	await Offer.create({
		...offer,
		profilePicture: uploaded.profilePicture,
		banner: uploaded.banner
	});

	return "Offer created successfully!";
};

const getOffers = async (query: Record<string, unknown>) => {
	const offersQuery = (await new QueryBuilder(Offer.find(), query).search(["name", "description"]).filter())
		.fields()
		.paginate()
		.sort();

	const offers = await offersQuery.modelQuery;
	// 🔹 Meta data
	const paginationData = await offersQuery.countTotal();

	return {
		offers,
		meta: {
			...paginationData
		}
	};
};

const getOffersWithProducts = async (query: Record<string, unknown>) => {
	const offersQuery = (
		await new QueryBuilder(
			Offer.find().populate({
				path: "products",
				select: "name images price actualPrice"
			}),
			query
		)
			.search(["name", "description"])
			.filter()
	)
		.fields()
		.paginate()
		.sort();

	const offers = await offersQuery.modelQuery;
	// 🔹 Meta data
	const paginationData = await offersQuery.countTotal();

	return {
		offers,
		meta: {
			...paginationData
		}
	};
};

const getSingleOfferById = async (id: string) => {
	const result = await Offer.findById(id).lean();
	return result;
};

const getSingleOfferBySlug = async (slug: string) => {
	const result = await Offer.findOne({ slug }).lean();
	return result;
};

const updateOffer = async (id: string, offer: IOffer) => {
	await Offer.findByIdAndUpdate(id, offer, { new: true });
	// 🔹 Clear cache
	await RedisClient.del(`offer:${id}`);
	await RedisClient.del(`offer:${offer.slug}`);
	return "Offer updated successfully!";
};

const deleteOffer = async (id: string) => {
	const offer = await Offer.findById(id).lean();
	if (!offer) {
		throw new AppError(httpStatus.NOT_FOUND, "Offer not found!");
	}

	// delete images from S3
	// const deletedBannerKey = extractS3Key(offer?.banner);
	// const deletedProfileKey = extractS3Key(offer?.profilePicture);
	// console.log({ deletedBannerKey, deletedProfileKey });
	await deleteFile(offer?.banner);
	await deleteFile(offer?.profilePicture);

	await Offer.findByIdAndDelete(id);
	return "Offer deleted successfully!";
};

const updateOfferImages = async (offerId: string, imagesToRemove: string[], newImages: IFile[]) => {
	const offer = await Offer.findById(offerId);
	if (!offer) {
		throw new AppError(httpStatus.NOT_FOUND, "Offer not found!");
	}

	const newImagesKeys: string[] = [];

	for (const image of newImages) {
		const imageId = createFileId(imageFolders.OFFERS, offer.name);
		const uploadedImage = await uploadFile(image, imageId);
		if (!uploadedImage) {
			throw new AppError(httpStatus.INTERNAL_SERVER_ERROR, "Error uploading image");
		}
		newImagesKeys.push(imageId);
	}

	// 🔹 Extract S3 keys for removal
	const imageKeysToDelete = imagesToRemove.map(extractS3Key) ?? [];

	// 🔹 Delete images in parallel if any exist
	if (imageKeysToDelete.length) {
		await Promise.all(imageKeysToDelete.map(deleteFile));
	}

	// 🔹 Filter out removed images

	// 🔹 Update offer images in DB using aggregation
	await Offer.updateOne(
		{ _id: new Types.ObjectId(offerId) },
		{ $set: { profilePicture: newImagesKeys[0], banner: newImagesKeys[1] } }
	);
	return "Offer images updated successfully!";
};

const OfferServices = {
	createOffer,
	getOffers,
	getOffersWithProducts,
	getSingleOfferById,
	getSingleOfferBySlug,
	updateOffer,
	updateOfferImages,
	deleteOffer
};

export default OfferServices;
