import express from "express";
import { userRoles } from "../../constant";
import AuthGuard from "../../middlewares/AuthGuard";
import validateRequest from "../../middlewares/validateRequest";
import { dataParser } from "../../utils";
import upload from "../../utils/awsFileManager";
import OfferControllers from "./offer.controllers";
import offerValidations from "./offer.validations";

const router = express.Router();

router.post(
	"/create",
	// AuthGuard(userRoles.ADMIN),
	// upload.array("images", 10),
	upload.fields([
		{ name: "banner", maxCount: 1 },
		{ name: "profilePicture", maxCount: 1 }
	]),
	dataParser(),
	validateRequest(offerValidations.offerCreateValidation),
	OfferControllers.createOffer
);

router.get("/", OfferControllers.getOffers);
router.get("/with-products", OfferControllers.getOffersWithProducts);
router.get("/:id", OfferControllers.getSingleOfferById);
router.get("/slug/:slug", OfferControllers.getSingleOfferBySlug);

router.patch(
	"/update-images/:id",
	AuthGuard(userRoles.ADMIN),
	upload.array("images", 2),
	dataParser(),
	OfferControllers.updateOfferImages
);

router.delete("/:id", AuthGuard(userRoles.ADMIN), OfferControllers.deleteOffer);

router.patch(
	"/:id",
	AuthGuard(userRoles.ADMIN),
	validateRequest(offerValidations.offerCreateValidation),
	OfferControllers.updateOffer
);

const OfferRoutes = router;

export default OfferRoutes;
