import { CallbackError, model, Schema } from "mongoose";
import { updateImageUrl } from "../../utils/awsFileManager";
import { CreateSlug } from "../../utils/CreateSlug";
import { IOffer } from "./offer.type";

/**
 * Offer Schema
 */
const offerSchema = new Schema<IOffer>(
	{
		name: { type: String, required: true },
		slug: { type: String, unique: true },
		description: { type: String, required: true },
		startDate: { type: Date, required: true },
		endDate: { type: Date, required: true },
		profilePicture: { type: String, required: true },
		banner: { type: String, required: true },
		products: {
			type: [Schema.Types.ObjectId],
			ref: "Product",
			default: []
		}
	},
	{ timestamps: true }
);

/**
 * Middleware: Validate category existence before saving
 */
offerSchema.pre<IOffer>("save", async function (next) {
	try {
		if (!this.slug) {
			this.slug = CreateSlug(this.name);
		}

		next();
	} catch (error) {
		next(error as CallbackError);
	}
});

/**
 * Middleware: Update slug when offer name is updated
 */
offerSchema.pre("findOneAndUpdate", async function (next) {
	const update = this.getUpdate() as Partial<IOffer>;
	if (update?.name) {
		update.slug = CreateSlug(update.name);
		this.setUpdate(update);
	}
	next();
});

/**
 * Middleware: Update image URLs after fetching offers
 */
offerSchema.post("find", async function (result) {
	await updateImageUrl(result);
});

/**
 * Middleware: Update image URLs after fetching offer
 */

offerSchema.post("findOne", async function (result) {
	await updateImageUrl(result);
});

const Offer = model<IOffer>("Offer", offerSchema);
export default Offer;
