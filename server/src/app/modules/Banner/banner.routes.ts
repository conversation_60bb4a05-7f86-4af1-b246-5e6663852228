import express from "express";
import { userRoles } from "../../constant";
import AuthGuard from "../../middlewares/AuthGuard";
import validateRequest from "../../middlewares/validateRequest";
import { dataParser } from "../../utils";
import upload from "../../utils/awsFileManager";

import BannerControllers from "./banner.controller";
import BannerValidation from "./banner.validation";

const router = express.Router();

// * only admin can delete, update or add a new banner
// * anyone can get all banners
//  * route: /api/banners/

router.post(
	"/add",
	AuthGuard(userRoles.ADMIN),
	upload.single("image"),
	dataParser(),
	validateRequest(BannerValidation),
	BannerControllers.AddBanner
);

router.get("/", BannerControllers.GetBanners);

router.put("/toggle/:id", AuthGuard(userRoles.ADMIN), BannerControllers.ToggleBanner);

router.patch("/:id", AuthGuard(userRoles.ADMIN), validateRequest(BannerValidation), BannerControllers.UpdateBanner);

router.delete("/:id", AuthGuard(userRoles.ADMIN), BannerControllers.DeleteBanner);

const BannerRoutes = router;

export default BannerRoutes;
