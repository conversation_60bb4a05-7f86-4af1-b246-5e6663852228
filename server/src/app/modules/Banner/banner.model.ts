import { model, Schema } from "mongoose";
import { deleteFile, updateImageUrl } from "../../utils/awsFileManager";
import { TBanner } from "./banner.types";

const BannerSchema = new Schema<TBanner>(
	{
		image: { type: String, required: true },
		linkUrl: { type: String },
		isActive: { type: Boolean, default: true },
		type: { type: String, enum: ["slider", "static-1", "static-2", "static-3", "static-4"], required: true }
	},
	{ timestamps: true }
);

BannerSchema.post<TBanner>("find", async function (result) {
	await updateImageUrl(result);
});

// delete image from s3 on delete
BannerSchema.post<TBanner>("findOneAndDelete", async function (res: TBanner) {
	if (res?.image) {
		await deleteFile(res?.image);
	}
});

const Banner = model<TBanner>("Banner", BannerSchema);

export default Banner;
