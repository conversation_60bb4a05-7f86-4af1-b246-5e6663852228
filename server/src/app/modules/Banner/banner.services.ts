import httpStatus from "http-status";
import { imageFolders } from "../../constant";
import RedisClient from "../../DB/redisClient";
import AppError from "../../errors/AppError";
import { IFile } from "../../interface";
import { createFileId } from "../../utils";
import { uploadFile } from "../../utils/awsFileManager";
import Banner from "./banner.model";
import { TBanner } from "./banner.types";

const AddBanner = async (banner: TBanner, file: IFile) => {
	if (!file) {
		throw new AppError(httpStatus.BAD_REQUEST, "Image is required!");
	}
	// a random image id of 6 digits is generated
	const randomId = Math.floor(100000 + Math.random() * 900000);

	const image = createFileId(imageFolders.CATEGORIES, randomId.toString());

	const uploadedImage = await uploadFile(file, image);
	if (!uploadedImage) {
		throw new AppError(httpStatus.INTERNAL_SERVER_ERROR, "Error uploading image");
	}

	const newBanner = await Banner.create({ ...banner, image });

	// 🔹 Clear cache
	await RedisClient.del("banners");
	return newBanner;
};

const GetBanners = async () => {
	const cachedProduct = await RedisClient.get("banners");
	if (cachedProduct) {
		return JSON.parse(cachedProduct);
	}

	const banners = await Banner.find().sort({ createdAt: -1 });
	await RedisClient.set("banners", JSON.stringify(banners), "EX", 6 * 60 * 60);
	return banners;
};

const ToggleBanner = async (id: string) => {
	const banner = await Banner.findById(id);
	if (!banner) {
		throw new AppError(httpStatus.NOT_FOUND, "Banner not found!");
	}
	banner.isActive = !banner.isActive;

	// 🔹 Clear cache
	await RedisClient.del("banners");
	return banner.save();
};

const UpdateBanner = async (id: string, data: Partial<TBanner>) => {
	const banner = await Banner.findById(id);

	if (!banner) {
		throw new AppError(httpStatus.NOT_FOUND, "Banner not found!");
	}

	Object.assign(banner, data);

	// 🔹 Clear cache
	await RedisClient.del("banners");
	return banner.save();
};

const DeleteBanner = async (id: string) => {
	// 🔹 Clear cache
	await RedisClient.del("banners");
	return Banner.findByIdAndDelete(id);
};

const BannerServices = {
	AddBanner,
	GetBanners,
	ToggleBanner,
	UpdateBanner,
	DeleteBanner
};

export default BannerServices;
