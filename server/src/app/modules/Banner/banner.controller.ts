import { Request, RequestHandler, Response } from "express";
import httpStatus from "http-status";
import { IFile } from "../../interface";
import catchAsync from "../../utils/catchAsync";
import sendResponse from "../../utils/sendResponse";
import BannerServices from "./banner.services";

const AddBanner: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const image = req.file;
	await BannerServices.AddBanner(req.body, image as IFile);

	sendResponse(res, {
		statusCode: httpStatus.CREATED,
		success: true,
		message: "Banner created successfully!"
	});
});

const GetBanners: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const banners = await BannerServices.GetBanners();

	sendResponse(res, {
		statusCode: httpStatus.OK,
		success: true,
		message: "Banners fetched successfully!",
		data: banners
	});
});

const ToggleBanner: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	await BannerServices.ToggleBanner(req.params.id);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		success: true,
		message: "Banner updated successfully!"
	});
});

const UpdateBanner: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	await BannerServices.UpdateBanner(req.params.id, req.body);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		success: true,
		message: "Banner updated successfully!"
	});
});

const DeleteBanner: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	await BannerServices.DeleteBanner(req.params.id);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		success: true,
		message: "Banner deleted successfully!"
	});
});

const BannerControllers = {
	AddBanner,
	GetBanners,
	ToggleBanner,
	UpdateBanner,
	DeleteBanner
};

export default BannerControllers;
