import { z } from "zod";

const BannerValidation = z.object({
	body: z.object({
		linkUrl: z.string({
			invalid_type_error: "Link URL must be a string!",
			required_error: "Add a link URL!"
		}),
		type: z.enum(["slider", "static-1", "static-2", "static-3", "static-4"], {
			message: "Banner type must be either 'slider', 'static-1', 'static-2', 'static-3', or 'static-4'!"
		})
	})
});

export default BannerValidation;
