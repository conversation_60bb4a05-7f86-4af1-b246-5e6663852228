import httpStatus from "http-status";
import mongoose, { SortOrder } from "mongoose";
import AppError from "../../errors/AppError";
import Order from "./order.model";
import { TOrder } from "./order.type";

const createOrder = async (payload: Omit<TOrder, "_id" | "orderNumber" | "orderDate">): Promise<TOrder> => {
	const totalAmount = payload?.items.reduce(
		(sum: number, item: { price: number; quantity: number }) => sum + item.price * item.quantity,
		0
	);
	const order = await Order.create({
		...payload,
		totalAmount,
		orderDate: new Date()
	});
	return order;
};

const getAllOrders = async (query: Record<string, unknown> = {}) => {
	const page = Number(query.page) || 1;
	const limit = Number(query.limit) || 10;
	const skip = (page - 1) * limit;

	const sortBy = (query.sortBy as string) || "orderDate";
	const sortOrder = (query.sortOrder as string) || "desc";

	// Filters
	const filter: Record<string, unknown> = {};

	if (query.status) {
		filter.status = query.status;
	}

	// if (query.customerName) {
	// 	filter.customerName = { $regex: query.customerName, $options: "i" };
	// }

	if (query.fromDate && query.toDate) {
		filter.orderDate = {
			$gte: new Date(query.fromDate as string),
			$lte: new Date(query.toDate as string)
		};
	} else if (query.fromDate) {
		filter.orderDate = { $gte: new Date(query.fromDate as string) };
	} else if (query.toDate) {
		filter.orderDate = { $lte: new Date(query.toDate as string) };
	}

	// Query
	const orders = await Order.find(filter)
		.sort({ [sortBy]: sortOrder as SortOrder })
		.skip(skip)
		.limit(limit)
		.populate({
			path: "items.productId",
			select: "name slug images variants"
		})
		.lean();

	const totalItems = await Order.countDocuments(filter);

	return {
		orders,
		meta: {
			page,
			limit,
			totalItems,
			totalPages: Math.ceil(totalItems / limit)
		}
	};
};

const getAllOrdersByUser = async (userId: string, query: Record<string, unknown> = {}) => {
	const page = Number(query.page) || 1;
	const limit = Number(query.limit) || 10;
	const skip = (page - 1) * limit;

	const sortBy = (query.sortBy as string) || "orderDate";
	const sortOrder = (query.sortOrder as string) || "desc";

	// Filters
	const filter: Record<string, unknown> = {};

	if (query.status) {
		filter.status = query.status;
	}

	// if (query.customerName) {
	// 	filter.customerName = { $regex: query.customerName, $options: "i" };
	// }

	if (query.fromDate && query.toDate) {
		filter.orderDate = {
			$gte: new Date(query.fromDate as string),
			$lte: new Date(query.toDate as string)
		};
	} else if (query.fromDate) {
		filter.orderDate = { $gte: new Date(query.fromDate as string) };
	} else if (query.toDate) {
		filter.orderDate = { $lte: new Date(query.toDate as string) };
	}

	// Query
	const orders = await Order.find(filter)
		.sort({ [sortBy]: sortOrder as SortOrder })
		.skip(skip)
		.limit(limit)
		.populate("items.productId", "name images");

	const totalItems = await Order.countDocuments(filter);

	return {
		orders,
		meta: {
			page,
			limit,
			totalItems,
			totalPages: Math.ceil(totalItems / limit)
		}
	};
};
const getOrderById = async (id: string): Promise<TOrder> => {
	const isValidId = mongoose.isValidObjectId(id);
	if (!isValidId) {
		throw new AppError(httpStatus.BAD_REQUEST, "Invalid order ID");
	}

	const order = await Order.findById(id);
	if (!order) {
		throw new AppError(httpStatus.NOT_FOUND, "Order not found");
	}

	return order;
};

const getOrderByOrderNumber = async (orderNumber: string): Promise<TOrder> => {
	const order = await Order.findOne({ orderNumber });
	if (!order) {
		throw new AppError(httpStatus.NOT_FOUND, "Order not found");
	}

	return order;
};

const updateOrderStatus = async (id: string, status: TOrder["status"]): Promise<TOrder> => {
	const isValidId = mongoose.isValidObjectId(id);
	if (!isValidId) {
		throw new AppError(httpStatus.BAD_REQUEST, "Invalid order ID");
	}

	const order = await Order.findById(id);
	if (!order) {
		throw new AppError(httpStatus.NOT_FOUND, "Order not found");
	}

	order.status = status;
	await order.save();

	return order;
};

const deleteOrder = async (id: string): Promise<void> => {
	const isValidId = mongoose.isValidObjectId(id);
	if (!isValidId) {
		throw new AppError(httpStatus.BAD_REQUEST, "Invalid order ID");
	}

	const result = await Order.findByIdAndDelete(id);
	if (!result) {
		throw new AppError(httpStatus.NOT_FOUND, "Order not found");
	}
};

const OrderService = {
	createOrder,
	getAllOrders,
	getAllOrdersByUser,
	getOrderById,
	getOrderByOrderNumber,
	updateOrderStatus,
	deleteOrder
};

export default OrderService;
