import { Request, RequestHandler, Response } from "express";
import httpStatus from "http-status";
import catchAsync from "../../utils/catchAsync";
import sendResponse from "../../utils/sendResponse";
import OrderService from "./order.service";

const createOrder: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const order = await OrderService.createOrder(req.body);

	sendResponse(res, {
		statusCode: httpStatus.CREATED,
		success: true,
		message: "Order created successfully!",
		data: order
	});
});

const getAllOrders: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const { orders, meta } = await OrderService.getAllOrders(req.query);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		success: true,
		message: "Orders fetched successfully!",
		meta,
		data: orders
	});
});

const getAllOrdersByUser: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const { orders, meta } = await OrderService.getAllOrdersByUser(req.userData?._id, req.query);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		success: true,
		message: "Orders fetched successfully!",
		meta,
		data: orders
	});
});

const getOrderById: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const order = await OrderService.getOrderById(req.params.id);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		success: true,
		message: "Order fetched successfully!",
		data: order
	});
});

const getOrderByOrderNumber: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const order = await OrderService.getOrderByOrderNumber(req.params.orderNumber);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		success: true,
		message: "Order fetched successfully!",
		data: order
	});
});

const updateOrderStatus: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const order = await OrderService.updateOrderStatus(req.params.id, req.body.status);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		success: true,
		message: "Order status updated successfully!",
		data: order
	});
});

const deleteOrder: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	await OrderService.deleteOrder(req.params.id);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		success: true,
		message: "Order deleted successfully!",
		data: null
	});
});

const OrderController = {
	createOrder,
	getAllOrders,
	getAllOrdersByUser,
	getOrderById,
	getOrderByOrderNumber,
	updateOrderStatus,
	deleteOrder
};

export default OrderController;
