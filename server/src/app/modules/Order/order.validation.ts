import mongoose from "mongoose";
import { z } from "zod";

const orderItemSchema = z.object({
	productId: z
		.string({
			required_error: "Product ID is required",
			invalid_type_error: "Product ID must be a string"
		})
		.refine((val) => mongoose.Types.ObjectId.isValid(val), {
			message: "Invalid product ID"
		}),
	variantId: z
		.string()
		.optional()
		.refine((val) => !val || mongoose.Types.ObjectId.isValid(val), { message: "Invalid variant ID" }),
	quantity: z
		.number({
			required_error: "Quantity is required",
			invalid_type_error: "Quantity must be a number"
		})
		.positive("Quantity must be a positive number"),
	price: z
		.number({
			required_error: "Price is required",
			invalid_type_error: "Price must be a number"
		})
		.nonnegative("Price must be a non-negative number")
});

const createOrderValidation = z.object({
	body: z.object({
		customerInfo: z.object({
			name: z
				.string({
					required_error: "Customer name is required",
					invalid_type_error: "Customer name must be a string"
				})
				.min(2, "Customer name must be at least 2 characters long")
				.max(100, "Customer name must be at most 100 characters long"),
			phone: z
				.string({
					required_error: "Customer phone number is required",
					invalid_type_error: "Customer phone number must be a string"
				})
				.min(2, "Customer phone number must be at least 2 characters long")
				.max(100, "Customer phone number must be at most 100 characters long"),
			address: z
				.string({
					required_error: "Customer address is required",
					invalid_type_error: "Customer address must be a string"
				})
				.min(2, "Customer address must be at least 2 characters long")
				.max(250, "Customer address must be at most 100 characters long")
		}),
		totalAmount: z
			.number({
				required_error: "Total amount is required",
				invalid_type_error: "Total amount must be a number"
			})
			.nonnegative("Total amount must be a non-negative number"),
		items: z
			.array(orderItemSchema, {
				required_error: "Order items are required",
				invalid_type_error: "Order items must be an array"
			})
			.nonempty("Order must contain at least one item")
	})
});

const updateOrderStatusValidation = z.object({
	body: z.object({
		status: z.enum(["pending", "shipped", "delivered", "canceled"], {
			required_error: "Status is required",
			invalid_type_error: "Invalid status value"
		})
	})
});

const orderValidations = {
	createOrderValidation,
	updateOrderStatusValidation
};

export default orderValidations;
