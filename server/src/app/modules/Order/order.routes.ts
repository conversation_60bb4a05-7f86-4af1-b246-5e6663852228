import express from "express";
import { userRoles } from "../../constant";
import AuthGuard from "../../middlewares/AuthGuard";
import validateRequest from "../../middlewares/validateRequest";
import OrderController from "./order.controller";
import orderValidations from "./order.validation";

const router = express.Router();

// Create order
router.post("/create", validateRequest(orderValidations.createOrderValidation), OrderController.createOrder);

// Get all orders
router.get("/", AuthGuard(userRoles.ADMIN), OrderController.getAllOrders);

// Get all orders by user
router.get("/user", AuthGuard(userRoles.USER), OrderController.getAllOrdersByUser);

// Get order by ID
router.get("/:id", AuthGuard(userRoles.ADMIN), OrderController.getOrderById);

// Get order by order number
router.get("/number/:orderNumber", AuthGuard(userRoles.ADMIN), OrderController.getOrderByOrderNumber);

// Update order status
router.patch(
	"/status/:id",
	AuthGuard(userRoles.ADMIN),
	validateRequest(orderValidations.updateOrderStatusValidation),
	OrderController.updateOrderStatus
);

// Delete order
router.delete("/:id", AuthGuard(userRoles.ADMIN), OrderController.deleteOrder);

const OrderRoutes = router;

export default OrderRoutes;
