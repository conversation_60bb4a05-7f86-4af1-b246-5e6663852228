import { model, Schema } from "mongoose";
import { TOrder } from "./order.type";

const itemSchema = new Schema({
	productId: {
		type: Schema.Types.ObjectId,
		required: true,
		ref: "Product"
	},
	variantId: {
		type: Schema.Types.ObjectId,
		required: false
	},
	quantity: {
		type: Number,
		required: true
	},
	price: {
		type: Number,
		required: true
	}
});

const orderSchema = new Schema<TOrder>(
	{
		orderNumber: {
			type: String,
			required: true,
			unique: true
		},
		customerInfo: {
			name: {
				type: String,
				required: true
			},
			phone: {
				type: String,
				required: true
			},
			address: {
				type: String,
				required: true
			}
		},
		orderDate: {
			type: Date,
			default: Date.now
		},
		status: {
			type: String,
			enum: ["pending", "shipped", "delivered", "canceled"],
			default: "pending"
		},
		totalAmount: {
			type: Number,
			required: true
		},
		items: [itemSchema]
	},
	{ timestamps: true }
);

// Generate a unique order number before saving
orderSchema.pre("validate", async function (next) {
	if (!this.orderNumber) {
		// Generate a random alphanumeric order number
		const timestamp = new Date().getTime().toString().slice(-6);
		const random = Math.floor(Math.random() * 10000)
			.toString()
			.padStart(4, "0");
		this.orderNumber = `ORD-${timestamp}-${random}`;
	}
	next();
});

const Order = model<TOrder>("Order", orderSchema);
export default Order;
