import bcrypt from "bcryptjs";
import { Schema, model } from "mongoose";
import config from "../../config";
import { IUser } from "./user.types";

const userSchema = new Schema<IUser>(
	{
		name: { type: String, required: true },
		phone: { type: String, required: true, unique: true },
		password: { type: String, required: true, select: false },
		is_verified: { type: <PERSON>olean, default: false },
		role: { type: String, default: "user" },
		status: { type: String, default: "active" },
		addresses: [
			{
				district: {
					type: String,
					required: true
				},
				// thana: {
				// 	type: String,
				// 	required: true
				// },
				detailsAddress: {
					type: String,
					required: true
				},
				isDefault: {
					type: <PERSON><PERSON><PERSON>,
					default: false
				}
			}
		]
	},
	{
		timestamps: true
	}
);

userSchema.pre<IUser>("save", async function (next) {
	// eslint-disable-next-line @typescript-eslint/no-this-alias
	const user = this;
	user.password = await bcrypt.hash(user.password, Number(config.bcrypt_salt_round));
	next();
});

userSchema.statics.isUserExists = async function (id: string) {
	return await this.findById(id);
};

userSchema.statics.isUserExistsWithPhone = async function (phone: string) {
	return await this.findOne({ phone });
};

const User = model<IUser>("User", userSchema);

export default User;
