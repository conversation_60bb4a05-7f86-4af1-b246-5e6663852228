import { z } from "zod";

const createUserValidation = z.object({
	body: z.object({
		name: z
			.string({
				invalid_type_error: "Name must be a string!",
				required_error: "Name is required!"
			})
			.min(3, {
				message: "Name must be at least 3 characters long!"
			})
			.max(50, {
				message: "Name must be at most 50 characters long!"
			}),
		phone: z
			.string({
				invalid_type_error: "Phone number must be a string!",
				required_error: "Phone number is required!"
			})
			.regex(/^(?:\+8801|01)[3-9]\d{8}$/, {
				message: "Phone number must be a valid Bangladeshi number (e.g., +8801XXXXXXXX or 01XXXXXXXX)!"
			}),
		password: z
			.string({
				invalid_type_error: "Password must be a string!",
				required_error: "Password is required!"
			})
			.min(6, {
				message: "Password must be at least 6 characters long!"
			})
			.regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{6,}$/, {
				message:
					"Password must contain at least one uppercase letter, one lowercase letter, one number and one special character!"
			})
	})
});

const verifyUserValidation = z.object({
	body: z.object({
		phone: z
			.string({
				invalid_type_error: "Phone number must be a string!",
				required_error: "Phone number is required!"
			})
			.regex(/^(?:\+8801|01)[3-9]\d{8}$/, {
				message: "Phone number must be a valid Bangladeshi number (e.g., +8801XXXXXXXX or 01XXXXXXXX)!"
			}),
		otp: z
			.string({
				invalid_type_error: "OTP must be a string!",
				required_error: "OTP is required!"
			})
			.length(6, {
				message: "OTP must be exactly 6 characters long!"
			})
			.regex(/^\d{6}$/, {
				message: "OTP must contain only digits!"
			})
	})
});

const loginUserValidation = z.object({
	body: z.object({
		phone: z
			.string({
				invalid_type_error: "Phone number must be a string!",
				required_error: "Phone number is required!"
			})
			.regex(/^(?:\+8801|01)[3-9]\d{8}$/, {
				message: "Phone number must be a valid Bangladeshi number (e.g., +8801XXXXXXXX or 01XXXXXXXX)!"
			}),
		password: z.string({
			invalid_type_error: "Password must be a string!",
			required_error: "Password is required!"
		})
	})
});

const UserValidations = {
	createUserValidation,
	verifyUserValidation,
	loginUserValidation
};

export default UserValidations;
