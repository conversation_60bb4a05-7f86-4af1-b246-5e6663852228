import { Request, RequestHandler, Response } from "express";
import httpStatus from "http-status";
import catchAsync from "../../utils/catchAsync";
import sendResponse from "../../utils/sendResponse";
import UserServices from "./user.services";

const createUser: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const user = req.body;
	const newUser = await UserServices.createUser(user);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		success: true,
		message: "User created successfully!",
		data: newUser
	});
});

const verifyUser: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const { phone, otp } = req.body;
	const verifiedUser = await UserServices.verifyUser(phone, otp);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		success: true,
		message: "User verified successfully!",
		data: verifiedUser
	});
});

const loginUser: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const { phone, password } = req.body;
	const { accessToken, user } = await UserServices.loginUser(phone, password);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		success: true,
		message: "User logged in successfully!",
		data: { accessToken, user }
	});
});

const getMe: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const result = await UserServices.getMe(req.userData);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		success: true,
		message: "User fetched successfully!",
		data: result
	});
});

const updateUser: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const result = await UserServices.updateUser(req?.userData?._id, req.body);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		success: true,
		message: "User updated successfully!",
		data: result
	});
});

const changePassword: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const result = await UserServices.changePassword(req?.userData, req.body);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		success: true,
		message: "Password changed successfully!",
		data: result
	});
});

const UserControllers = {
	createUser,
	verifyUser,
	loginUser,
	getMe,
	updateUser,
	changePassword
};

export default UserControllers;
