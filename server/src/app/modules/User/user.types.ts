/* eslint-disable no-unused-vars */
import { Model } from "mongoose";
export interface IUser {
	_id?: string;
	name: string;
	phone: string;
	password: string;
	is_verified: boolean;
	role: "user";
	status: "active" | "blocked";
	addresses: {
		district: string;
		// thana: string;
		detailsAddress: string;
		isDefault: boolean;
	}[];
	createdAt: Date;
	updatedAt: Date;
}

export interface UserModel extends Model<IUser> {
	isUserExists(id: string): Promise<IUser | null>;
	isUserExistsWithPhone(phone: string): Promise<IUser | null>;
	isPasswordMatched(plainPassword: string, hashedPassword: string): Promise<boolean>;
}
