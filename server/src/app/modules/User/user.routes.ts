import express from "express";
import { userRoles } from "../../constant";
import AuthGuard from "../../middlewares/AuthGuard";
import validateRequest from "../../middlewares/validateRequest";
import UserControllers from "./user.controllers";
import UserValidations from "./user.validations";

const router = express.Router();

router.post("/create", validateRequest(UserValidations.createUserValidation), UserControllers.createUser);

router.post("/verify", validateRequest(UserValidations.verifyUserValidation), UserControllers.verifyUser);

router.post("/login", validateRequest(UserValidations.loginUserValidation), UserControllers.loginUser);

router.get("/get-me", AuthGuard(userRoles.ADMIN, userRoles.USER), UserControllers.getMe);

router.patch("/update-me", AuthGuard(userRoles.ADMIN, userRoles.USER), UserControllers.updateUser);

router.patch("/change-password", AuthGuard(userRoles.ADMIN, userRoles.USER), UserControllers.changePassword);

const userRoutes = router;

export default userRoutes;
