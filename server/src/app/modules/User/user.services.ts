import bcrypt from "bcryptjs";
import httpStatus from "http-status";
import { JwtPayload } from "jsonwebtoken";
import config from "../../config";
import AppError from "../../errors/AppError";
import { createToken } from "../../utils/JwtHelper";
import otpServices from "../OTP/otp.services";
import User from "./user.model";
import { IUser } from "./user.types";

const createUser = async (user: IUser) => {
	const existedUser = await User.findOne({ phone: user.phone });
	if (existedUser) {
		throw new AppError(httpStatus.CONFLICT, "Phone number already used!");
	}
	const res = await User.create(user);
	const createOtp = await otpServices.createOtp(user.phone);

	if (!createOtp) {
		await User.deleteOne({ _id: res._id });
		throw new AppError(httpStatus.INTERNAL_SERVER_ERROR, "Failed to create OTP");
	}

	return res;
};

const verifyUser = async (phone: string, otp: string) => {
	const verify = await otpServices.verifyOtp(phone, otp);

	if (!verify) {
		throw new AppError(httpStatus.BAD_REQUEST, "Invalid OTP. Please try again!");
	}

	const user = await User.findOneAndUpdate({ phone }, { is_verified: true }, { new: true });

	if (!user) {
		throw new AppError(httpStatus.NOT_FOUND, "User not found");
	}

	return user;
};

const loginUser = async (phone: string, password: string) => {
	const user = await User.findOne({ phone }).select("+password");

	if (!user) {
		throw new AppError(httpStatus.NOT_FOUND, "User not found");
	}

	if (!user.is_verified) {
		throw new AppError(httpStatus.UNAUTHORIZED, "User is not verified");
	}

	if (user.status === "blocked") {
		throw new AppError(httpStatus.FORBIDDEN, "User is blocked");
	}

	const isPasswordMatched = await bcrypt.compare(password, user.password);

	if (!isPasswordMatched) {
		throw new AppError(httpStatus.UNAUTHORIZED, "Incorrect password");
	}

	// Create JWT payload
	const jwtPayload = {
		id: user._id.toString(),
		phone: user.phone,
		role: user.role
	};

	// Generate access token
	const accessToken = createToken(jwtPayload, config.jwt_secret, config.jwt_expiration);

	// Return user without password
	const userWithoutPassword = await User.findOne({ phone });
	return { accessToken, user: userWithoutPassword };
};

const getMe = async (user: JwtPayload | undefined) => {
	const userData = await User.findById(user?._id).select("-password");

	if (!userData) {
		throw new AppError(httpStatus.NOT_FOUND, "User not found");
	}
	return userData;
};

const updateUser = async (id: string, payload: Partial<IUser>) => {
	const userData = await User.findById(id).select("-password");

	if (!userData) {
		throw new AppError(httpStatus.NOT_FOUND, "User not found");
	}
	const updatedData = await User.findByIdAndUpdate(id, payload);
	return updatedData;
};

const changePassword = async (user: JwtPayload | undefined, payload: { oldPassword: string; newPassword: string }) => {
	const { oldPassword, newPassword } = payload;
	const userData = await User.findById(user?._id).select("+password");
	if (!userData) {
		throw new AppError(httpStatus.NOT_FOUND, "User does not exist");
	}
	const isPasswordMatched = await bcrypt.compare(oldPassword, userData?.password);
	// check old password matching
	if (userData.password && !isPasswordMatched) {
		throw new AppError(httpStatus.UNAUTHORIZED, "Old Password is incorrect");
	}
	const updatedUser = await User.findByIdAndUpdate(userData?._id, { password: newPassword }, { new: true }).select(
		"-password"
	);

	return updatedUser;
};

const UserServices = {
	createUser,
	verifyUser,
	loginUser,
	getMe,
	updateUser,
	changePassword
};

export default UserServices;
