import httpStatus from "http-status";
import { CallbackError, model, Schema } from "mongoose";
import AppError from "../../errors/AppError";
import { updateImageUrl } from "../../utils/awsFileManager";
import { CreateSlug } from "../../utils/CreateSlug";
import PrimaryCategory from "../PrimaryCategory/primaryCategory.model";
import SecondaryCategory from "../SecondaryCategory/secondaryCategory.model";
import TertiaryCategory from "../TertiaryCategory/tertiaryCategory.model";
import { IProduct, IProductVariant } from "./product.type";

/**
 * Variant Schema
 */
const variantSchema = new Schema<IProductVariant>({
	color: {
		name: { type: String },
		value: { type: String }
	},
	storage: { type: String },
	price: { type: Number, required: true },
	stock: { type: Number, required: true },
	images: [{ type: String, required: true }],
	region: { type: String },
	condition: { type: String }
});

/**
 * Product Schema
 */
const productSchema = new Schema<IProduct>(
	{
		name: { type: String, required: true },
		slug: { type: String, unique: true },
		price: { type: Number, required: true },
		actualPrice: { type: Number },
		description: { type: String, required: true },
		warrantyInfo: { type: String },
		brand: { type: Schema.Types.ObjectId, required: true, ref: "Brand" },
		additionalInfo: [
			{
				key: { type: String, required: true },
				value: { type: String, required: true },
				description: { type: String, required: true }
			}
		],
		primaryCategory: { type: Schema.Types.ObjectId, required: true, ref: "PrimaryCategory" },
		secondaryCategory: { type: Schema.Types.ObjectId, required: true, ref: "SecondaryCategory" },
		tertiaryCategory: { type: Schema.Types.ObjectId, ref: "TertiaryCategory" },
		images: [{ type: String, required: true }],
		stock: { type: Number },
		isActive: { type: Boolean, default: true },
		variants: [variantSchema],
		filterableFields: [{ type: Schema.Types.ObjectId, ref: "ProductField" }]
	},
	{ timestamps: true }
);

/**
 * Middleware: Validate category existence before saving
 */
productSchema.pre<IProduct>("save", async function (next) {
	try {
		const [primaryCategory, secondaryCategory] = await Promise.all([
			PrimaryCategory.findById(this.primaryCategory),
			SecondaryCategory.findById(this.secondaryCategory),
			this.tertiaryCategory ? TertiaryCategory.findById(this.tertiaryCategory) : null
		]);

		const missingCategory = [
			{ category: primaryCategory, name: "Primary category" },
			{ category: secondaryCategory, name: "Secondary category" }
		].find(({ category }) => !category);

		if (missingCategory) {
			return next(new AppError(httpStatus.NOT_FOUND, `${missingCategory.name} not found!`));
		}

		if (!this.slug) {
			this.slug = CreateSlug(this.name);
		}

		next();
	} catch (error) {
		next(error as CallbackError);
	}
});

/**
 * Middleware: Update slug when product name is updated
 */
productSchema.pre("findOneAndUpdate", async function (next) {
	const update = this.getUpdate() as Partial<IProduct>;
	if (update?.name) {
		update.slug = CreateSlug(update.name);
		this.setUpdate(update);
	}
	next();
});

/**
 * Middleware: Update image URLs after fetching products
 */
productSchema.post("find", async function (result) {
	await updateImageUrl(result);
});

/**
 * Middleware: Update image URLs after fetching product
 */

productSchema.post("findOne", async function (result) {
	await updateImageUrl(result);
});
productSchema.index({ brand: 1, primaryCategory: 1, secondaryCategory: 1, tertiaryCategory: 1 });

const Product = model<IProduct>("Product", productSchema);
export default Product;
