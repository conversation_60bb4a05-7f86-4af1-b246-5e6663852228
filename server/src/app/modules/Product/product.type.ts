import { Types } from "mongoose";

export interface IProductVariant {
	_id?: Types.ObjectId;
	color?: {
		name: string;
		value: string;
	};
	storage?: string;
	price: number;
	stock: number;
	images: string[];
	region?: string;
	condition?: string;
}

export interface IProduct {
	_id?: Types.ObjectId;
	name: string;
	slug: string;
	brand: Types.ObjectId;
	description: string;
	warrantyInfo: string;

	price: number;
	actualPrice?: number;

	primaryCategory: Types.ObjectId;
	secondaryCategory: Types.ObjectId;
	tertiaryCategory?: Types.ObjectId;

	images?: string[];
	stock: number;
	isActive: boolean;

	variants: IProductVariant[];
	additionalInfo?: { key: string; value: string; description: string }[];

	filterableFields?: Types.ObjectId[];

	createAt?: Date;
	updateAt?: Date;
}
