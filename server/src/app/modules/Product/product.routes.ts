import express from "express";
import { userRoles } from "../../constant";
import AuthGuard from "../../middlewares/AuthGuard";
import validateRequest from "../../middlewares/validateRequest";
import { dataParser } from "../../utils";
import upload from "../../utils/awsFileManager";
import ProductControllers from "./product.controllers";
import productValidations from "./product.validations";

const router = express.Router();

router.post(
	"/create",
	AuthGuard(userRoles.ADMIN),
	upload.array("images", 10),
	dataParser(),
	validateRequest(productValidations.productCreateValidation),
	ProductControllers.createProduct
);

router.get("/", ProductControllers.getProducts);

router.get("/minimal", ProductControllers.getMinimalProducts);

router.get("/best-deals", ProductControllers.getBestDeals);

router.get("/best-sellers", ProductControllers.getBestSellers);

router.get("/offer/:slug", ProductControllers.getProductsOfAnOffer);

router.get("/:id", ProductControllers.getProductById);

router.get("/slug/:slug", ProductControllers.getProductBySlug);

router.patch(
	"/update-images/:id",
	AuthGuard(userRoles.ADMIN),
	upload.array("images", 10),
	dataParser(),
	validateRequest(productValidations.updateImagesValidation),
	ProductControllers.updateProductImages
);

router.delete("/:id", AuthGuard(userRoles.ADMIN), ProductControllers.deleteProduct);

router.patch("/toggle-status/:id", AuthGuard(userRoles.ADMIN), ProductControllers.toggleProductStatus);

/**
 * variant management
 */

router.post(
	"/create-variant/:productId",
	AuthGuard(userRoles.ADMIN),
	upload.array("images", 5),
	dataParser(),
	validateRequest(productValidations.variantSchemaValidation),
	ProductControllers.createProductVariant
);

router.patch(
	"/update-variant-images/:productId/:variantId",
	AuthGuard(userRoles.ADMIN),
	upload.array("images", 10),
	dataParser(),
	validateRequest(productValidations.updateImagesValidation),
	ProductControllers.updateProductVariantImages
);

router.patch(
	"/update-variant/:productId/:variantId",
	AuthGuard(userRoles.ADMIN),
	validateRequest(productValidations.variantSchemaValidation),
	ProductControllers.updateProductVariant
);

router.delete(
	"/delete-variant/:productId/:variantId",
	AuthGuard(userRoles.ADMIN),
	ProductControllers.deleteProductVariant
);
router.patch(
	"/:id",
	AuthGuard(userRoles.ADMIN),
	validateRequest(productValidations.productCreateValidation),
	ProductControllers.updateProduct
);

const ProductRoutes = router;

export default ProductRoutes;
