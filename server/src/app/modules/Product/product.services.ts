import httpStatus from "http-status";
import { Types } from "mongoose";
import QueryBuilder from "../../builder/QueryBuilder";
import { imageFolders } from "../../constant";
import RedisClient from "../../DB/redisClient";
import AppError from "../../errors/AppError";
import { IFile } from "../../interface";
import { createFileId } from "../../utils";
import { deleteFile, extractS3Key, uploadFile } from "../../utils/awsFileManager";
import Offer from "../Offer/offer.model";
import { ProductPopulateFields, ProductSearchableFields } from "./product.constant";
import Product from "./product.model";
import { IProduct, IProductVariant } from "./product.type";

const createProduct = async (product: IProduct, images: IFile[]) => {
	if (!images.length) {
		throw new AppError(httpStatus.BAD_REQUEST, "Images are required!");
	}

	const uploadedImages = [];

	// Loop through images and upload to AWS S3
	for (const image of images) {
		const imageId = createFileId(imageFolders.PRODUCTS, product.name);
		const uploadedImage = await uploadFile(image, imageId);
		if (!uploadedImage) {
			throw new AppError(httpStatus.INTERNAL_SERVER_ERROR, "Error uploading image");
		}
		uploadedImages.push(imageId);
	}

	await Product.create({
		...product,
		images: uploadedImages
	});
	return "Product created successfully!";
};

const getProductById = async (id: string) => {
	const cachedProduct = await RedisClient.get(`product:${id}`);
	if (cachedProduct) {
		return JSON.parse(cachedProduct);
	}

	const res = await Product.findById(id).populate(ProductPopulateFields);
	if (!res) {
		throw new AppError(httpStatus.NOT_FOUND, "Product not found!");
	}

	await RedisClient.set(`product:${id}`, JSON.stringify(res), "EX", 6 * 60 * 60);
	return res;
};

const getProductBySlug = async (slug: string) => {
	// 🔹 Check cache first
	const cachedProduct = await RedisClient.get(`product:${slug}`);
	if (cachedProduct) {
		return JSON.parse(cachedProduct);
	}

	const res = await Product.findOne({ slug }).populate(ProductPopulateFields);
	if (!res) {
		throw new AppError(httpStatus.NOT_FOUND, "Product not found!");
	}
	await RedisClient.set(`product:${slug}`, JSON.stringify(res), "EX", 6 * 60 * 60);
	return res;
};

const getProducts = async (query: Record<string, unknown>) => {
	const productsQuery = (
		await new QueryBuilder(Product.find().populate(ProductPopulateFields), query)
			.search(ProductSearchableFields)
			.filter()
	)
		.fields()
		.paginate()
		.sort();

	const products = await productsQuery.modelQuery;

	// 🔹 Extract filterable fields & brands in a single loop
	const filterableFieldsSet = new Set<string>();
	const commonBrandsSet = new Set<Types.ObjectId>();
	const commonPrimaryCategories = new Set<Types.ObjectId>();
	const commonSecondaryCategories = new Set<Types.ObjectId>();
	const commonTertiaryCategories = new Set<Types.ObjectId>();

	const finalProducts = products.map((product) => {
		const productObj = product.toObject();

		// ✅ Collect unique filterable fields
		(productObj.filterableFields as unknown as string[]).forEach((field: string) => filterableFieldsSet.add(field));

		// ✅ Collect unique brands & categories as strings for JSON compatibility
		if (productObj.brand) commonBrandsSet.add(productObj.brand);
		if (productObj.primaryCategory) commonPrimaryCategories.add(productObj.primaryCategory);
		if (productObj.secondaryCategory) commonSecondaryCategories.add(productObj.secondaryCategory);
		if (productObj.tertiaryCategory) commonTertiaryCategories.add(productObj.tertiaryCategory);

		// ❌ Remove `filterableFields` from response
		delete productObj.filterableFields;

		return productObj;
	});

	// 🔹 Meta data
	const paginationData = await productsQuery.countTotal();

	return {
		products: finalProducts,
		meta: {
			...paginationData,
			filterableFields: Array.from(filterableFieldsSet),
			commonBrands: Array.from(commonBrandsSet),
			commonPrimaryCategories: Array.from(commonPrimaryCategories),
			commonSecondaryCategories: Array.from(commonSecondaryCategories),
			commonTertiaryCategories: Array.from(commonTertiaryCategories)
		}
	};
};

const getMinimalProducts = async (query: Record<string, unknown>) => {
	const productsQuery = (
		await new QueryBuilder(Product.find().select("name slug images price actualPrice"), query)
			.search(ProductSearchableFields)
			.filter()
	)
		.fields()
		.paginate()
		.sort();

	const products = await productsQuery.modelQuery;
	const paginationData = await productsQuery.countTotal();

	return { products, meta: paginationData };
};

const getProductsOfAnOffer = async (offerSlug: string, query: Record<string, unknown>) => {
	const offer = await Offer.findOne({ slug: offerSlug }).select("products");
	if (!offer) {
		throw new AppError(httpStatus.NOT_FOUND, "Offer not found!");
	}
	const productsQuery = (
		await new QueryBuilder(
			Product.find({ _id: { $in: offer?.products } }).select("name slug images price actualPrice"),
			query
		)
			.search(ProductSearchableFields)
			.filter()
	)
		.fields()
		.paginate()
		.sort();

	const products = await productsQuery.modelQuery;
	const paginationData = await productsQuery.countTotal();

	return { products, meta: paginationData };
};

const updateProduct = async (id: string, product: IProduct) => {
	await getProductById(id);
	await Product.findByIdAndUpdate(id, product, { new: true });

	// 🔹 Clear cache
	await RedisClient.del(`product:${id}`);
	await RedisClient.del(`product:${product.slug}`);
	return "Product updated successfully!";
};

const deleteProduct = async (id: string) => {
	const product = await getProductById(id);

	// delete images from S3
	if (product.images) {
		for (const image of product.images) {
			const deletedImageKey = extractS3Key(image);
			await deleteFile(deletedImageKey);
		}

		await Product.findByIdAndDelete(id);

		// 🔹 Clear cache
		await RedisClient.del(`product:${id}`);
		await RedisClient.del(`product:${product.slug}`);
		return "Product deleted successfully!";
	}
};

const updateProductImages = async (productId: string, imagesToRemove: string[], newImages: IFile[]) => {
	const product = await getProductById(productId);

	const newImagesKeys: string[] = [];

	for (const image of newImages) {
		const imageId = createFileId(imageFolders.PRODUCTS, product.name);
		const uploadedImage = await uploadFile(image, imageId);
		if (!uploadedImage) {
			throw new AppError(httpStatus.INTERNAL_SERVER_ERROR, "Error uploading image");
		}
		newImagesKeys.push(imageId);
	}

	// 🔹 Extract S3 keys for removal
	const imageKeysToDelete = imagesToRemove.map(extractS3Key) ?? [];

	// 🔹 Delete images in parallel if any exist
	if (imageKeysToDelete.length) {
		await Promise.all(imageKeysToDelete.map(deleteFile));
	}

	// 🔹 Filter out removed images
	const currentImageKeys = product.images?.map(extractS3Key) ?? [];
	const updatedImages = currentImageKeys?.filter((image: string) => !imageKeysToDelete.includes(image)) ?? [];

	// 🔹 Update product images in DB using aggregation
	await Product.updateOne(
		{ _id: new Types.ObjectId(productId) },
		{ $set: { images: [...updatedImages, ...newImagesKeys] } }
	);

	// 🔹 Clear cache
	await RedisClient.del(`product:${productId}`);
	await RedisClient.del(`product:${product.slug}`);
	return "Product images updated successfully!";
};

const toggleProductStatus = async (id: string) => {
	const product = await getProductById(id);
	await Product.findByIdAndUpdate(id, { isActive: !product.isActive }, { new: true });

	// 🔹 Clear cache
	await RedisClient.del(`product:${id}`);
	await RedisClient.del(`product:${product.slug}`);
	return "Product status updated successfully!";
};

/**
 * Variant Management
 */

const createProductVariant = async (productId: string, variant: IProductVariant, images: IFile[]) => {
	const product = await getProductById(productId);
	if (!images.length) {
		throw new AppError(httpStatus.BAD_REQUEST, "Images are required!");
	}

	const uploadedImages = [];

	// Loop through images and upload to AWS S3
	for (const image of images) {
		const imageId = createFileId(imageFolders.PRODUCTS, `${product.name}-variant`);
		const uploadedImage = await uploadFile(image, imageId);
		if (!uploadedImage) {
			throw new AppError(httpStatus.INTERNAL_SERVER_ERROR, "Error uploading image");
		}
		uploadedImages.push(imageId);
	}

	await Product.updateOne(
		{ _id: new Types.ObjectId(productId) },
		{ $push: { variants: { ...variant, images: uploadedImages } } }
	);

	// 🔹 Clear cache
	await RedisClient.del(`product:${productId}`);
	await RedisClient.del(`product:${product.slug}`);
	return `New variant added to ${product.name} successfully!`;
};

const updateProductVariant = async (productId: string, variantId: string, variant: IProductVariant) => {
	const product = await getProductById(productId);
	const variantIndex = product?.variants?.findIndex(
		(v: { _id: string }) => v && v._id && v._id.toString() === variantId
	);
	const currentVariant = product?.variants?.[variantIndex];

	if (variantIndex === -1) {
		throw new AppError(httpStatus.NOT_FOUND, "Variant not found!");
	}
	const updatedVariant = {
		...variant,
		images: currentVariant.images.map(extractS3Key)
	};

	await Product.updateOne(
		{ _id: new Types.ObjectId(productId), "variants._id": new Types.ObjectId(variantId) },
		{ $set: { "variants.$": updatedVariant } }
	);

	// 🔹 Clear cache
	await RedisClient.del(`product:${productId}`);
	await RedisClient.del(`product:${product.slug}`);

	return "Product variant updated successfully!";
};

const deleteProductVariant = async (productId: string, variantId: string) => {
	const product = await getProductById(productId);
	const variantIndex = product?.variants?.findIndex(
		(v: { _id: string }) => v && v._id && v._id.toString() === variantId
	);
	if (variantIndex === -1) {
		throw new AppError(httpStatus.NOT_FOUND, "Variant not found!");
	}
	// delete images from S3
	for (const image of product.variants[variantIndex].images) {
		const deletedImageKey = extractS3Key(image);
		await deleteFile(deletedImageKey);
	}

	await Product.updateOne(
		{ _id: new Types.ObjectId(productId) },
		{ $pull: { variants: { _id: new Types.ObjectId(variantId) } } }
	);

	// 🔹 Clear cache
	await RedisClient.del(`product:${productId}`);
	await RedisClient.del(`product:${product.slug}`);

	return true;
};

const updateProductVariantImages = async (
	productId: string,
	variantId: string,
	imagesToRemove: string[],
	newImages: IFile[]
) => {
	const product = await getProductById(productId);
	const variantIndex = product?.variants?.findIndex(
		(v: { _id: string }) => v && v._id && v._id.toString() === variantId
	);
	const currentVariant = product?.variants?.[variantIndex];

	if (variantIndex === -1) {
		throw new AppError(httpStatus.NOT_FOUND, "Variant not found!");
	}

	const newImagesKeys: string[] = [];

	for (const image of newImages) {
		const imageId = createFileId(imageFolders.PRODUCTS, `${product.name}-variant`);
		const uploadedImage = await uploadFile(image, imageId);
		if (!uploadedImage) {
			throw new AppError(httpStatus.INTERNAL_SERVER_ERROR, "Error uploading image");
		}
		newImagesKeys.push(imageId);
	}

	// 🔹 Extract S3 keys for removal
	const imageKeysToDelete = imagesToRemove.map(extractS3Key) ?? [];
	const currentImages = currentVariant?.images.map(extractS3Key) ?? [];

	// 🔹 Delete images in parallel if any exist
	if (imageKeysToDelete.length) {
		await Promise.all(imageKeysToDelete.map(deleteFile));
	}

	// 🔹 Filter out removed images
	const updatedImages = currentImages.filter((image: string) => !imageKeysToDelete.includes(image)) ?? [];

	// 🔹 Update product images in DB using aggregation
	await Product.updateOne(
		{ _id: new Types.ObjectId(productId), "variants._id": new Types.ObjectId(variantId) },
		{ $set: { "variants.$.images": [...updatedImages, ...newImagesKeys] } }
	);

	// 🔹 Clear cache
	await RedisClient.del(`product:${productId}`);
	await RedisClient.del(`product:${product.slug}`);

	return "Product variant images updated successfully!";
};

const getBestDeals = async () => {
	// Get products with discount price (where discount price is less than original price)
	const products = await Product.find({
		discountPrice: { $exists: true, $ne: null, $gt: 0 },
		$expr: { $lt: ["$discountPrice", "$price"] }
	})
		.populate(ProductPopulateFields)
		.sort({ discountDifference: -1 })
		.limit(10);

	// Transform products to include discount percentage
	const finalProducts = products.map((product) => {
		const productObj = product.toObject();
		// Remove filterableFields from response
		delete productObj.filterableFields;
		return productObj;
	});

	return finalProducts;
};

const getBestSellers = async () => {
	// For a real implementation, you might want to track sales in an Order model
	// and use aggregation to find the most sold products
	// This is a simplified version that returns products with lowest stock (assuming they sell well)
	const products = await Product.find({ isActive: true, stock: { $gt: 0 } })
		.populate(ProductPopulateFields)
		.sort({ stock: 1 })
		.limit(10);

	const finalProducts = products.map((product) => {
		const productObj = product.toObject();
		// Remove filterableFields from response
		delete productObj.filterableFields;
		return productObj;
	});

	return finalProducts;
};

const ProductServices = {
	createProduct,
	getProductById,
	getProductBySlug,
	getProducts,
	getMinimalProducts,
	getProductsOfAnOffer,
	updateProduct,
	updateProductImages,
	deleteProduct,
	toggleProductStatus,

	// Variant Management
	createProductVariant,
	updateProductVariant,
	updateProductVariantImages,
	deleteProductVariant,

	// Best deals and sellers
	getBestDeals,
	getBestSellers
};

export default ProductServices;
