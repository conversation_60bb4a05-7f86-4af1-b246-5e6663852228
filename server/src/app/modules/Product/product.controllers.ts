import { Request, RequestHandler, Response } from "express";
import httpStatus from "http-status";
import { IFile } from "../../interface";
import catchAsync from "../../utils/catchAsync";
import sendResponse from "../../utils/sendResponse";
import ProductServices from "./product.services";

const createProduct: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const images = req.files as IFile[];
	const product = await ProductServices.createProduct(req.body, images);

	sendResponse(res, {
		statusCode: httpStatus.CREATED,
		success: true,
		message: "Product created successfully!",
		data: product
	});
});

const getProducts: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const { products, meta } = await ProductServices.getProducts(req.query);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		message: "Products fetched successfully!",
		success: true,
		meta,
		data: products
	});
});

const getMinimalProducts: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const { products, meta } = await ProductServices.getMinimalProducts(req.query);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		message: "Products fetched successfully!",
		success: true,
		meta,
		data: products
	});
});

const getProductsOfAnOffer: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const { products, meta } = await ProductServices.getProductsOfAnOffer(req.params.slug, req.query);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		message: "Offer products fetched successfully!",
		success: true,
		meta,
		data: products
	});
});

const getProductById: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const product = await ProductServices.getProductById(req.params.id);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		success: true,
		message: "Product fetched successfully!",
		data: product
	});
});

const getProductBySlug: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const product = await ProductServices.getProductBySlug(req.params.slug);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		success: true,
		message: "Product fetched successfully!",
		data: product
	});
});

const updateProduct: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const product = await ProductServices.updateProduct(req.params.id, req.body);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		success: true,
		message: "Product updated successfully!",
		data: product
	});
});

const updateProductImages: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const newImages = req.files as IFile[];
	const product = await ProductServices.updateProductImages(req.params.id, req.body.imagesToRemove, newImages);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		success: true,
		message: "Product images updated successfully!",
		data: product
	});
});

const deleteProduct: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	await ProductServices.deleteProduct(req.params.id);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		success: true,
		message: "Product deleted successfully!",
		data: null
	});
});

const toggleProductStatus: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const product = await ProductServices.toggleProductStatus(req.params.id);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		success: true,
		message: "Product status updated successfully!",
		data: product
	});
});

/**
 * Variant Management
 */

const createProductVariant: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const images = req.files as IFile[];
	const product = await ProductServices.createProductVariant(req.params.productId, req.body, images);

	sendResponse(res, {
		statusCode: httpStatus.CREATED,
		success: true,
		message: "Product variant created successfully!",
		data: product
	});
});

const updateProductVariant: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const product = await ProductServices.updateProductVariant(req.params.productId, req.params.variantId, req.body);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		success: true,
		message: "Product variant updated successfully!",
		data: product
	});
});

const updateProductVariantImages: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const newImages = req.files as IFile[];
	const product = await ProductServices.updateProductVariantImages(
		req.params.productId,
		req.params.variantId,
		req.body.imagesToRemove,
		newImages
	);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		success: true,
		message: "Product variant images updated successfully!",
		data: product
	});
});

const deleteProductVariant: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const product = await ProductServices.deleteProductVariant(req.params.productId, req.params.variantId);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		success: true,
		message: "Product variant deleted successfully!",
		data: product
	});
});

const getBestDeals: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const products = await ProductServices.getBestDeals();

	sendResponse(res, {
		statusCode: httpStatus.OK,
		success: true,
		message: "Best deals fetched successfully!",
		data: products
	});
});

const getBestSellers: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const products = await ProductServices.getBestSellers();

	sendResponse(res, {
		statusCode: httpStatus.OK,
		success: true,
		message: "Best sellers fetched successfully!",
		data: products
	});
});

const ProductControllers = {
	createProduct,
	getProducts,
	getMinimalProducts,
	getProductsOfAnOffer,
	getProductById,
	getProductBySlug,
	updateProduct,
	updateProductImages,
	deleteProduct,
	toggleProductStatus,

	createProductVariant,
	updateProductVariant,
	updateProductVariantImages,
	deleteProductVariant,

	getBestDeals,
	getBestSellers
};

export default ProductControllers;
