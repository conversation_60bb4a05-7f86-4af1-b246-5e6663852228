import mongoose from "mongoose";
import { z } from "zod";

const productCreateValidation = z.object({
	body: z.object({
		name: z
			.string({
				required_error: "Name is required!",
				invalid_type_error: "Name must be a string!"
			})
			.min(3, "Name must be at least 3 characters long!")
			.max(255, "Name must be at most 255 characters long!"),

		price: z
			.number({
				required_error: "Price is required!",
				invalid_type_error: "Price must be a number!"
			})
			.nonnegative("Price must be a non-negative number!"),

		actualPrice: z
			.number({
				invalid_type_error: "Discount price must be a number!"
			})
			.nonnegative("Discount price must be a non-negative number!")
			.optional(),

		brand: z
			.string({
				required_error: "Brand is required!"
			})
			.refine((val) => mongoose.Types.ObjectId.isValid(val), {
				message: "Invalid brand id!"
			}),

		description: z.string({
			invalid_type_error: "Description must be a string!"
		}),

		warrantyInfo: z.string().optional(),

		primaryCategory: z
			.string({
				required_error: "Primary category is required!"
			})
			.refine((val) => mongoose.Types.ObjectId.isValid(val), {
				message: "Invalid primary category id!"
			}),

		secondaryCategory: z
			.string({
				required_error: "Secondary category is required!"
			})
			.refine((val) => mongoose.Types.ObjectId.isValid(val), {
				message: "Invalid secondary category id!"
			}),

		tertiaryCategory: z
			.string()
			.refine((val) => mongoose.Types.ObjectId.isValid(val), {
				message: "Invalid tertiary category id!"
			})
			.transform((val) => (val?.trim() === "" ? undefined : val))
			.optional(),

		stock: z
			.number({
				required_error: "Stock is required!",
				invalid_type_error: "Stock must be a number!"
			})
			.nonnegative("Stock must be a non-negative number!"),

		additionalInfo: z.array(
			z.object({
				key: z.string({
					required_error: "Additional info key is required!",
					invalid_type_error: "Additional info key must be a string!"
				}),
				value: z.string({
					required_error: "Additional info value is required!",
					invalid_type_error: "Additional info value must be a string!"
				}),
				description: z
					.string({
						required_error: "Additional info description is required!",
						invalid_type_error: "Additional info description must be a string!"
					})
					.optional()
			})
		),
		filterableFields: z.array(
			z.string().refine((val) => mongoose.Types.ObjectId.isValid(val), {
				message: "Invalid filterable field id!"
			})
		)
	})
});

const variantSchemaValidation = z.object({
	body: z.object({
		color: z
			.object({
				name: z.string({
					invalid_type_error: "Color name must be a string!"
				}),
				value: z.string({
					invalid_type_error: "Color value must be a string!"
				})
			})
			.optional(),

		storage: z
			.string({
				required_error: "Storage is required!",
				invalid_type_error: "Storage must be a string!"
			})
			.optional(),

		price: z
			.number({
				required_error: "Variant price is required!",
				invalid_type_error: "Variant price must be a number!"
			})
			.nonnegative("Variant price must be a non-negative number!"),

		stock: z
			.number({
				required_error: "Variant stock is required!",
				invalid_type_error: "Variant stock must be a number!"
			})
			.nonnegative("Variant stock must be a non-negative number!"),

		region: z.string().optional(),
		condition: z.string().optional()
	})
});

const updateImagesValidation = z.object({
	body: z.object({
		imagesToRemove: z
			.array(
				z.string().url({
					message: "Invalid image url to remove!"
				}),
				{
					message: "Images to remove must be an array of strings!"
				}
			)
			.optional()
	})
});

const productValidations = {
	productCreateValidation,
	variantSchemaValidation,
	updateImagesValidation
};

export default productValidations;
