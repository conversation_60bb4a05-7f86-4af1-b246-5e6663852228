import { Request, Response } from "express";
import httpStatus from "http-status";
import sendResponse from "../../utils/sendResponse";
import otpServices from "./otp.services";

const resendOTP = async (req: Request, res: Response) => {
	const { phone } = req.body;

	const response = await otpServices.createOtp(phone);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		success: true,
		message: "OTP sent successfully!",
		data: response
	});
};

const verifyOTP = async (req: Request, res: Response) => {
	const { phone, code } = req.body;

	const response = await otpServices.verifyOtp(phone, code);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		success: true,
		message: "OTP verified successfully!",
		data: response
	});
};

const otpControllers = {
	resendOTP,
	verifyOTP
};

export default otpControllers;
