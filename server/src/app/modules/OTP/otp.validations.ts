import { z } from "zod";

const otpValidation = z.object({
	body: z.object({
		phone: z
			.string({
				invalid_type_error: "Phone number must be a string!",
				required_error: "Phone number is required!"
			})
			.regex(/^(?:\+8801|01)[3-9]\d{8}$/, {
				message: "Phone number must be a valid Bangladeshi number (e.g., +8801XXXXXXXX or 01XXXXXXXX)!"
			})
	})
});

const verifyOtpValidation = z.object({
	body: z.object({
		phone: z
			.string({
				invalid_type_error: "Phone number must be a string!",
				required_error: "Phone number is required!"
			})
			.regex(/^(?:\+8801|01)[3-9]\d{8}$/, {
				message: "Phone number must be a valid Bangladeshi number (e.g., +8801XXXXXXXX or 01XXXXXXXX)!"
			}),
		code: z
			.string({
				invalid_type_error: "Otp must be a string!",
				required_error: "Otp is required!"
			})
			.min(6, {
				message: "Otp must be at least 6 characters long!"
			})
			.max(6, {
				message: "Otp must be at most 6 characters long!"
			})
	})
});

const OTPValidations = {
	otpValidation,
	verifyOtpValidation
};

export default OTPValidations;
