import express from "express";
import validateRequest from "../../middlewares/validateRequest";
import otpControllers from "./otp.controllers";
import OTPValidations from "./otp.validations";

const router = express.Router();

router.post("/resend", validateRequest(OTPValidations.otpValidation), otpControllers.resendOTP);
router.post("/verify", validateRequest(OTPValidations.verifyOtpValidation), otpControllers.verifyOTP);
const otpRoutes = router;

export default otpRoutes;
