import httpStatus from "http-status";
import QueryBuilder from "../../builder/QueryBuilder";
import AppError from "../../errors/AppError";
import Product from "../Product/product.model";
import ProductField from "./productField.model";
import { IProductField } from "./productField.types";

const createProductField = async (productField: Partial<IProductField>) => {
	const res = await ProductField.create(productField);
	return res;
};

const getProductField = async (id: string) => {
	const res = await ProductField.findById(id);
	return res;
};

const getProductFields = async (query: Record<string, unknown>) => {
	const fieldsQuery = (await new QueryBuilder(ProductField.find(), query).search(["name"]).filter()).paginate().sort();
	const result = await fieldsQuery.modelQuery;
	const meta = await fieldsQuery.countTotal();
	return { result, meta };
};

const updateProductField = async (id: string, productField: IProductField) => {
	await getProductField(id);
	const res = await ProductField.findByIdAndUpdate(id, productField, { new: true });
	return res;
};

const deleteProductField = async (id: string) => {
	await getProductField(id);

	const products = await Product.find({
		filterableFields: { $elemMatch: { fieldId: id } }
	});

	if (products.length > 0) {
		throw new AppError(httpStatus.CONFLICT, "Cannot delete field because it is being used in some products.");
	}

	const res = await ProductField.findByIdAndDelete(id);
	return res;
};

const ProductFieldServices = {
	createProductField,
	getProductField,
	getProductFields,
	updateProductField,
	deleteProductField
};

export default ProductFieldServices;
