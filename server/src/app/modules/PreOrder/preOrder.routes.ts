import express from "express";
// import { userRoles } from "../../constant";
// import AuthGuard from "../../middlewares/AuthGuard";
import validateRequest from "../../middlewares/validateRequest";
import { dataParser } from "../../utils";
import upload from "../../utils/awsFileManager";
import PreOrderControllers from "./preOrder.controllers";
import PreOrderValidations from "./preOrder.validation";

const router = express.Router();

router.post(
	"/create",
	// AuthGuard(userRoles.ADMIN),
	upload.single("image"),
	dataParser(),
	validateRequest(PreOrderValidations.PreOrderCreateValidation),
	PreOrderControllers.createPreOrder
);

router.get("/", PreOrderControllers.getPreOrders);

const PreOrderRoutes = router;

export default PreOrderRoutes;
