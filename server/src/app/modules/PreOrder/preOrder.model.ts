import { model, Schema } from "mongoose";
import { deleteFile, updateImageUrl } from "../../utils/awsFileManager";
import { IPreOrder } from "./preOrder.types";

const PreOrderSchema = new Schema<IPreOrder>(
	{
		productInformation: {
			type: String,
			required: true
		},
		image: {
			type: String
		},
		name: {
			type: String,
			required: true
		},
		phone: {
			type: String,
			required: true
		},
		email: {
			type: String,
			required: true
		},
		address: {
			type: String,
			required: true
		},
		agreeToTerms: {
			type: Boolean,
			default: false
		}
	},
	{
		timestamps: true,
		toJSON: {
			virtuals: true
		}
	}
);

PreOrderSchema.pre("findOneAndUpdate", async function (next) {
	const update = this.getUpdate() as Partial<IPreOrder>;

	// delete old image from s3 if new image is uploaded
	if (update.image) {
		const category = await this.model.findOne(this.getFilter());
		await deleteFile(category.image);
	}

	next();
});

PreOrderSchema.post<IPreOrder>("find", async function (result) {
	await updateImageUrl(result);
});

// delete image from s3 on delete
PreOrderSchema.post<IPreOrder>("findOneAndDelete", async function (res: IPreOrder) {
	if (res?.image) {
		await deleteFile(res?.image);
	}
});

const PreOrder = model<IPreOrder>("PreOrder", PreOrderSchema);

export default PreOrder;
