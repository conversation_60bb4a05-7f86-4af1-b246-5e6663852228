import { Request, RequestHandler, Response } from "express";
import httpStatus from "http-status";
import { IFile } from "../../interface";
import catchAsync from "../../utils/catchAsync";
import sendResponse from "../../utils/sendResponse";
import PreOrderServices from "./preOrder.services";

const createPreOrder: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const image = req.file;
	const preOrder = await PreOrderServices.createPreOrder(req.body, image as IFile);

	sendResponse(res, {
		statusCode: httpStatus.CREATED,
		success: true,
		message: "Pre order created successfully!",
		data: preOrder
	});
});

const getPreOrders: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const { preOrders, meta } = await PreOrderServices.getPreOrders(req.query);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		success: true,
		message: "Pre orders fetched successfully!",
		meta,
		data: preOrders
	});
});

const PreOrderControllers = {
	createPreOrder,
	getPreOrders
};

export default PreOrderControllers;
