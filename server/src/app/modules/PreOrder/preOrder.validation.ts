import { z } from "zod";

const PreOrderCreateValidation = z.object({
	body: z.object({
		productInformation: z
			.string({
				required_error: "Product information is required!",
				invalid_type_error: "Product information must be a string!"
			})
			.min(3, {
				message: "Product information must be at least 3 characters!"
			}),
		phone: z
			.string({
				required_error: "Phone is required!",
				invalid_type_error: "Phone must be a string!"
			})
			.min(3, {
				message: "Phone must be at least 3 characters!"
			}),
		email: z
			.string({
				required_error: "Email is required!",
				invalid_type_error: "Email must be a string!"
			})
			.min(3, {
				message: "Email must be at least 3 characters!"
			}),
		name: z
			.string({
				required_error: "Name is required!",
				invalid_type_error: "Name must be a string!"
			})
			.min(3, {
				message: "Name must be at least 3 characters!"
			}),
		address: z
			.string({
				required_error: "Address is required!",
				invalid_type_error: "Address must be a string!"
			})
			.min(3, {
				message: "Address must be at least 3 characters!"
			}),
		agreeToTerms: z
			.boolean({
				required_error: "Agree to terms is required!",
				invalid_type_error: "Agree to terms must be a boolean!"
			})
			.optional()
	})
});

const PreOrderUpdateValidation = z.object({
	body: z.object({
		name: z
			.string({
				required_error: "Name is required!",
				invalid_type_error: "Name must be a string!"
			})
			.min(3, {
				message: "Name must be at least 3 characters!"
			})
			.optional(),
		productInformation: z
			.string({
				required_error: "Product information is required!",
				invalid_type_error: "Product information must be a string!"
			})
			.min(3, {
				message: "Product information must be at least 3 characters!"
			})
			.optional(),
		phone: z
			.string({
				required_error: "Phone is required!",
				invalid_type_error: "Phone must be a string!"
			})
			.min(3, {
				message: "Phone must be at least 3 characters!"
			})
			.optional(),
		email: z
			.string({
				required_error: "Email is required!",
				invalid_type_error: "Email must be a string!"
			})
			.min(3, {
				message: "Email must be at least 3 characters!"
			})
			.optional(),
		address: z
			.string({
				required_error: "Address is required!",
				invalid_type_error: "Address must be a string!"
			})
			.min(3, {
				message: "Address must be at least 3 characters!"
			})
			.optional(),
		agreeToTerms: z
			.boolean({
				required_error: "Agree to terms is required!",
				invalid_type_error: "Agree to terms must be a boolean!"
			})
			.optional()
	})
});

const PreOrderValidations = {
	PreOrderCreateValidation,
	PreOrderUpdateValidation
};

export default PreOrderValidations;
