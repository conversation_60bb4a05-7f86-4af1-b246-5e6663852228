import QueryBuilder from "../../builder/QueryBuilder";
import { imageFolders } from "../../constant";
import { IFile } from "../../interface";
import { createFileId } from "../../utils";
import { uploadFile } from "../../utils/awsFileManager";
import PreOrder from "./preOrder.model";
import { IPreOrder } from "./preOrder.types";

const createPreOrder = async (preOrder: IPreOrder, file?: IFile) => {
	let image;

	if (file) {
		try {
			image = createFileId(imageFolders.PRODUCTS, preOrder.name);

			const uploadedImage = await uploadFile(file, image);
			if (!uploadedImage) {
				image = undefined; // or set a default fallback image path here
			}
		} catch (error) {
			image = undefined; // fail silently and continue
		}
	}

	await PreOrder.create({ ...preOrder, ...(image && { image }) });
	return null;
};

const getPreOrders = async (query: Record<string, unknown>) => {
	const preOrderQuery = (await new QueryBuilder(PreOrder.find(), query).search(["name"]).filter())
		.fields()
		.paginate()
		.sort();

	const preOrders = await preOrderQuery.modelQuery;
	const meta = await preOrderQuery.countTotal();

	return { preOrders, meta };
};

const PreOrderServices = {
	createPreOrder,
	getPreOrders
};

export default PreOrderServices;
