import httpStatus from "http-status";
import mongoose from "mongoose";
import QueryBuilder from "../../builder/QueryBuilder";
import { imageFolders } from "../../constant";
import AppError from "../../errors/AppError";
import { IFile } from "../../interface";
import { createFileId } from "../../utils";
import { deleteFile, uploadFile } from "../../utils/awsFileManager";
import TertiaryCategory from "../TertiaryCategory/tertiaryCategory.model";
import SecondaryCategory from "./secondaryCategory.model";
import { ISecondaryCategory } from "./secondaryCategory.types";

const createSecondaryCategory = async (category: ISecondaryCategory, file: IFile) => {
	if (!file) {
		throw new AppError(httpStatus.BAD_REQUEST, "Image is required!");
	}

	const imageKey = createFileId(imageFolders.CATEGORIES, category.name);

	const uploadedImage = await uploadFile(file, imageKey);
	if (!uploadedImage) {
		throw new AppError(httpStatus.INTERNAL_SERVER_ERROR, "Error uploading image!");
	}

	const newCategory = await SecondaryCategory.create({ ...category, image: imageKey });
	return newCategory;
};

const getSecondaryCategory = async (id: string) => {
	const res = await SecondaryCategory.findById(id).populate("tertiaryCategoryCount productCount");
	if (!res) {
		throw new AppError(httpStatus.NOT_FOUND, "Secondary category not found");
	}
	return res;
};

const getSecondaryCategoryBySlug = async (slug: string) => {
	const res = await SecondaryCategory.findOne({ slug }).populate("tertiaryCategoryCount productCount");

	if (!res) {
		throw new AppError(httpStatus.NOT_FOUND, "Secondary category not found");
	}

	return res;
};

const getSecondaryCategories = async (query: Record<string, unknown>) => {
	const categoryQuery = (
		await new QueryBuilder(SecondaryCategory.find().populate("tertiaryCategoryCount productCount"), query)
			.search(["name"])
			.filter()
	)
		.fields()
		.paginate()
		.sort();

	const categories = await categoryQuery.modelQuery;
	const meta = await categoryQuery.countTotal();

	return { categories, meta };
};

const updateSecondaryCategory = async (id: string, data: ISecondaryCategory) => {
	await getSecondaryCategory(id);

	const res = await SecondaryCategory.findByIdAndUpdate(id, data, { new: true });
	return res;
};

const updateSecondaryCategoryImage = async (id: string, file: IFile) => {
	//	check if category exists
	const category = await getSecondaryCategory(id);

	if (!file) {
		throw new AppError(httpStatus.BAD_REQUEST, "Image is required!");
	}

	const image = createFileId(imageFolders.CATEGORIES, category.name);

	const uploadedImage = await uploadFile(file, image);
	if (!uploadedImage) {
		throw new AppError(httpStatus.INTERNAL_SERVER_ERROR, "Error uploading image!");
	}

	const updatedCategory = await SecondaryCategory.findByIdAndUpdate(id, { image }, { new: true });

	return updatedCategory;
};

const deleteSecondaryCategory = async (id: string) => {
	await getSecondaryCategory(id);

	const session = await mongoose.startSession();
	try {
		session.startTransaction();

		// Find and delete related tertiary categories
		const tertiaryCategories = await TertiaryCategory.find({ secondaryCategory: id }).session(session);
		for (const category of tertiaryCategories) {
			if (category.image) {
				await deleteFile(category.image); // Delete associated AWS images
			}
		}
		await TertiaryCategory.deleteMany({ secondaryCategory: id }, { session });

		const deletedCategory = await SecondaryCategory.findByIdAndDelete(id).session(session);

		await session.commitTransaction();
		await session.endSession();

		return deletedCategory;
	} catch (error) {
		await session.abortTransaction();
		await session.endSession();

		throw new AppError(httpStatus.INTERNAL_SERVER_ERROR, "Error deleting category");
	}
};

const toggleFeatured = async (id: string) => {
	const category = await getSecondaryCategory(id);
	category.isFeatured = !category.isFeatured;
	await category.save();
	return category;
};

const SecondaryCategoryServices = {
	createSecondaryCategory,
	getSecondaryCategory,
	getSecondaryCategoryBySlug,
	getSecondaryCategories,
	updateSecondaryCategory,
	updateSecondaryCategoryImage,
	deleteSecondaryCategory,
	toggleFeatured
};

export default SecondaryCategoryServices;
