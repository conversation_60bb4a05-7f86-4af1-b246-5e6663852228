import { z } from "zod";

const adValidation = z.object({
	body: z.object({
		title: z
			.string({
				required_error: "Title is required!",
				invalid_type_error: "Title must be a string!"
			})
			.min(3, "Title must be at least 3 characters long!"),
		description: z
			.string({
				required_error: "Description is required!",
				invalid_type_error: "Description must be a string!"
			})
			.min(3, "Description must be at least 3 characters long!"),
		category: z
			.string({
				required_error: "Category is required!",
				invalid_type_error: "Category must be a string!"
			})
			.min(3, "Category must be at least 3 characters long!"),
		condition: z
			.string({
				required_error: "Condition is required!",
				invalid_type_error: "Condition must be a string!"
			})
			.min(3, "Condition must be at least 3 characters long!"),
		location: z
			.string({
				required_error: "Location is required!",
				invalid_type_error: "Location must be a string!"
			})
			.min(3, "Location must be at least 3 characters long!"),
		images: z.array(z.string()),
		user: z
			.string({
				required_error: "User is required!",
				invalid_type_error: "User must be a string!"
			})
			.optional(),
		price: z.number({
			required_error: "Price is required!",
			invalid_type_error: "Price must be a number!"
		})
	})
});

export default adValidation;
