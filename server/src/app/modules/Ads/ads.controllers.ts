import { Request, RequestHandler, Response } from "express";
import httpStatus from "http-status";
import catchAsync from "../../utils/catchAsync";
import sendResponse from "../../utils/sendResponse";
import AdServices from "./ads.services";

const createAd: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const data = { ...req.body, user: req.userData?._id };
	const ad = await AdServices.createAd(data);

	sendResponse(res, {
		statusCode: httpStatus.CREATED,
		success: true,
		message: "Ad created successfully!",
		data: ad
	});
});

const getAd: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const ad = await AdServices.getAd(req.params.id);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		success: true,
		message: "Ad fetched successfully!",
		data: ad
	});
});

const getAllAds: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const { result, meta } = await AdServices.getAllAds(req.query);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		success: true,
		meta,
		message: "Ads fetched successfully!",
		data: result
	});
});

const getMyAds: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const ads = await AdServices.getMyAds(req.userData?._id);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		success: true,
		message: "Ads fetched successfully!",
		data: ads
	});
});

const getFeaturedAds: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const ads = await AdServices.getFeaturedAds();

	sendResponse(res, {
		statusCode: httpStatus.OK,
		success: true,
		message: "Featured ads fetched successfully!",
		data: ads
	});
});

const getRecentAds: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const ads = await AdServices.getRecentAds();

	sendResponse(res, {
		statusCode: httpStatus.OK,
		success: true,
		message: "Recent ads fetched successfully!",
		data: ads
	});
});

const updateAd: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const ad = await AdServices.updateAd(req.params.id, req.body);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		success: true,
		message: "Ad updated successfully!",
		data: ad
	});
});

const deleteAd: RequestHandler = catchAsync(async (req: Request, res: Response) => {
	const ad = await AdServices.deleteAd(req.params.id);

	sendResponse(res, {
		statusCode: httpStatus.OK,
		success: true,
		message: "Ad deleted successfully!",
		data: ad
	});
});

const AdControllers = {
	createAd,
	getAllAds,
	getAd,
	getMyAds,
	getFeaturedAds,
	getRecentAds,
	updateAd,
	deleteAd
};

export default AdControllers;
