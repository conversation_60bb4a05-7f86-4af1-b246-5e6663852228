import QueryBuilder from "../../builder/QueryBuilder";
import Ad from "./ads.model";
import { IAd } from "./ads.types";

const createAd = async (ad: Partial<IAd>) => {
	const res = await Ad.create(ad);
	return res;
};

const getAd = async (id: string) => {
	const res = await Ad.findById(id).populate({
		path: "user",
		select: "name phone createdAt"
	});
	return res;
};

const getAllAds = async (query: Record<string, unknown>) => {
	const adsQuery = (
		await new QueryBuilder(
			Ad.find().populate({
				path: "user",
				select: "name phone"
			}),
			query
		)
			.search(["name"])
			.filter()
	)
		.paginate()
		.sort();
	const result = await adsQuery.modelQuery;
	const meta = await adsQuery.countTotal();
	return { result, meta };
};

const getMyAds = async (user: string) => {
	const res = await Ad.find({ user });
	return res;
};

const getFeaturedAds = async () => {
	const res = await Ad.find({}).sort({ createdAt: -1 }).limit(4);
	return res;
};

const getRecentAds = async () => {
	const res = await Ad.find({}).sort({ createdAt: -1 }).limit(4);
	return res;
};

const updateAd = async (id: string, ad: IAd) => {
	await getAd(id);
	const res = await Ad.findByIdAndUpdate(id, ad, { new: true });
	return res;
};

const deleteAd = async (id: string) => {
	const res = await Ad.findByIdAndDelete(id);
	return res;
};

const AdServices = {
	createAd,
	getAd,
	getAllAds,
	getMyAds,
	getFeaturedAds,
	getRecentAds,
	updateAd,
	deleteAd
};

export default AdServices;
