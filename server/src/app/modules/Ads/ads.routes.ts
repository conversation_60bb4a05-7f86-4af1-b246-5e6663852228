import express from "express";
import { userRoles } from "../../constant";
import AuthGuard from "../../middlewares/AuthGuard";
import validateRequest from "../../middlewares/validateRequest";
import AdControllers from "./ads.controllers";
import adValidation from "./ads.validations";

const router = express.Router();

router.post("/create", AuthGuard(userRoles.USER), validateRequest(adValidation), AdControllers.createAd);

router.get("/", AdControllers.getAllAds);

router.get("/my-ads", AuthGuard(userRoles.USER), AdControllers.getMyAds);

router.get("/featured", AuthGuard(userRoles.USER), AdControllers.getFeaturedAds);

router.get("/recent", AuthGuard(userRoles.USER), AdControllers.getRecentAds);

router.get("/:id", AuthGuard(userRoles.USER), AdControllers.getAd);

router.put("/:id", AuthGuard(userRoles.USER), validateRequest(adValidation), AdControllers.updateAd);

router.delete("/:id", AuthGuard(userRoles.USER), AdControllers.deleteAd);

const AdRoutes = router;

export default AdRoutes;
