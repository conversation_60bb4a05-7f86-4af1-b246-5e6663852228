import { model, Schema } from "mongoose";
import { CreateSlug } from "../../utils/CreateSlug";
import { IAd } from "./ads.types";

const adSchema = new Schema<IAd>(
	{
		user: { type: Schema.Types.ObjectId, ref: "User", required: true },
		title: { type: String, required: true },
		slug: { type: String, unique: true },
		description: { type: String, required: true },
		category: { type: String, required: true },
		condition: { type: String, required: true },
		price: { type: Number, required: true },
		images: { type: [String], required: true },
		location: { type: String, required: true }
	},
	{
		timestamps: true
	}
);

adSchema.pre<IAd>("save", async function (next) {
	if (this.title) {
		this.slug = CreateSlug(this.title);
	}
	next();
});

// pre update middleware to create slug from name
adSchema.pre("findOneAndUpdate", async function (next) {
	const update = this.getUpdate() as Partial<IAd>;
	if (update.title) {
		update.slug = CreateSlug(update.title);
		this.setUpdate(update);
	}
	next();
});

const Ad = model<IAd>("Ad", adSchema);

export default Ad;
