import express from "express";
import AdminRoutes from "../modules/Admin/admin.routes";
import BannerRoutes from "../modules/Banner/banner.routes";
import BrandRoutes from "../modules/Brand/brand.routes";
import OfferRoutes from "../modules/Offer/offer.routes";
import OrderRoutes from "../modules/Order/order.routes";
import otpRoutes from "../modules/OTP/otp.routes";
import PreOrderRoutes from "../modules/PreOrder/preOrder.routes";
import PrimaryCategoryRoutes from "../modules/PrimaryCategory/primaryCategory.routes";
import ProductRoutes from "../modules/Product/product.routes";
import ProductFieldRoutes from "../modules/ProductField/productField.routes";
import SecondaryCategoryRoutes from "../modules/SecondaryCategory/secondaryCategory.routes";
import TertiaryCategoryRoutes from "../modules/TertiaryCategory/tertiaryCategory.routes";
import TestRoutes from "../modules/Test/test.routes";
import userRoutes from "../modules/User/user.routes";
import AdRoutes from "../modules/Ads/ads.routes";
const router = express.Router();

const moduleRoutes = [
	{
		path: "/admins",
		route: AdminRoutes
	},
	{
		path: "/users",
		route: userRoutes
	},
	{
		path: "/otps",
		route: otpRoutes
	},
	{
		path: "/primary-categories",
		route: PrimaryCategoryRoutes
	},
	{
		path: "/secondary-categories",
		route: SecondaryCategoryRoutes
	},
	{
		path: "/tertiary-categories",
		route: TertiaryCategoryRoutes
	},
	{
		path: "/brands",
		route: BrandRoutes
	},
	{
		path: "/product-fields",
		route: ProductFieldRoutes
	},
	{
		path: "/products",
		route: ProductRoutes
	},
	{
		path: "/test",
		route: TestRoutes
	},
	{
		path: "/banners",
		route: BannerRoutes
	},
	{
		path: "/orders",
		route: OrderRoutes
	},
	{
		path: "/pre-orders",
		route: PreOrderRoutes
	},
	{
		path: "/offers",
		route: OfferRoutes
	},
	{
		path: "/ads",
		route: AdRoutes
	}
];

moduleRoutes.forEach((route) => router.use(route.path, route.route));

export default router;
