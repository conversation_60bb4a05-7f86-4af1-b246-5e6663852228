import AWS from "aws-sdk";
import config from "../config";

AWS.config.update({
	accessKeyId: config.aws_access_key_id,
	secretAccessKey: config.aws_secret_access_key,
	region: config.aws_region
});

const s3 = new AWS.S3();

interface File {
	fileName: string;
	contentType: string;
}

const generateSignedUrls = async (files: File[], folder: string = ""): Promise<string[]> => {
	if (!Array.isArray(files) || files.length === 0) {
		throw new Error("Files array is required and should not be empty.");
	}

	try {
		// Generate pre-signed URLs for each file
		const signedUrls: string[] = await Promise.all(
			files.map((file: File) => {
				const params: AWS.S3.Types.PutObjectRequest = {
					Bucket: config.aws_bucket_name,
					Key: `${folder}/${file.fileName}`, // File path in the bucket
					Expires: new Date(Date.now() + 6 * 60 * 60), // URL expiration time in milliseconds (6 hours)
					ContentType: file.contentType // MIME type of the file
				};

				return s3.getSignedUrlPromise("putObject", params);
			})
		);

		return signedUrls;
	} catch (error) {
		console.error("Error generating pre-signed URLs:", error);
		throw error;
	}
};

export default generateSignedUrls;
