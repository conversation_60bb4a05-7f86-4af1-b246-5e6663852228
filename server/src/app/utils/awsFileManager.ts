import { DeleteObjectCommand, GetObjectCommand, PutObjectCommand, S3Client } from "@aws-sdk/client-s3";
import { getSignedUrl } from "@aws-sdk/s3-request-presigner";
import httpStatus from "http-status";
import multer from "multer";
import config from "../config";
import AppError from "../errors/AppError";
import { IFile } from "../interface";
import { IProductVariant } from "../modules/Product/product.type";

// Initialize S3 Client
const s3 = new S3Client({
	credentials: {
		accessKeyId: config.aws_access_key_id,
		secretAccessKey: config.aws_secret_access_key
	},
	region: config.aws_region
});

// Configure Multer Storage
const storage = multer.memoryStorage();

// File Upload Middleware
const upload = multer({
	storage,
	limits: {
		fileSize: 5 * 1024 * 1024 // Limit file size to 5 MB
	},
	fileFilter: (req, file, cb) => {
		if (file.mimetype.startsWith("image/")) {
			cb(null, true);
		} else {
			cb(new AppError(httpStatus.INTERNAL_SERVER_ERROR, "Invalid file type. Only images are allowed."));
		}
	}
});

export const uploadFile = async (file: IFile, key: string): Promise<object> => {
	if (!file) {
		throw new Error("File is required for upload.");
	}

	const params = {
		Bucket: config.aws_bucket_name,
		Key: key,
		Body: file.buffer,
		ContentType: file.mimetype
	};

	try {
		const command = new PutObjectCommand(params);
		const data = await s3.send(command);
		return data;
	} catch (error: any) {
		throw new AppError(httpStatus.INTERNAL_SERVER_ERROR, "Error uploading file.");
	}
};

export const deleteFile = async (key: string): Promise<object> => {
	const params = {
		Bucket: config.aws_bucket_name,
		Key: key
	};

	try {
		const command = new DeleteObjectCommand(params);
		const data = await s3.send(command);
		return data;
	} catch (error: any) {
		throw new AppError(httpStatus.INTERNAL_SERVER_ERROR, "Error deleting file.");
	}
};

export const getSignedUrlForFile = async (key: string): Promise<string> => {
	const signedUrl = await getSignedUrl(s3, new GetObjectCommand({ Bucket: config.aws_bucket_name, Key: key }), {
		expiresIn: 172800 // 2 hour
	});
	return signedUrl;
};

export const updateImageUrl = async (result: Record<string, unknown>) => {
	if (Array.isArray(result)) {
		// Handle multiple documents
		await Promise.all(
			result.map(async (item) => {
				// Handle single image
				if (item.image) {
					// Make sure we get { key, url } and assign it
					item.image = await getSignedUrlForFile(item.image as string);
				}

				// Handle multiple images
				if (item.images && Array.isArray(item.images)) {
					// Map each image to { key, url }
					item.images = await Promise.all(item.images.map((image: string) => getSignedUrlForFile(image as string)));
				}

				// handle product variants images
				if (item.variants && Array.isArray(item.variants)) {
					item.variants = await Promise.all(
						item.variants.map(async (variant: IProductVariant) => {
							if (variant.images && Array.isArray(variant.images)) {
								variant.images = await Promise.all(variant.images.map((image: string) => getSignedUrlForFile(image)));
							}
							return variant;
						})
					);
				}
				if (item.profilePicture) {
					item.profilePicture = await getSignedUrlForFile(item.profilePicture as string);
				}
				if (item.banner) {
					item.banner = await getSignedUrlForFile(item.banner as string);
				}
			})
		);
	} else if (result) {
		// Handle single document
		if (result.image) {
			if (typeof result.image === "string") {
				result.image = await getSignedUrlForFile(result.image);
			}
		}

		// Handle multiple images
		if (result.images && Array.isArray(result.images)) {
			result.images = await Promise.all(result.images.map((image: string) => getSignedUrlForFile(image)));
		}

		// handle product variants images
		if (result.variants && Array.isArray(result.variants)) {
			result.variants = await Promise.all(
				result.variants.map(async (variant: IProductVariant) => {
					if (variant.images && Array.isArray(variant.images)) {
						variant.images = await Promise.all(variant.images.map((image: string) => getSignedUrlForFile(image)));
					}
					return variant;
				})
			);
		}
	}
};

export const extractS3Key = (s3Url: string): string => {
	try {
		// Extract the path after the bucket domain
		const url = new URL(s3Url);
		const keyWithParams = url.pathname.substring(1); // Remove leading "/"

		// Remove any query parameters if present
		const key = keyWithParams.split("?")[0];

		return key;
	} catch (error) {
		console.log({ error, s3Url });
		throw new AppError(httpStatus.INTERNAL_SERVER_ERROR, "Invalid S3 URL.");
	}
};

export default upload;
