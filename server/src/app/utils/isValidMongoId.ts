/**
 * Checks if the given id is a valid MongoDB ObjectId.
 *
 * @param {string} id - The value to check.
 * @returns {boolean} - Returns true if id is a valid ObjectId, otherwise false.
 */

const isValidMongoId = (id: string): boolean => {
	// Check if id is a string and matches the 24-character hex pattern
	return typeof id === "string" && /^[a-fA-F0-9]{24}$/.test(id);
};

export default isValidMongoId;
