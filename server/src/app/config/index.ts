import dotenv from "dotenv";
import path from "path";

dotenv.config({ path: path.join(process.cwd(), ".env") });

export default {
	NODE_ENV: process.env.NODE_ENV,
	port: process.env.PORT,
	database_url: process.env.DATABASE_URL,
	redis_url: process.env.REDIS_URL as string,

	// Bcrypt
	bcrypt_salt_round: process.env.BCRYPT_SALT_ROUND,
	jwt_secret: process.env.JWT_SECRET as string,
	jwt_expiration: process.env.JWT_EXPIRATION as string,

	// Super Admin
	super_admin_email: process.env.SUPER_ADMIN_EMAIL as string,
	super_admin_password: process.env.SUPER_ADMIN_PASSWORD as string,

	// Email
	support_email: process.env.SUPPORT_EMAIL,
	support_email_password: process.env.SUPPORT_EMAIL_PASSWORD,

	// Password Reset
	password_reset_token: process.env.PASSWORD_RESET_TOKEN,
	password_reset_url_expires_in: process.env.PASSWORD_RESET_URL_EXPIRES_IN,

	// AWS
	aws_access_key_id: process.env.AWS_ACCESS_KEY_ID as string,
	aws_secret_access_key: process.env.AWS_SECRET_ACCESS_KEY as string,
	aws_region: process.env.AWS_REGION as string,
	aws_bucket_name: process.env.AWS_BUCKET_NAME as string,

	// SMS
	sms_api_url: process.env.SMS_API_URL as string,
	sms_api_key: process.env.SMS_API_KEY as string,
	sms_customer_id: process.env.SMS_CUSTOMER_ID as string
};
