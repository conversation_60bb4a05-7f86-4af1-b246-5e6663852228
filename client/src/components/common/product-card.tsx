"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { useToast } from "@/hooks/use-toast";
import { useCart } from "@/hooks/useCart";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import type React from "react";

interface ProductCardProps {
	_id: string;
	slug?: string;
	name: string;
	images: string[];
	price: string | number;
	actualPrice?: string | number;
}

export default function ProductCard({ _id, slug, name, images, price, actualPrice }: ProductCardProps) {
	const { addItem, cartItems } = useCart();
	const router = useRouter();
	const { toast } = useToast();

	// Handle Buy Now button click
	const handleBuyNow = (e: React.MouseEvent) => {
		e.stopPropagation();
		e.preventDefault();
		if (!_id) return;
		router.push(`/checkout?buyNow=${_id}&quantity=${1}`);
	};
	// Handle Add to Cart button click
	const handleAddToCart = (e: React.MouseEvent) => {
		e.stopPropagation();
		e.preventDefault();
		addItem({
			id: _id,
			quantity: 1
		});
	};

	const handelAddedToCart = () => {
		toast({
			title: "Item already in cart",
			description: "This item is already in your cart",
			variant: "destructive"
		});
	};
	// Calculate discount percentage if originalPrice exists
	const discountPercentage = actualPrice
		? Math.round(
				((Number.parseFloat(actualPrice?.toString()) - Number.parseFloat(price?.toString())) /
					Number.parseFloat(actualPrice?.toString())) *
					100
		  )
		: null;

	return (
		<div className='block h-full'>
			<Card className='overflow-hidden border-gray-100 h-full flex flex-col group transition-all duration-300 hover:shadow-md relative'>
				<Link href={`/product/${slug}`}>
					{/* Smaller discount percentage badge */}
					{discountPercentage && discountPercentage > 0 && (
						<div className='absolute top-1.5 left-1.5 bg-brand-dark-600 text-white text-[10px] py-0.5 px-1.5 z-10 font-medium rounded-sm'>
							{discountPercentage}% OFF
						</div>
					)}

					{/* Image container with subtle hover effect */}
					<div className='h-[160px] w-full flex items-center justify-center p-4 bg-white overflow-hidden'>
						<div className='relative w-full h-full transition-transform duration-500 group-hover:scale-105'>
							<Image
								src={images[0] || "/placeholder.svg"}
								alt={name}
								fill
								className='object-contain'
								sizes='(max-width: 768px) 100vw, 200px'
							/>
						</div>
					</div>
				</Link>
				<CardContent className='p-4 flex flex-col flex-grow'>
					<Link href={`/product/${slug}`}>
						{/* Product name with line clamp for consistent height */}
						<p className='text-center font-medium text-sm line-clamp-2 h-[40px]'>{name}</p>

						{/* Price section with improved styling */}
						<div className='flex items-center justify-center gap-2 my-3'>
							<span className='font-bold text-brand-black'>{price}৳</span>
							{actualPrice && <span className='line-through text-gray-400 text-sm'>৳{actualPrice}</span>}
						</div>
					</Link>
					{/* Smaller buttons */}
					<div className='grid grid-cols-2 gap-2 mt-auto'>
						<Button variant='brand' onClick={handleBuyNow} className='h-7 text-[10px] font-medium px-1'>
							{/* <ShoppingBag className='h-3 w-3 mr-1 flex-shrink-0' /> */}
							<span className='truncate'>Buy Now</span>
						</Button>
						{cartItems.find((item) => item.id === _id) ? (
							<Button variant='brand-outline' onClick={handelAddedToCart} className='h-7 text-[10px] font-medium px-1'>
								<span className='truncate'>Added</span>
							</Button>
						) : (
							<Button variant='brand-outline' onClick={handleAddToCart} className='h-7 text-[10px] font-medium px-1'>
								{/* <ShoppingCart className='h-3 w-3 mr-1 flex-shrink-0' /> */}
								<span className='truncate'>Add to Cart</span>
							</Button>
						)}
					</div>
				</CardContent>

				{/* Bottom accent line that appears on hover */}
				<div className='h-1 bg-brand w-0 group-hover:w-full transition-all duration-500 ease-out'></div>
			</Card>
		</div>
	);
}
