"use client";

import type React from "react";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import axios from "axios";
import { Eye, EyeOff, Lock, User } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { useForm } from "react-hook-form";

import * as z from "zod";

import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import config from "@/config";
import { setUser } from "@/redux/authSlice";
import { useAppDispatch } from "@/redux/hooks";
import { useToast } from "@/hooks/use-toast";

const signupSchema = z
	.object({
		name: z
			.string({
				required_error: "Name is required",
				invalid_type_error: "Name must be a string"
			})
			.min(3, { message: "Name must be at least 3 characters long!" })
			.max(50, { message: "Name must be at most 50 characters long!" }),

		phone: z
			.string({
				required_error: "Phone number is required",
				invalid_type_error: "Phone number must be a string"
			})
			.regex(/^(?:\+8801|01)[3-9]\d{8}$/, {
				message: "Phone number must be a valid Bangladeshi number (e.g., +8801XXXXXXXX or 01XXXXXXXX)!"
			}),

		password: z
			.string({
				required_error: "Password is required",
				invalid_type_error: "Password must be a string"
			})
			.min(6, { message: "Password must be at least 6 characters long!" })
			.regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{6,}$/, {
				message:
					"Password must contain at least one uppercase letter, one lowercase letter, one number and one special character!"
			}),

		confirmPassword: z.string({
			required_error: "Confirm password is required"
		})
	})
	.refine((data) => data.password === data.confirmPassword, {
		message: "Passwords do not match",
		path: ["confirmPassword"]
	});

const loginSchema = z.object({
	phone: z
		.string()
		.min(1, { message: "Phone number is required" })
		.refine(
			(val) => {
				// Remove the +88 prefix if it exists for validation
				const phoneWithoutPrefix = val.startsWith("+88") ? val.substring(3) : val;
				return phoneWithoutPrefix.length <= 11;
			},
			{ message: "Phone number must not exceed 11 digits" }
		),
	password: z.string().min(1, { message: "Password is required" })
});

interface AuthModalProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
}

export default function AuthModal({ open, onOpenChange }: AuthModalProps) {
	const [activeTab, setActiveTab] = useState("login");
	const [showPassword, setShowPassword] = useState(false);
	const [showConfirmPassword, setShowConfirmPassword] = useState(false);
	const [showLoginPassword, setShowLoginPassword] = useState(false);
	const [isVerifyingOTP, setIsVerifyingOTP] = useState(false);
	const [otpValues, setOtpValues] = useState(["", "", "", "", "", ""]);
	const [userPhone, setUserPhone] = useState("");
	const [isLoading, setIsLoading] = useState(false);
	const otpInputRefs = useRef<Array<HTMLInputElement | null>>([null, null, null, null, null, null]);
	const dispatch = useAppDispatch();
	const { toast } = useToast();

	const signupForm = useForm<z.infer<typeof signupSchema>>({
		resolver: zodResolver(signupSchema),
		defaultValues: {
			name: "",
			phone: "+88",
			password: "",
			confirmPassword: ""
		}
	});

	const loginForm = useForm<z.infer<typeof loginSchema>>({
		resolver: zodResolver(loginSchema),
		defaultValues: {
			phone: "+88",
			password: ""
		}
	});

	async function onSignupSubmit(data: z.infer<typeof signupSchema>) {
		try {
			setIsLoading(true);

			// Format phone by removing +88 prefix if needed for the API
			const phoneFormatted = data.phone.startsWith("+88") ? data.phone : `+88${data.phone}`;

			// Call signup API
			const response = await axios.post(`${config.serverURL}/users/create`, {
				name: data.name,
				phone: phoneFormatted,
				password: data.password
			});

			// If successful, proceed to OTP verification
			if (response.data.success) {
				toast({
					title: "Account created successfully. Please verify with OTP.",
					variant: "default"
				});
				setUserPhone(phoneFormatted);
				setIsVerifyingOTP(true);
			}
		} catch (error: any) {
			// Handle error
			const errorMessage = error.response?.data?.message || "Failed to create account. Please try again.";
			toast({
				title: errorMessage,
				variant: "destructive"
			});
		} finally {
			setIsLoading(false);
		}
	}

	async function onLoginSubmit(data: z.infer<typeof loginSchema>) {
		try {
			setIsLoading(true);

			// Format phone by removing +88 prefix if needed for the API
			const phoneFormatted = data.phone.startsWith("+88") ? data.phone : `+88${data.phone}`;

			// Call login API
			const response = await axios.post(`${config.serverURL}/users/login`, {
				phone: phoneFormatted,
				password: data.password
			});

			// If successful, save token and close modal
			if (response.data.success) {
				const { accessToken, user } = response.data.data;
				// Store token and user data in Redux
				dispatch(setUser({ user, token: accessToken }));

				toast({
					title: "Login successful!",
					variant: "default"
				});
				onOpenChange(false);
				window.location.href = "/account";
			}
		} catch (error: any) {
			// Handle error
			const errorMessage = error.response?.data?.message || "Failed to login. Please check your credentials.";
			toast({
				title: errorMessage,
				variant: "destructive"
			});
		} finally {
			setIsLoading(false);
		}
	}

	function handleOtpChange(index: number, value: string) {
		// Only allow numbers
		if (value && !/^\d+$/.test(value)) return;

		const newOtpValues = [...otpValues];
		newOtpValues[index] = value;
		setOtpValues(newOtpValues);

		// Auto-focus next input
		if (value && index < 5) {
			otpInputRefs.current[index + 1]?.focus();
		}
	}

	function handleKeyDown(index: number, e: React.KeyboardEvent<HTMLInputElement>) {
		// Handle backspace to go to previous input
		if (e.key === "Backspace" && !otpValues[index] && index > 0) {
			otpInputRefs.current[index - 1]?.focus();
		}
	}

	function handlePaste(e: React.ClipboardEvent<HTMLInputElement>) {
		e.preventDefault();
		const pastedData = e.clipboardData.getData("text/plain").trim();

		// Check if pasted content is a 6-digit number
		if (/^\d{6}$/.test(pastedData)) {
			const digits = pastedData.split("");
			setOtpValues(digits);

			// Focus the last input
			otpInputRefs.current[5]?.focus();
		}
	}

	async function verifyOtp() {
		try {
			setIsLoading(true);
			const otpCode = otpValues.join("");

			// Call verify OTP API
			const response = await axios.post(`${config.serverURL}/users/verify`, {
				phone: userPhone,
				otp: otpCode
			});

			// If successful, close the modal and show success message
			if (response.data.success) {
				toast({
					title: "Phone number verified successfully.",
					variant: "default"
				});
				setIsVerifyingOTP(false);
				// setActiveTab("login");
				const { accessToken, user } = response.data.data;
				// Store token and user data in Redux
				dispatch(setUser({ user, token: accessToken }));

				onOpenChange(false);
				window.location.href = "/account";
			}
		} catch (error: any) {
			// Handle error
			const errorMessage = error.response?.data?.message || "Failed to verify OTP. Please try again.";
			toast({
				title: errorMessage,
				variant: "destructive"
			});
		} finally {
			setIsLoading(false);
		}
	}

	async function resendOtp() {
		try {
			setIsLoading(true);

			// Call signup API again to resend OTP
			const response = await axios.post(`${config.serverURL}/users/create`, {
				phone: userPhone
			});

			if (response.data.success) {
				toast({
					title: "OTP has been resent to your phone.",
					variant: "default"
				});

				// Reset OTP inputs
				setOtpValues(["", "", "", "", "", ""]);
				otpInputRefs.current[0]?.focus();
			}
		} catch (error: any) {
			// Handle error
			const errorMessage = error.response?.data?.message || "Failed to resend OTP. Please try again.";
			toast({
				title: errorMessage,
				variant: "destructive"
			});
		} finally {
			setIsLoading(false);
		}
	}

	function cancelOtpVerification() {
		setIsVerifyingOTP(false);
		setOtpValues(["", "", "", "", "", ""]);
	}

	// Focus first OTP input when verification screen appears
	useEffect(() => {
		if (isVerifyingOTP) {
			otpInputRefs.current[0]?.focus();
		}
	}, [isVerifyingOTP]);

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className='sm:max-w-[700px] md:max-w-[950px] p-0 overflow-hidden border-0 bg-brand-dark-900'>
				<div className='grid md:grid-cols-5 border-0'>
					<div className='hidden md:block md:col-span-2 bg-gradient-to-br from-brand-dark-700 to-brand-dark-800 p-8 text-white'>
						<div className='h-full flex flex-col justify-between'>
							<div>
								<h2 className='text-2xl font-bold mb-6'>Welcome to Exchenger Bd</h2>
								<p className='opacity-90 mb-4'>Your one-stop destination for premium gadgets and accessories.</p>
							</div>
							<div className='space-y-4'>
								<div className='flex items-center space-x-2'>
									<div className='w-8 h-8 rounded-full bg-white/20 flex items-center justify-center'>
										<User className='h-4 w-4' />
									</div>
									<div>
										<p className='font-medium'>Easy Registration</p>
										<p className='text-sm opacity-80'>Create an account in seconds</p>
									</div>
								</div>
								<div className='flex items-center space-x-2'>
									<div className='w-8 h-8 rounded-full bg-white/20 flex items-center justify-center'>
										<Lock className='h-4 w-4' />
									</div>
									<div>
										<p className='font-medium'>Secure Login</p>
										<p className='text-sm opacity-80'>Your data is always protected</p>
									</div>
								</div>
							</div>
						</div>
					</div>

					<div className='md:col-span-3 p-6 bg-white'>
						<DialogHeader className='space-y-1 px-0 pt-0'>
							<DialogTitle className='text-2xl font-bold text-brand-black'>
								{isVerifyingOTP ? "Verify Your Phone" : activeTab === "signup" ? "Create Account" : "Welcome Back"}
							</DialogTitle>
							<p className='text-sm text-muted-foreground text-brand-black'>
								{isVerifyingOTP
									? `Enter the 6-digit code sent to ${userPhone}`
									: activeTab === "signup"
									? "Sign up to start shopping with us"
									: "Log in to access your account"}
							</p>
						</DialogHeader>

						<div className='mt-6'>
							{isVerifyingOTP ? (
								<div className='space-y-6'>
									<div className='flex justify-center gap-2'>
										{otpValues.map((value, index) => (
											<Input
												key={index}
												// @ts-ignore
												ref={(el) => (otpInputRefs.current[index] = el)}
												type='text'
												inputMode='numeric'
												maxLength={1}
												value={value}
												onChange={(e) => handleOtpChange(index, e.target.value)}
												onKeyDown={(e) => handleKeyDown(index, e)}
												onPaste={index === 0 ? handlePaste : undefined}
												className='w-12 h-12 text-center text-lg'
											/>
										))}
									</div>

									<div className='flex flex-col gap-3'>
										<Button
											onClick={verifyOtp}
											className='w-full bg-purple-600 hover:bg-purple-700'
											disabled={otpValues.some((v) => !v) || isLoading}
										>
											{isLoading ? "Verifying..." : "Verify & Complete Signup"}
										</Button>

										<div className='flex items-center justify-center gap-1 text-sm'>
											<span className='text-muted-foreground'>Didn&apos;t receive the code?</span>
											<Button variant='link' className='p-0 h-auto' onClick={resendOtp} disabled={isLoading}>
												Resend OTP
											</Button>
										</div>

										<Button variant='outline' onClick={cancelOtpVerification} disabled={isLoading}>
											Back to Signup
										</Button>
									</div>
								</div>
							) : (
								<Tabs defaultValue='signup' value={activeTab} onValueChange={setActiveTab} className='w-full'>
									<TabsList className='grid w-full grid-cols-2 mb-6'>
										<TabsTrigger
											value='signup'
											className='data-[state=active]:bg-brand data-[state=active]:text-brand-black'
										>
											Sign Up
										</TabsTrigger>
										<TabsTrigger
											value='login'
											className='data-[state=active]:bg-brand data-[state=active]:text-brand-black'
										>
											Login
										</TabsTrigger>
									</TabsList>

									<TabsContent value='signup' className='space-y-3 mt-0'>
										<Form {...signupForm}>
											<form onSubmit={signupForm.handleSubmit(onSignupSubmit)} className='space-y-3'>
												<div className='grid grid-cols-1 gap-3'>
													<FormField
														control={signupForm.control}
														name='name'
														render={({ field }) => (
															<FormItem>
																<FormLabel>Full Name</FormLabel>
																<FormControl>
																	<div className='relative'>
																		<User className='absolute left-3 top-2.5 h-4 w-4 text-muted-foreground' />
																		<Input placeholder='John' className='pl-9' {...field} />
																	</div>
																</FormControl>
																<FormMessage />
															</FormItem>
														)}
													/>
												</div>

												<div className='grid grid-cols-1 gap-3'>
													<FormField
														control={signupForm.control}
														name='phone'
														render={({ field }) => (
															<FormItem>
																<FormLabel>Phone Number</FormLabel>
																<FormControl>
																	<div className='phone-input-wrapper'>
																		<div className='flex'>
																			<div className='phone-prefix'>+88</div>
																			<Input
																				placeholder='1XXXXXXXXX'
																				className='phone-input'
																				value={field.value.startsWith("+88") ? field.value.substring(3) : field.value}
																				onChange={(e) => {
																					const value = e.target.value;
																					// Only allow numbers
																					if (value && !/^\d*$/.test(value)) return;
																					// Limit to 11 digits (excluding the +88 prefix)
																					if (value.length > 11) return;
																					field.onChange("+88" + value);
																				}}
																			/>
																		</div>
																	</div>
																</FormControl>
																<FormMessage />
															</FormItem>
														)}
													/>
												</div>

												<FormField
													control={signupForm.control}
													name='password'
													render={({ field }) => (
														<FormItem>
															<FormLabel>Password</FormLabel>
															<FormControl>
																<div className='relative'>
																	<Lock className='absolute left-3 top-2.5 h-4 w-4 text-muted-foreground' />
																	<Input
																		type={showPassword ? "text" : "password"}
																		placeholder='••••••••'
																		className='pl-9'
																		{...field}
																	/>
																	<Button
																		type='button'
																		variant='ghost'
																		size='icon'
																		className='absolute right-0 top-0 h-full px-3'
																		onClick={() => setShowPassword(!showPassword)}
																	>
																		{showPassword ? <EyeOff className='h-4 w-4' /> : <Eye className='h-4 w-4' />}
																		<span className='sr-only'>{showPassword ? "Hide password" : "Show password"}</span>
																	</Button>
																</div>
															</FormControl>
															<FormMessage />
														</FormItem>
													)}
												/>

												<FormField
													control={signupForm.control}
													name='confirmPassword'
													render={({ field }) => (
														<FormItem>
															<FormLabel>Confirm Password</FormLabel>
															<FormControl>
																<div className='relative'>
																	<Lock className='absolute left-3 top-2.5 h-4 w-4 text-muted-foreground' />
																	<Input
																		type={showConfirmPassword ? "text" : "password"}
																		placeholder='••••••••'
																		className='pl-9'
																		{...field}
																	/>
																	<Button
																		type='button'
																		variant='ghost'
																		size='icon'
																		className='absolute right-0 top-0 h-full px-3'
																		onClick={() => setShowConfirmPassword(!showConfirmPassword)}
																	>
																		{showConfirmPassword ? <EyeOff className='h-4 w-4' /> : <Eye className='h-4 w-4' />}
																		<span className='sr-only'>
																			{showConfirmPassword ? "Hide password" : "Show password"}
																		</span>
																	</Button>
																</div>
															</FormControl>
															<FormMessage />
														</FormItem>
													)}
												/>

												<Button
													type='submit'
													className='w-full bg-brand text-brand-black hover:bg-brand/90'
													disabled={isLoading}
												>
													{isLoading ? "Creating Account..." : "Create Account"}
												</Button>
											</form>
										</Form>
									</TabsContent>

									<TabsContent value='login' className='space-y-3 mt-0'>
										<Form {...loginForm}>
											<form onSubmit={loginForm.handleSubmit(onLoginSubmit)} className='space-y-3'>
												<FormField
													control={loginForm.control}
													name='phone'
													render={({ field }) => (
														<FormItem>
															<FormLabel>Phone Number</FormLabel>
															<FormControl>
																<div className='phone-input-wrapper'>
																	<div className='flex'>
																		<div className='phone-prefix'>+88</div>
																		<Input
																			placeholder='1XXXXXXXXX'
																			className='phone-input'
																			value={field.value.startsWith("+88") ? field.value.substring(3) : field.value}
																			onChange={(e) => {
																				const value = e.target.value;
																				// Only allow numbers
																				if (value && !/^\d*$/.test(value)) return;
																				// Limit to 11 digits (excluding the +88 prefix)
																				if (value.length > 11) return;
																				field.onChange("+88" + value);
																			}}
																		/>
																	</div>
																</div>
															</FormControl>
															<FormMessage />
														</FormItem>
													)}
												/>

												<FormField
													control={loginForm.control}
													name='password'
													render={({ field }) => (
														<FormItem>
															<FormLabel>Password</FormLabel>
															<FormControl>
																<div className='relative'>
																	<Lock className='absolute left-3 top-2.5 h-4 w-4 text-muted-foreground' />
																	<Input
																		type={showLoginPassword ? "text" : "password"}
																		placeholder='••••••••'
																		className='pl-9'
																		{...field}
																	/>
																	<Button
																		type='button'
																		variant='ghost'
																		size='icon'
																		className='absolute right-0 top-0 h-full px-3'
																		onClick={() => setShowLoginPassword(!showLoginPassword)}
																	>
																		{showLoginPassword ? <EyeOff className='h-4 w-4' /> : <Eye className='h-4 w-4' />}
																		<span className='sr-only'>
																			{showLoginPassword ? "Hide password" : "Show password"}
																		</span>
																	</Button>
																</div>
															</FormControl>
															<FormMessage />
														</FormItem>
													)}
												/>

												<div className='flex items-center justify-between'>
													<div className='flex items-center space-x-2'>
														<input type='checkbox' id='remember' className='rounded border-gray-300' />
														<label htmlFor='remember' className='text-sm'>
															Remember me
														</label>
													</div>
													<Button variant='link' className='p-0 h-auto' type='button'>
														Forgot Password?
													</Button>
												</div>

												<Button
													type='submit'
													className='w-full bg-brand text-brand-black hover:bg-brand/90'
													disabled={isLoading}
												>
													{isLoading ? "Logging in..." : "Login"}
												</Button>
											</form>
										</Form>
									</TabsContent>
								</Tabs>
							)}
						</div>

						<div className='mt-6'>
							<p className='text-xs text-muted-foreground text-center'>
								By creating an account, you agree to the Applegadgetsbd.com
								<br />
								<a href='#' className='text-purple-600 hover:underline'>
									Privacy Policy
								</a>{" "}
								and{" "}
								<a href='#' className='text-purple-600 hover:underline'>
									Delivery Terms & Conditions
								</a>
							</p>
						</div>
					</div>
				</div>
			</DialogContent>
		</Dialog>
	);
}
