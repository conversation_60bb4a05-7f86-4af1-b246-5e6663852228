"use client";

import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { IProduct } from "@/interfaces";
import ProductCard from "../common/product-card";
import ProductCardSkeleton from "../common/skeleton/product-card-skeleton";
import MaxWidthWrapper from "../ui/max-width-wrapper";

interface ITab {
	_id: string;
	name: string;
	slug: string;
}

interface FeaturedProductsProps {
	title?: string;
	tabs: ITab[];
	className?: string;
	activeTab: string;
	setActiveTab: (value: string) => void;
	products?: IProduct[];
	productsLoading?: boolean;
}

export default function ProductShowcaseTab({
	title = "Featured Products",
	tabs = [],
	className = "",
	activeTab,
	setActiveTab,
	products,
	productsLoading
}: FeaturedProductsProps) {
	// Handle tab change with loading state
	const handleTabChange = (value: any) => {
		setActiveTab(value);
	};
	return (
		<MaxWidthWrapper className={`py-8 ${className}`}>
			{title && (
				<div className='text-center mb-2 block md:hidden'>
					<h1 className='h1-24-black uppercase'>{title}</h1>
				</div>
			)}

			{activeTab && (
				<Tabs defaultValue={activeTab} className='w-full' onValueChange={handleTabChange}>
					<div className=' flex justify-between'>
						{title && (
							<div className=' mb-2 hidden md:block'>
								<h1 className='h1-24-black uppercase'>{title}</h1>
							</div>
						)}
						<div className='w-full md:w-auto overflow-x-auto'>
							<div className='flex justify-center md:justify-start mb-6 min-w-max'>
								<TabsList className='bg-transparent flex gap-6 px-4'>
									{tabs.map((tab) => (
										<TabsTrigger
											key={tab._id}
											value={tab?.slug}
											className='data-[state=active]:text-brand-black data-[state=active]:border-b-2 data-[state=active]:border-brand data-[state=active]:shadow-none rounded-none px-0 w-auto whitespace-nowrap'
										>
											{tab.name}
										</TabsTrigger>
									))}
								</TabsList>
							</div>
						</div>
					</div>

					{tabs.map((tab) => (
						<TabsContent key={tab._id} value={tab?.slug} className='mt-0'>
							{productsLoading ? (
								<div className='grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4'>
									{Array(6)
										.fill(0)
										.map((_, index) => (
											<ProductCardSkeleton key={`skeleton-${index}`} />
										))}
								</div>
							) : products === undefined ? (
								<div className='text-center py-12 bg-red-50 border border-red-200 rounded-md'>
									<p className='text-red-600 text-lg font-semibold'>Something went wrong while loading products.</p>
									<p className='text-sm text-red-500'>Please try again later or refresh the page.</p>
								</div>
							) : products.length === 0 ? (
								<div className='text-center py-12 bg-yellow-50 border border-yellow-200 rounded-md'>
									<p className='text-yellow-600 text-lg font-semibold'>No products found under this brand.</p>
									<p className='text-sm text-yellow-500'>Try another brand or check back later.</p>
								</div>
							) : (
								<div className='grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4'>
									{products.map((product: IProduct) => (
										<ProductCard
											key={product._id}
											_id={product._id}
											images={product?.images}
											name={product?.name}
											slug={product?.slug}
											price={product.price}
											actualPrice={product.actualPrice}
										/>
									))}
								</div>
							)}
						</TabsContent>
					))}
				</Tabs>
			)}
		</MaxWidthWrapper>
	);
}
