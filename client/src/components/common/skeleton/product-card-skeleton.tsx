import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";

export default function ProductCardSkeleton() {
	return (
		<Card className='overflow-hidden border-gray-100 h-full flex flex-col'>
			{/* Image skeleton */}
			<div className='h-[160px] w-full flex items-center justify-center p-4'>
				<Skeleton className='w-full h-full rounded-md' />
			</div>

			<CardContent className='p-4 pt-0 flex flex-col flex-grow'>
				{/* Title skeleton */}
				<div className='flex justify-center mb-2'>
					<Skeleton className='h-4 w-3/4' />
				</div>

				{/* Price skeleton */}
				<div className='flex items-center justify-center gap-2 mb-4'>
					<Skeleton className='h-4 w-16' />
					<Skeleton className='h-4 w-16' />
				</div>

				{/* Button skeletons - pushed to bottom with mt-auto */}
				<div className='grid grid-cols-2 gap-2 mt-auto'>
					<Skeleton className='h-9 w-full rounded' />
					<Skeleton className='h-9 w-full rounded' />
				</div>
			</CardContent>
		</Card>
	);
}
