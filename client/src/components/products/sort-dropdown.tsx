"use client";

import { Button } from "@/components/ui/button";
import { ChevronDown } from "lucide-react";
import { useEffect, useRef, useState } from "react";
import { createPortal } from "react-dom";

export default function SortDropdown() {
	const [isOpen, setIsOpen] = useState(false);
	const [selectedOption, setSelectedOption] = useState("Default");
	const [position, setPosition] = useState({ top: 0, left: 0, width: 0 });
	const buttonRef = useRef<HTMLButtonElement>(null);

	const options = [
		{ label: "Default", value: "default" },
		{ label: "Price: Low to High", value: "price-low" },
		{ label: "Price: High to Low", value: "price-high" },
		{ label: "Newest First", value: "newest" }
	];

	// Update position when button position changes or window resizes
	useEffect(() => {
		const updatePosition = () => {
			if (buttonRef.current) {
				const rect = buttonRef.current.getBoundingClientRect();
				setPosition({
					top: rect.bottom + window.scrollY,
					left: rect.left + window.scrollX,
					width: rect.width
				});
			}
		};

		updatePosition();
		window.addEventListener("resize", updatePosition);
		window.addEventListener("scroll", updatePosition);

		return () => {
			window.removeEventListener("resize", updatePosition);
			window.removeEventListener("scroll", updatePosition);
		};
	}, [isOpen]);

	// Close dropdown when clicking outside
	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (
				buttonRef.current &&
				!buttonRef.current.contains(event.target as Node) &&
				!(event.target as Element).closest(".sort-dropdown-menu")
			) {
				setIsOpen(false);
			}
		};

		if (isOpen) {
			document.addEventListener("mousedown", handleClickOutside);
		}

		return () => {
			document.removeEventListener("mousedown", handleClickOutside);
		};
	}, [isOpen]);

	const handleSelect = (option: string) => {
		setSelectedOption(option);
		setIsOpen(false);
		// Add your sorting logic here
	};

	return (
		<div className='flex items-center gap-2'>
			<span className='text-sm font-medium'>Sort By:</span>
			<Button
				ref={buttonRef}
				variant='outline'
				className='bg-white h-9 px-3 font-normal'
				onClick={() => setIsOpen(!isOpen)}
			>
				{selectedOption} <ChevronDown className='ml-2 h-4 w-4' />
			</Button>

			{isOpen &&
				typeof document !== "undefined" &&
				createPortal(
					<div
						className='sort-dropdown-menu fixed z-50 bg-white border rounded-md shadow-md py-1'
						style={{
							top: `${position.top}px`,
							left: `${position.left}px`,
							width: "180px"
						}}
					>
						{options.map((option) => (
							<div
								key={option.value}
								className='px-3 py-2 hover:bg-gray-100 cursor-pointer text-sm'
								onClick={() => handleSelect(option.label)}
							>
								{option.label}
							</div>
						))}
					</div>,
					document.body
				)}
		</div>
	);
}
