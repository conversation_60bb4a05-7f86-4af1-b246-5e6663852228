import BrandFilter from "@/components/products/brand-filter";
import BrandText from "@/components/products/brand-text";
import FilterSidebar from "@/components/products/filter-sidebar";
import ProductGrid from "@/components/products/product-grid";
import { Breadcrumb, BreadcrumbItem, BreadcrumbLink } from "@/components/ui/breadcrumb";
import { fetchFromServer } from "@/utils/fetchFromServer";
import { ChevronRight } from "lucide-react";
import CategoryFilter from "./category-filter";
import NoProductsFound from "./no-product-founc";
import Pagination from "./pagination";
import SortDropdown from "./sort-dropdown";

export default async function ProductListingPage({
	searchParams,
	params,
	from
}: {
	searchParams: { [key: string]: string | string[] | undefined };
	params: { slug: string[] | string };
	from: "category" | "brand";
}) {
	let filterData: any = {};
	let products = {
		data: [],
		meta: {
			totalPages: 0
		}
	};
	let data = {
		data: [],
		meta: {
			page: 1,
			limit: 10,
			totalItems: 0,
			totalPages: 1,
			filterableFields: [],
			commonBrands: [],
			commonPrimaryCategories: [],
			commonSecondaryCategories: [],
			commonTertiaryCategories: []
		},
		message: "",
		success: true
	};
	let breadcrumbs: { label: string; href?: string }[] = [];

	const filterQuery = Object.entries(searchParams)
		.filter(([key, value]) => value !== undefined && key !== "page")
		.map(([key, value]) => {
			if (typeof value === "string") {
				return `field_${key}=${value}`;
			} else if (Array.isArray(value)) {
				return `field_${key}=${value.join(",")}`;
			}
			return "";
		})
		.filter(Boolean)
		.join("&");

	// if (from === "brand") {
	// 	filterData = await fetchFromServer(`primary-categories`);
	// 	const res = await fetchFromServer(`brands/slug/${params?.slug}`);
	// 	breadcrumbs = ["Brand", res?.data?.name];
	// 	products = await fetchFromServer(`products/minimal?brand=${params?.slug}`);
	// 	data = await fetchFromServer(`products?brand=${params?.slug}&${filterQuery}`);
	// }
	if (from === "brand") {
		filterData = await fetchFromServer(`primary-categories`);
		const res = await fetchFromServer(`brands/slug/${params?.slug}`);
		breadcrumbs = [
			{ label: "Brand" },
			{ label: res?.data?.name } // current page - not clickable
		];
		products = await fetchFromServer(`products/minimal?brand=${params?.slug}`);
		data = await fetchFromServer(`products?brand=${params?.slug}&${filterQuery}`);
	}

	const categoryQuery = `primaryCategory=${params?.slug[0]}${
		params?.slug[1] ? `&secondaryCategory=${params?.slug[1]}` : ""
	}${params?.slug[2] ? `&tertiaryCategory=${params?.slug[2]}` : ""}`;

	// if (from === "category") {
	// 	filterData = await fetchFromServer(`brands`);
	// 	const res = await fetchFromServer(`primary-categories/slug/${params?.slug[0]}`);
	// 	breadcrumbs = ["Category", res?.data?.name];
	// 	if (params?.slug[1]) {
	// 		const secondaryRes = await fetchFromServer(`secondary-categories/slug/${params?.slug[1]}`);
	// 		breadcrumbs.push(secondaryRes?.data?.name);
	// 	}
	// 	if (params?.slug[2]) {
	// 		const tertiaryRes = await fetchFromServer(`tertiary-categories/slug/${params?.slug[2]}`);
	// 		breadcrumbs.push(tertiaryRes?.data?.name);
	// 	}
	// 	products = await fetchFromServer(`products/minimal?${categoryQuery}`);
	// 	data = await fetchFromServer(`products?${categoryQuery}&${filterQuery}`);
	// }
	if (from === "category") {
		filterData = await fetchFromServer(`brands`);
		const res = await fetchFromServer(`primary-categories/slug/${params?.slug[0]}`);
		breadcrumbs = [{ label: "Category" }, { label: res?.data?.name, href: `/category/${params?.slug[0]}` }];

		if (params?.slug[1]) {
			const secondaryRes = await fetchFromServer(`secondary-categories/slug/${params?.slug[1]}`);
			breadcrumbs.push({
				label: secondaryRes?.data?.name,
				href: `/category/${params?.slug[0]}/${params?.slug[1]}`
			});
		}

		if (params?.slug[2]) {
			const tertiaryRes = await fetchFromServer(`tertiary-categories/slug/${params?.slug[2]}`);
			breadcrumbs.push({
				label: tertiaryRes?.data?.name // final item – no href
			});
		}

		products = await fetchFromServer(`products/minimal?${categoryQuery}`);
		data = await fetchFromServer(`products?${categoryQuery}&${filterQuery}`);
	}

	const hasProducts = data?.data && data.data.length > 0;

	return (
		<section className='py-4'>
			{/* Breadcrumb */}
			{/* <Breadcrumb className='mb-4 flex items-center py-2'>
				<BreadcrumbItem>
					<BreadcrumbLink href='/' className='p-12-black'>
						Home
					</BreadcrumbLink>
				</BreadcrumbItem>
				{breadcrumbs.map((item: string, index: number) => (
					<BreadcrumbItem key={index}>
						<ChevronRight className='h-4 w-4' />
						<BreadcrumbLink className='p-12-black'>{item}</BreadcrumbLink>
					</BreadcrumbItem>
				))}
			</Breadcrumb> */}
			<Breadcrumb className='mb-4 flex items-center py-2'>
				<BreadcrumbItem>
					<BreadcrumbLink href='/' className='p-12-black'>
						Home
					</BreadcrumbLink>
				</BreadcrumbItem>

				{breadcrumbs.map((item, index) => {
					const isFirst = index === 0;
					const isLast = index === breadcrumbs.length - 1;

					return (
						<BreadcrumbItem key={index}>
							<ChevronRight className='h-4 w-4' />
							{!isFirst && !isLast && item.href ? (
								<BreadcrumbLink href={item.href} className='p-12-black'>
									{item.label}
								</BreadcrumbLink>
							) : (
								<span className='p-12-black'>{item.label}</span>
							)}
						</BreadcrumbItem>
					);
				})}
			</Breadcrumb>

			{/* Brand Filter */}
			{from === "brand" && <BrandFilter brands={filterData?.data} />}
			{from === "category" && <CategoryFilter categories={filterData?.data} />}

			{/* Main Content */}
			<div className='flex flex-col md:flex-row gap-6 mt-2'>
				<h2 className='h1-20-blackn block md:hidden'>{breadcrumbs[breadcrumbs.length - 1]?.label}</h2>
				{/* Filter Sidebar */}
				<div className='w-full md:w-64 lg:w-72 hidden md:block'>
					<FilterSidebar filterableFields={data?.meta?.filterableFields} />
				</div>

				{/* Product Grid or No Products Found */}
				<div className='flex-1'>
					<div className='flex justify-between items-center mb-4 bg-brand-light-100 p-4 rounded-[3px]'>
						<div className=' block md:hidden '>
							<FilterSidebar filterableFields={data?.meta?.filterableFields} />
						</div>
						<h2 className='h1-20-blackn hidden md:block'>{breadcrumbs[breadcrumbs.length - 1]?.label}</h2>
						<div className='flex items-center gap-2'>
							<SortDropdown />
						</div>
					</div>

					{hasProducts ? (
						<>
							<ProductGrid products={data.data} />
							{/* Pagination */}
							<Pagination currentPage={data?.meta?.page} totalPages={data?.meta?.totalPages} />
						</>
					) : (
						<NoProductsFound />
					)}
				</div>
			</div>

			{/* SEO Content */}
			<BrandText />
		</section>
	);
}
