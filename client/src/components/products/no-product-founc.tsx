import { PackageSearch } from "lucide-react";

export default function NoProductsFound() {
	return (
		<div className='flex flex-col items-center justify-center py-16 px-4 text-center'>
			<div className='bg-brand-light-100 rounded-full p-6 mb-6'>
				<PackageSearch className='h-16 w-16  text-brand-dark-300' />
			</div>
			<h3 className='h2-24-black mb-3'>No Products Found</h3>
			<p className='p-16-regular text-gray-600 max-w-md mb-8'>
				We couldn't find any products matching your current filters. Try adjusting your filter criteria or browse our
				other categories.
			</p>
		</div>
	);
}
