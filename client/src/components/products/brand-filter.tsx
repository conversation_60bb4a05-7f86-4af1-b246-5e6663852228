"use client";

import { IBrand } from "@/interfaces";
import { cn } from "@/lib/utils";
import { useState } from "react";

interface BrandFilterProps {
	brands: IBrand[];
}

export default function BrandFilter({ brands }: BrandFilterProps) {
	const [selectedBrands, setSelectedBrands] = useState<string[]>([]);

	const toggleBrand = (brandId: string) => {
		setSelectedBrands((prev) => (prev.includes(brandId) ? prev.filter((id) => id !== brandId) : [...prev, brandId]));
	};

	return (
		<div className='overflow-x-auto pb-8'>
			<div className='flex flex-wrap gap-4 min-w-max'>
				{brands?.map((brand) => (
					<button
						key={brand._id}
						onClick={() => toggleBrand(brand._id as string)}
						className={cn(
							"flex flex-col items-center justify-center p-2 border border-brand rounded-md transition-colors",
							selectedBrands.includes(brand._id as string) ? "bg-brand/20 border border-brand" : "hover:bg-gray-100"
						)}
					>
						<span className='text-xs font-medium'>{brand.name}</span>
					</button>
				))}
			</div>
		</div>
	);
}
