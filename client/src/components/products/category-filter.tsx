"use client";

import { cn } from "@/lib/utils";
import { useState } from "react";

export default function CategoryFilter({ categories }: any) {
	const [selectedCategories, setSelectedCategories] = useState<string[]>([]);

	const toggleCategory = (categoryId: string) => {
		setSelectedCategories((prev) =>
			prev.includes(categoryId) ? prev.filter((id) => id !== categoryId) : [...prev, categoryId]
		);
	};

	return (
		<div className='overflow-x-auto pb-8'>
			<div className='flex flex-wrap gap-4 min-w-max'>
				{categories?.map((category: any) => (
					<button
						key={category._id}
						onClick={() => toggleCategory(category._id as string)}
						className={cn(
							"flex flex-col items-center justify-center p-2 border border-brand rounded-md transition-colors",
							selectedCategories.includes(category._id as string)
								? "bg-brand/20 border border-brand"
								: "hover:bg-gray-100"
						)}
					>
						<span className='text-xs font-medium'>{category.name}</span>
					</button>
				))}
			</div>
		</div>
	);
}
