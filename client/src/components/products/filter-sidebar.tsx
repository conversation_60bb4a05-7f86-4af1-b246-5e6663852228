"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { ChevronDown, ChevronUp, Filter, X } from "lucide-react";
import { useRouter, useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";

// Define the types for the filter data
interface FilterOption {
	title: string;
	value: string;
}

interface FilterCategory {
	_id: string;
	name: string;
	slug: string;
	options: FilterOption[];
}

interface FilterSidebarProps {
	filterableFields: FilterCategory[];
	priceRange?: { min: number; max: number };
	onFilterChange?: (filters: any) => void;
}

export default function FilterSidebar({
	filterableFields,
	priceRange = { min: 0, max: 200000 },
	onFilterChange
}: FilterSidebarProps) {
	const router = useRouter();
	const searchParams = useSearchParams();
	const [isMobileFilterOpen, setIsMobileFilterOpen] = useState(false);

	// Process the filter data to remove duplicates based on slug
	const uniqueFilters = filterableFields?.reduce((acc: Record<string, FilterCategory>, filter) => {
		if (!acc[filter.slug]) {
			acc[filter.slug] = filter;
		}
		return acc;
	}, {});

	const filterCategories = Object?.values(uniqueFilters);

	// Initialize price range state
	const [currentPriceRange, setCurrentPriceRange] = useState<[number, number]>([priceRange.min, priceRange.max]);

	// Initialize expanded sections state
	const [expandedSections, setExpandedSections] = useState<Record<string, boolean>>(() => {
		const initialState: Record<string, boolean> = { priceRange: true };
		filterCategories?.forEach((category) => {
			initialState[category.slug] = true;
		});
		return initialState;
	});

	// Initialize selected filters state
	const [selectedFilters, setSelectedFilters] = useState<Record<string, string[]>>(() => {
		const initialState: Record<string, string[]> = {};
		filterCategories.forEach((category) => {
			initialState[category.slug] = [];
		});
		return initialState;
	});

	// Load filters from URL when component mounts
	useEffect(() => {
		const initialFilters: Record<string, string[]> = {};
		let minPrice = priceRange.min;
		let maxPrice = priceRange.max;

		// Parse price range from URL
		const minPriceParam = searchParams.get("minPrice");
		const maxPriceParam = searchParams.get("maxPrice");

		if (minPriceParam && !isNaN(Number(minPriceParam))) {
			minPrice = Number(minPriceParam);
		}

		if (maxPriceParam && !isNaN(Number(maxPriceParam))) {
			maxPrice = Number(maxPriceParam);
		}

		setCurrentPriceRange([minPrice, maxPrice]);

		// Parse filter categories from URL
		filterCategories.forEach((category) => {
			const paramValue = searchParams.get(category.slug);
			if (paramValue) {
				initialFilters[category.slug] = paramValue.split(",");
			} else {
				initialFilters[category.slug] = [];
			}
		});

		setSelectedFilters(initialFilters);
	}, []); // Only run on mount, removed dependencies

	// Notify parent component when filters change
	useEffect(() => {
		if (onFilterChange) {
			onFilterChange({
				priceRange: currentPriceRange,
				...selectedFilters
			});
		}
	}, [currentPriceRange, selectedFilters, onFilterChange]);

	// Close mobile filter when clicking outside
	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (!isMobileFilterOpen) return;

			const mobileFilter = document.getElementById("mobile-filter-sidebar");
			const filterButton = document.getElementById("mobile-filter-button");
			const target = event.target as Node;

			// Don't close if clicking inside the filter sidebar or on the filter button
			if ((mobileFilter && mobileFilter.contains(target)) || (filterButton && filterButton.contains(target))) {
				return;
			}

			// Close the filter if clicking outside
			setIsMobileFilterOpen(false);
		};

		if (isMobileFilterOpen) {
			document.addEventListener("mousedown", handleClickOutside);
		}

		return () => {
			document.removeEventListener("mousedown", handleClickOutside);
		};
	}, [isMobileFilterOpen]);

	// Prevent body scroll when mobile filter is open
	useEffect(() => {
		if (isMobileFilterOpen) {
			document.body.style.overflow = "hidden";
		} else {
			document.body.style.overflow = "";
		}
		return () => {
			document.body.style.overflow = "";
		};
	}, [isMobileFilterOpen]);

	const toggleSection = (section: string) => {
		setExpandedSections((prev) => ({
			...prev,
			[section]: !prev[section]
		}));
	};

	const toggleFilter = (section: string, value: string) => {
		setSelectedFilters((prev) => {
			const current = prev[section] || [];
			return {
				...prev,
				[section]: current.includes(value) ? current.filter((item) => item !== value) : [...current, value]
			};
		});
	};

	const clearFilters = () => {
		setCurrentPriceRange([priceRange.min, priceRange.max]);

		const resetFilters: Record<string, string[]> = {};
		filterCategories.forEach((category) => {
			resetFilters[category.slug] = [];
		});

		setSelectedFilters(resetFilters);

		// Clear URL params
		router.push(window.location.pathname);
	};

	const applyFilters = () => {
		// Create a new URLSearchParams instance
		const params = new URLSearchParams();

		// Add price range to params if different from defaults
		if (currentPriceRange[0] !== priceRange.min) {
			params.set("minPrice", currentPriceRange[0].toString());
		}

		if (currentPriceRange[1] !== priceRange.max) {
			params.set("maxPrice", currentPriceRange[1].toString());
		}

		// Add selected filters to params
		Object.entries(selectedFilters).forEach(([key, values]) => {
			if (values.length > 0) {
				params.set(key, values.join(","));
			}
		});

		// Update the URL
		const queryString = params.toString();
		const url = queryString ? `${window.location.pathname}?${queryString}` : window.location.pathname;
		router.push(url);

		// Close mobile filter after applying
		setIsMobileFilterOpen(false);

		// Notify parent component
		if (onFilterChange) {
			onFilterChange({
				priceRange: currentPriceRange,
				...selectedFilters
			});
		}
	};

	const FilterSection = ({ title, slug, options }: { title: string; slug: string; options: FilterOption[] }) => (
		<div className='border-b border-brand-light-200 pb-4'>
			<button onClick={() => toggleSection(slug)} className='flex items-center justify-between w-full py-2'>
				<h3 className='p-14-black font-medium'>{title}</h3>
				{expandedSections[slug] ? <ChevronUp className='h-4 w-4' /> : <ChevronDown className='h-4 w-4' />}
			</button>
			{expandedSections[slug] && (
				<div className='space-y-2 mt-2'>
					{options.map((option) => (
						<div key={option.value} className='flex items-center space-x-2'>
							<Checkbox
								id={`${slug}-${option.value}`}
								checked={selectedFilters[slug]?.includes(option.value)}
								onCheckedChange={() => toggleFilter(slug, option.value)}
							/>
							<Label htmlFor={`${slug}-${option.value}`} className='p-13-black cursor-pointer'>
								{option.title}
							</Label>
						</div>
					))}
					{options.length > 5 && <button className='text-xs text-brand hover:underline mt-1'>See More</button>}
				</div>
			)}
		</div>
	);

	// Filter sidebar content - reused for both desktop and mobile
	const filterContent = (
		<>
			<h2 className='p-16-black mb-4 flex justify-between items-center'>
				Filters
				{isMobileFilterOpen && (
					<button onClick={() => setIsMobileFilterOpen(false)} className='md:hidden'>
						<X className='h-5 w-5' />
					</button>
				)}
			</h2>

			{/* Price Range Filter */}
			<div className='border-b border-brand-light-200 pb-4'>
				<button onClick={() => toggleSection("priceRange")} className='flex items-center justify-between w-full py-2'>
					<h3 className='p-14-black font-medium'>Price Range</h3>
					{expandedSections.priceRange ? <ChevronUp className='h-4 w-4' /> : <ChevronDown className='h-4 w-4' />}
				</button>
				{expandedSections.priceRange && (
					<div className='space-y-4 mt-2'>
						<div className='flex items-center justify-between gap-2'>
							<div className='flex-1'>
								<Label htmlFor='min-price' className='p-12-black mb-1 block'>
									Min
								</Label>
								<input
									id='min-price'
									type='number'
									value={currentPriceRange[0]}
									min={priceRange.min}
									max={currentPriceRange[1]}
									onChange={(e) => {
										const value = Number(e.target.value);
										if (!isNaN(value) && value >= priceRange.min && value <= currentPriceRange[1]) {
											setCurrentPriceRange([value, currentPriceRange[1]]);
										}
									}}
									className='w-full p-2 border border-brand-light-200 rounded-[3px] text-sm'
								/>
							</div>
							<div className='flex-1'>
								<Label htmlFor='max-price' className='p-12-black mb-1 block'>
									Max
								</Label>
								<input
									id='max-price'
									type='number'
									value={currentPriceRange[1]}
									min={currentPriceRange[0]}
									max={priceRange.max}
									onChange={(e) => {
										const value = Number(e.target.value);
										if (!isNaN(value) && value >= currentPriceRange[0] && value <= priceRange.max) {
											setCurrentPriceRange([currentPriceRange[0], value]);
										}
									}}
									className='w-full p-2 border border-brand-light-200 rounded-[3px] text-sm'
								/>
							</div>
						</div>
						<div className='flex items-center justify-between'>
							<span className='p-12-black'>৳{currentPriceRange[0].toLocaleString()}</span>
							<span className='p-12-black'>৳{currentPriceRange[1].toLocaleString()}</span>
						</div>
						<Button size='sm' className='w-full bg-brand hover:bg-brand/90 text-brand-black' onClick={applyFilters}>
							Apply
						</Button>
					</div>
				)}
			</div>

			{/* Dynamic Filter Sections */}
			{filterCategories.map((category) => (
				<FilterSection key={category._id} title={category.name} slug={category.slug} options={category.options} />
			))}

			{/* Filter Actions */}
			<div className='flex gap-2 mt-4'>
				<Button variant='outline' size='sm' className='flex-1' onClick={clearFilters}>
					Clear All
				</Button>
				<Button size='sm' className='flex-1 bg-brand hover:bg-brand/90 text-brand-black' onClick={applyFilters}>
					Apply Filters
				</Button>
			</div>
		</>
	);

	return (
		<>
			{/* Mobile Filter Button */}
			<div className='md:hidden '>
				<Button
					id='mobile-filter-button'
					variant='outline'
					className='w-full flex items-center justify-center gap-2'
					onClick={() => setIsMobileFilterOpen(true)}
				>
					<Filter className='h-4 w-4' />
					<span>Filters</span>
				</Button>
			</div>

			{/* Desktop Filter Sidebar */}
			<div className='border-0 rounded-[3px] p-4 sticky top-28 bg-brand-light-100 hidden md:block'>{filterContent}</div>

			{/* Mobile Filter Sidebar */}
			<div
				id='mobile-filter-sidebar'
				className={`fixed inset-y-0 left-0 z-50 w-[80%] max-w-xs bg-white shadow-lg transform transition-transform duration-300 ease-in-out ${
					isMobileFilterOpen ? "translate-x-0" : "-translate-x-full"
				} md:hidden overflow-y-auto`}
			>
				<div className='p-4'>{filterContent}</div>
			</div>

			{/* Overlay for mobile */}
			{isMobileFilterOpen && (
				<div
					className='fixed inset-0 bg-black bg-opacity-50 z-40 md:hidden'
					onClick={() => setIsMobileFilterOpen(false)}
				/>
			)}
		</>
	);
}
