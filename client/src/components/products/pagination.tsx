"use client";

import { cn } from "@/lib/utils";
import { ChevronLeft, ChevronRight } from "lucide-react";
import Link from "next/link";

interface PaginationProps {
	currentPage: number;
	totalPages: number;
}

export default function Pagination({ currentPage, totalPages }: PaginationProps) {
	// Generate page numbers to display
	const getPageNumbers = () => {
		const pages: (number | string)[] = [];
		const maxPagesToShow = 5;

		if (totalPages <= maxPagesToShow) {
			// If total pages is less than max to show, display all pages
			for (let i = 1; i <= totalPages; i++) {
				pages.push(i);
			}
		} else {
			// Always include first page
			pages.push(1);

			// Calculate start and end of page range
			let start = Math.max(2, currentPage - 1);
			let end = Math.min(totalPages - 1, currentPage + 1);

			// Adjust if at the beginning
			if (currentPage <= 3) {
				end = 4;
			}

			// Adjust if at the end
			if (currentPage >= totalPages - 2) {
				start = totalPages - 3;
			}

			// Add ellipsis if needed at the beginning
			if (start > 2) {
				pages.push("...");
			}

			// Add page numbers in the middle
			for (let i = start; i <= end; i++) {
				pages.push(i);
			}

			// Add ellipsis if needed at the end
			if (end < totalPages - 1) {
				pages.push("...");
			}

			// Always include last page
			pages.push(totalPages);
		}

		return pages;
	};

	const pageNumbers = getPageNumbers();

	return (
		<div className='flex items-center justify-between mt-8'>
			<div className='p-12-black'>Showing 1 to 20 of {totalPages * 10} Pages</div>
			<div className='flex items-center gap-1'>
				<Link
					href={`?page=${Math.max(1, currentPage - 1)}`}
					className={cn(
						"flex items-center justify-center w-8 h-8 rounded-md border",
						currentPage === 1 ? "text-gray-400 cursor-not-allowed" : "hover:bg-gray-100"
					)}
					aria-disabled={currentPage === 1}
				>
					<ChevronLeft className='h-4 w-4' />
					<span className='sr-only'>Previous</span>
				</Link>

				{pageNumbers.map((page, index) => (
					<Link
						key={index}
						href={typeof page === "number" ? `?page=${page}` : "#"}
						className={cn("flex items-center justify-center w-8 h-8 rounded-md border", {
							"bg-brand text-brand-black": currentPage === page,
							"hover:bg-gray-100": currentPage !== page && page !== "...",
							"cursor-default": page === "..."
						})}
						aria-current={currentPage === page ? "page" : undefined}
					>
						{page}
					</Link>
				))}

				<Link
					href={`?page=${Math.min(totalPages, currentPage + 1)}`}
					className={cn(
						"flex items-center justify-center w-8 h-8 rounded-md border",
						currentPage === totalPages ? "text-gray-400 cursor-not-allowed" : "hover:bg-gray-100"
					)}
					aria-disabled={currentPage === totalPages}
				>
					<ChevronRight className='h-4 w-4' />
					<span className='sr-only'>Next</span>
				</Link>
			</div>
		</div>
	);
}
