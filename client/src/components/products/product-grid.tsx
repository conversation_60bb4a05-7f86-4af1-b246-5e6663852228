"use client";
import ProductCard from "@/components/common/product-card";

interface Product {
	_id: string;
	name: string;
	slug: string;
	price: number;
	actualPrice: number;
	images: string[];
	brand: string;
	inStock: boolean;
	storage: string;
	type: string;
	warranty: string;
	sim: string;
	region: string;
}

interface ProductGridProps {
	products: Product[];
}

export default function ProductGrid({ products }: ProductGridProps) {
	console.log({ products });
	return (
		<div className='grid grid-cols-2 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4'>
			{products.map((product) => (
				<ProductCard
					price={`${product?.price}`}
					actualPrice={`${product?.actualPrice}`}
					_id={`${product?._id}`}
					images={product?.images}
					name={product?.name}
					slug={product?.slug}
					key={product?._id}
				/>
			))}
		</div>
	);
}
