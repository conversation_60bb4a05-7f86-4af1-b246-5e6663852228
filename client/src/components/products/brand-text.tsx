import MaxWidthWrapper from "@/components/ui/max-width-wrapper";
import { Smartphone } from "lucide-react";

export default function BrandText() {
	const sections = [
		{
			title: "Affordable Peripherals Devices Price in Bangladesh",
			content:
				"Peripherals are the most essential things to run a digital system. They are like <PERSON> from <PERSON>. Without them, it’s impossible to run a  digital system. There are so many kinds of peripherals for running a system like a mouse, keyboard, smart tag, wifi router, hubs & docks, gaming accessories, and microphone. Users are always searching for the best peripherals at a reasonable price in BD. Apple Gadgets is the best option because all peripherals are available at the lowest price here in BD.",
			icon: Smartphone
		},
		{
			title: "Preeminent Peripheral products Online shop in Dhaka, Bangladesh",
			content:
				"Apple Gadgets is known as the leading peripheral shop in BD. With four outlets around Dhaka city in four different places, we give you the opportunity to grab your peripherals from the nearest one. Conversely, you can get your selected peripherals through order it online.",
			icon: Smartphone
		}
	];

	return (
		<section className='py-12 bg-gray-50'>
			<MaxWidthWrapper className=''>
				<div className='space-y-10'>
					{sections.map((section, index) => {
						const Icon = section.icon;
						return (
							<div key={index} className={`${index > 0 ? "pt-10 border-t border-gray-200" : ""}`}>
								<div className='flex items-start gap-4'>
									{/* <div className='bg-brand/10 p-3 rounded-lg mt-1'>
                                        <Icon className='h-6 w-6 text-brand' strokeWidth={1.5} />
                                    </div> */}
									<div className='flex-1'>
										<h2 className='text-xl font-bold text-brand-black mb-3'>{section.title}</h2>
										<p className='text-brand-dark-800 leading-relaxed'>{section.content}</p>
									</div>
								</div>
							</div>
						);
					})}
				</div>
			</MaxWidthWrapper>
		</section>
	);
}
