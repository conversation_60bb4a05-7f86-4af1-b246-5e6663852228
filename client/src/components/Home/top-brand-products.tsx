"use client";

import { useGetBrandsQuery } from "@/redux/apis/brandsApis";
import { useGetMinimalProductsQuery } from "@/redux/apis/productApis";
import { useEffect, useState } from "react";
import ProductShowcaseTab from "../common/product-showcase-tab";

export default function TopBrandProducts() {
	const { data, isLoading } = useGetBrandsQuery([{ name: "limit", value: "7" }]);
	const [activeTab, setActiveTab] = useState("");
	const { data: pd, isFetching: isProductsLoading } = useGetMinimalProductsQuery(
		[{ name: "brand", value: activeTab }],
		{
			skip: !activeTab
		}
	);

	// Set first brand ID as active tab once data is loaded
	useEffect(() => {
		if (data?.data?.length) {
			setActiveTab(data.data[0].slug || "");
		}
	}, [data, isLoading]);

	return (
		<ProductShowcaseTab
			title='Brand Products'
			tabs={data?.data || []}
			activeTab={activeTab}
			setActiveTab={setActiveTab}
			products={pd?.data || []}
			productsLoading={isProductsLoading}
		/>
	);
}
