"use client";

import { useState } from "react";

import ProductShowcaseTab from "../common/product-showcase-tab";

export default function NewArrival({ products }: any) {
	const [activeTab, setActiveTab] = useState("gadgets");

	const tabs = [
		{
			_id: "gadgets",
			name: "Gadgets",
			slug: "gadgets"
		}
	];

	return (
		<ProductShowcaseTab
			title='New Arrival'
			tabs={tabs}
			activeTab={activeTab}
			setActiveTab={setActiveTab}
			products={products}
		/>
	);
}
