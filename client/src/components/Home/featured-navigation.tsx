import { MessageCircle, MessageSquare, Settings, Smartphone, type LucideIcon } from "lucide-react";
import Link from "next/link";
import MaxWidthWrapper from "../ui/max-width-wrapper";

// Define the feature type
type Feature = {
	title: string;
	description: string;
	icon: LucideIcon;
	href: string;
};

export default function FeatureNavigation() {
	// Array of features with added href property
	const features: Feature[] = [
		{
			title: "Outfit Finder",
			description: "Find Outfit for Gadgets",
			icon: Smartphone,
			href: "/outfit-finder"
		},
		{
			title: "Share Experience",
			description: "We Value your Feedback",
			icon: MessageSquare,
			href: "/feedback"
		},
		{
			title: "Online Support",
			description: "Get Support on WhatsApp",
			icon: MessageCircle,
			href: "/support"
		},
		{
			title: "Exchanger Care",
			description: "Repair Your Device",
			icon: Settings,
			href: "/care"
		}
	];
	// test comment
	return (
		<MaxWidthWrapper className='py-6'>
			<div className='grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4'>
				{features.map((feature, index) => {
					const Icon = feature.icon;
					return (
						<Link href={feature.href} key={index} className='group'>
							<div className='bg-white h-[90px] md:h-[110px] shadow-sm hover:shadow-md transition-all duration-300 rounded-[8px] overflow-hidden relative border border-gray-200'>
								<div className='p-2 md:p-4 flex items-center gap-2 md:gap-3 h-full'>
									{/* Left side with icon */}
									<div className='bg-brand-light-100 rounded-lg p-2.5 flex items-center justify-center transition-all duration-300 group-hover:bg-brand-light-100'>
										<Icon
											className='h-6 w-6 md:h-8 md:w-8 text-brand-black transition-colors duration-300'
											strokeWidth={1}
										/>
									</div>

									{/* Right side with text */}
									<div className='ml-1 md:ml-3 flex-1'>
										<h3 className='font-medium text-[14px] md:text-base'>{feature.title}</h3>
										<p className='text-muted-foreground text-sm mt-0.5 hidden md:block'>{feature.description}</p>
									</div>
								</div>

								{/* Bottom accent line that grows on hover */}
								<div className='h-1 w-0 bg-brand absolute bottom-0 left-0 group-hover:w-full transition-all duration-500 ease-out'></div>
							</div>
						</Link>
					);
				})}
			</div>
		</MaxWidthWrapper>
	);
}
