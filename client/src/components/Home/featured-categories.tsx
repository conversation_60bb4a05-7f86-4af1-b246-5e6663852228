import { fetchFromServer } from "@/utils/fetchFromServer";
import {
	Battery,
	BatteryCharging,
	Cable,
	AlbumIcon as CoverIcon,
	Headphones,
	HeadphonesIcon,
	Laptop,
	Pencil,
	Plug,
	Smartphone,
	Speaker,
	Usb,
	Watch,
	WatchIcon as WatchCircle
} from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import React from "react";
import MaxWidthWrapper from "../ui/max-width-wrapper";

type Category = {
	name: string;
	icon: React.ElementType;
	slug: string;
};

export default async function FeaturedCategories() {
	const categories: Category[] = [
		{ name: "Phones & Tablets", icon: Smartphone, slug: "phones-and-tablets" },
		{ name: "MacBook", icon: Laptop, slug: "macbook" },
		{ name: "Hubs & Docks", icon: Usb, slug: "hubs-docks" },
		{ name: "Stylus", icon: Pencil, slug: "stylus" },
		{ name: "Smart Watch", icon: Watch, slug: "smart-watch" },
		{ name: "Watch Strap", icon: WatchCircle, slug: "watch-strap" },
		{ name: "Airpods", icon: Headphones, slug: "airpods" },
		{ name: "Wired Headphone", icon: Cable, slug: "wired-headphone" },
		{ name: "Wireless Headphone", icon: HeadphonesIcon, slug: "wireless-headphone" },
		{ name: "Power Adapter", icon: Plug, slug: "power-adapter" },
		{ name: "Power Bank", icon: Battery, slug: "power-bank" },
		{ name: "Cable & Interconnects", icon: Cable, slug: "cable-interconnects" },
		{ name: "Wireless Charger", icon: BatteryCharging, slug: "wireless-charger" },
		{ name: "Speakers", icon: Speaker, slug: "speakers" },
		{ name: "Overhead Headphones", icon: HeadphonesIcon, slug: "overhead-headphones" },
		{ name: "Cover & Glass", icon: CoverIcon, slug: "cover-glass" }
	];
	const res = await fetchFromServer("primary-categories?fields=name,slug,image");
	console.log("categories", { res });

	return (
		<MaxWidthWrapper className='pb-8 pt-[24px] md:pt-[32px]'>
			<div className=''>
				<div className='text-center mb-10'>
					<h1 className='h1-24-black uppercase'>FEATURED CATEGORIES</h1>
					<p className='text-gray-700'>Get your desired product from featured category</p>
				</div>

				<div className='grid grid-cols-4 sm:grid-cols-4 md:grid-cols-4 lg:grid-cols-8 gap-5'>
					{res?.data?.map((category, index) => (
						<Link href={`/category/${category.slug}`} key={index} className='block group h-full'>
							<div className='relative bg-white border border-gray-100 rounded-lg shadow-sm transition-all duration-300 hover:shadow-md overflow-hidden h-full flex flex-col'>
								{/* Top accent line */}
								<div className='h-1 w-full bg-brand opacity-0 group-hover:opacity-100 transition-opacity duration-300'></div>

								<div className='flex flex-col flex-grow items-center justify-center h-full p-2 md:p-4'>
									<div className='relative mb-3'>
										{/* Background circle */}
										<div className='absolute inset-0 bg-gray-100 rounded-full transform scale-0 group-hover:scale-100 transition-transform duration-300 -z-10'></div>

										{/* Icon */}
										{/* {React.createElement(category.icon, {
											size: 36,
											className: "text-brand-black group-hover:text-brand-black transition-colors duration-300",
											strokeWidth: 1.25
										})} */}
										<Image src={category?.image} width={500} height={500} alt='image' className=' w-[60px] h-[60px]' />
									</div>

									<span className='text-[0.60rem] sm:text-2xs md:text-sm duration-300 text-center font-medium text-brand-black mt-2'>
										{category.name}
									</span>
								</div>
							</div>
						</Link>
					))}
				</div>
			</div>
		</MaxWidthWrapper>
	);
}
