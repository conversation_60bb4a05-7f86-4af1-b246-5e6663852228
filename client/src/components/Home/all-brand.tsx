import { But<PERSON> } from "@/components/ui/button";
import { I<PERSON><PERSON> } from "@/interfaces";
import { fetchFromServer } from "@/utils/fetchFromServer";
import { ChevronRight } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import MaxWidthWrapper from "../ui/max-width-wrapper";

export default async function ShopByBrands() {
	const res = await fetchFromServer("brands?limit=10&fields=name,image,slug");
	return (
		<MaxWidthWrapper className='py-10'>
			<div className='container mx-auto'>
				<div className='flex justify-between items-center mb-6'>
					<h2 className='h1-24-black uppercase'>SHOP BY BRANDS</h2>
					<Link href='/brands'>
						<Button variant='outline' className='border-brand text-brand-black hover:bg-brand hover:text-brand-black'>
							All Brands <ChevronRight className='ml-1 h-4 w-4' />
						</Button>
					</Link>
				</div>

				<div className='grid grid-cols-3 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-6'>
					{res?.data?.map((brand: IBrand) => (
						<Link key={brand._id} href={`/brands/${brand.slug}`}>
							<div className='border border-gray-100 rounded-lg h-[110px] flex items-center justify-center bg-white hover:shadow-md transition-all duration-300 group'>
								{!brand.image ? (
									<span className='text-lg font-medium text-center text-gray-800 group-hover:text-brand transition-colors'>
										{brand.name}
									</span>
								) : (
									<div className='relative w-[85%] h-[85%]'>
										<Image
											src={brand.image}
											alt={brand.name}
											fill
											className='object-contain transition-transform duration-300 group-hover:scale-110'
											sizes='(max-width: 768px) 100vw, 200px'
										/>
									</div>
								)}
							</div>
						</Link>
					))}
				</div>

				{/* Mobile view "All Brands" button */}
				<div className='mt-6 text-center sm:hidden'>
					<Link href='/brands'>
						<Button variant='brand' className='w-full'>
							View All Brands
						</Button>
					</Link>
				</div>
			</div>
		</MaxWidthWrapper>
	);
}
