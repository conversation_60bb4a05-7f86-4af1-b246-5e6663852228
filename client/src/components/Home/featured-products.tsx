"use client";

import { useState } from "react";

import ProductShowcaseTab from "../common/product-showcase-tab";

export default function FeaturedProducts({ products }: any) {
	const [activeTab, setActiveTab] = useState("deals");
	const tabs = [
		{
			_id: "deals",
			name: "BEST DEALS",
			slug: "deals"
		},
		{
			_id: "sellers",
			name: "BEST SELLERS",
			slug: "sellers"
		}
	];

	return (
		<ProductShowcaseTab
			title='Featured Products'
			tabs={tabs}
			activeTab={activeTab}
			setActiveTab={setActiveTab}
			products={products}
		/>
	);
}
