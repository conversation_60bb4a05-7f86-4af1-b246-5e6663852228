"use client";
import { <PERSON>ouse<PERSON>, <PERSON>ouse<PERSON><PERSON><PERSON>, Carouse<PERSON><PERSON>ontent, CarouselItem } from "@/components/ui/carousel";
import { useGetBannersQuery } from "@/redux/apis/bannerApis";
import Autoplay from "embla-carousel-autoplay";
import Image from "next/image";
import Link from "next/link";
import { useCallback, useEffect, useState } from "react";
import MaxWidthWrapper from "../ui/max-width-wrapper";

export type TBanner = {
	_id: string;
	image: string;
	linkUrl?: string;
	isActive: boolean;
	type: "slider" | "static-1" | "static-2" | "static-3" | "static-4";
};

// Dummy data for when API doesn't return data
const dummySliderBanners: TBanner[] = [
	{
		_id: "slider1",
		image: "/mac-book.webp",
		linkUrl: "/products/featured",
		isActive: true,
		type: "slider"
	},
	{
		_id: "slider2",
		image: "/Galaxy-s25.webp",
		linkUrl: "/products/sale",
		isActive: true,
		type: "slider"
	},
	{
		_id: "slider3",
		image: "/mac-book.webp",
		linkUrl: "/products/new",
		isActive: true,
		type: "slider"
	}
];

const dummyHeroBanners: TBanner[] = [
	{
		_id: "hero1",
		image: "/air-buds.webp",
		linkUrl: "/category/seasonal",
		isActive: true,
		type: "static-1"
	},
	{
		_id: "hero2",
		image: "/watch.webp",
		linkUrl: "/category/exclusive",
		isActive: true,
		type: "static-2"
	}
];
const HeroSection = () => {
	const { data: banners, isLoading } = useGetBannersQuery(undefined, {
		refetchOnFocus: true,
		refetchOnReconnect: true
	});
	// State for carousel API and current slide
	const [api, setApi] = useState<CarouselApi>();
	const [current, setCurrent] = useState(0);

	// Filter banners once for different types, use dummy data if API returns nothing
	const sliderBanners =
		banners?.data?.filter((banner: TBanner) => banner.type === "slider")?.length > 0
			? banners.data.filter((banner: TBanner) => banner.type === "slider")
			: dummySliderBanners;

	const heroBanners =
		banners?.data?.filter((banner: TBanner) => ["static-1", "static-2"].includes(banner.type))?.length > 0
			? banners.data.filter((banner: TBanner) => ["static-1", "static-2"].includes(banner.type))
			: dummyHeroBanners;

	// Create autoplay plugin with a 5 second delay
	// Important: We need to create a new instance for each render to ensure it works properly
	const autoplayOptions = {
		delay: 5000,
		stopOnInteraction: false, // Changed to false so it continues after user interaction
		rootNode: (emblaRoot: any) => emblaRoot.parentElement // Ensure proper root node
	};
	// Update current slide when carousel changes
	useEffect(() => {
		if (!api) return;

		const onSelect = () => {
			setCurrent(api.selectedScrollSnap());
		};

		api.on("select", onSelect);
		// Call once to set initial slide
		onSelect();

		return () => {
			api.off("select", onSelect);
		};
	}, [api]);

	// Handle indicator click
	const handleIndicatorClick = useCallback(
		(index: number, event: React.MouseEvent) => {
			// Stop event propagation to prevent parent Link from being triggered
			event.preventDefault();
			event.stopPropagation();

			if (api) {
				api.scrollTo(index);
			}
		},
		[api]
	);

	const renderPlaceholder = () => (
		<div className='col-span-12 lg:col-span-8 h-[400px] bg-gray-200 animate-pulse rounded-md'></div>
	);

	const renderHeroBanners = () => (
		<div className='grid-cols-2 lg:grid-cols-1 col-span-12 lg:col-span-4 grid w-full gap-4 lg:gap-0'>
			{heroBanners?.length
				? heroBanners.slice(0, 2).map((banner: TBanner) => (
						<div key={banner._id} className='w-full h-[100%]'>
							<Link href={banner.linkUrl || ""}>
								<Image
									src={banner.image || "/placeholder.svg"}
									alt={banner.linkUrl ? "Hero Banner" : "Static Banner"}
									width={700}
									height={350}
									className='object-cover w-full h-full'
								/>
							</Link>
						</div>
				  ))
				: [...Array(2)].map((_, index) => (
						<div key={index} className='w-full h-[98%] bg-gray-200 animate-pulse rounded-md'></div>
				  ))}
		</div>
	);

	const renderSlider = () => (
		<div className='col-span-12 lg:col-span-8 relative'>
			{sliderBanners?.length > 0 ? (
				<Carousel
					opts={{
						loop: true,
						align: "start"
					}}
					plugins={[Autoplay(autoplayOptions)]}
					className='w-full'
					setApi={setApi}
				>
					<CarouselContent>
						{sliderBanners?.map((banner: TBanner) => (
							<CarouselItem key={banner._id} className='w-full h-full'>
								<Link href={banner.linkUrl || "/"}>
									<Image
										src={banner.image || "/placeholder.svg"}
										alt='Slider Banner'
										width={1000}
										height={500}
										className='object-cover w-full h-full lg:h-[450px]'
									/>
								</Link>
							</CarouselItem>
						))}
					</CarouselContent>

					{/* Custom styled arrow buttons with semi-transparent background */}
					{/* <CarouselPrevious className='left-2 bg-black/30 hover:bg-black/50 border-none' />
					<CarouselNext className='right-2 bg-black/30 hover:bg-black/50 border-none' /> */}

					{/* Indicators positioned outside of any Link component */}
					<div className='absolute bottom-4 left-0 right-0 flex justify-center gap-2 z-10'>
						{/* @ts-ignore */}
						{sliderBanners?.map((_, index) => (
							<button
								key={index}
								onClick={(e) => handleIndicatorClick(index, e)}
								className={`w-4 h-1.5 rounded-full transition-colors ${index === current ? "bg-brand" : "bg-white"}`}
								aria-label={`Go to slide ${index + 1}`}
							/>
						))}
					</div>
				</Carousel>
			) : (
				renderPlaceholder()
			)}
		</div>
	);

	return (
		<MaxWidthWrapper className='mt-[-20px] xl:mt-0 xl:my-8'>
			<div className='grid grid-cols-12 gap-4 container mx-auto mt-4'>
				{isLoading ? (
					<>
						{renderPlaceholder()}
						{renderHeroBanners()}
					</>
				) : (
					<>
						{renderSlider()}
						{renderHeroBanners()}
					</>
				)}
			</div>
		</MaxWidthWrapper>
	);
};

export default HeroSection;
