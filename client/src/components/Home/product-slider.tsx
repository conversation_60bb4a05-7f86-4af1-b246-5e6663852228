"use client";

import { IProduct } from "@/interfaces";
import { useEffect, useRef, useState } from "react";
import ProductCard from "../common/product-card";
import MaxWidthWrapper from "../ui/max-width-wrapper";

export default function ProductSlider({ products }: any) {
	const [currentPage, setCurrentPage] = useState(0);
	const sliderRef = useRef<HTMLDivElement>(null);
	const intervalRef = useRef<NodeJS.Timeout | null>(null);

	// Calculate total pages
	const productsPerPage = 6;
	const totalPages = Math.ceil(products.length / productsPerPage);
	// Auto-slide functionality
	useEffect(() => {
		// Start the auto-slide interval
		intervalRef.current = setInterval(() => {
			setCurrentPage((prev) => (prev + 1) % totalPages);
		}, 3000); // Change slide every 3 seconds

		// Clear interval on component unmount
		return () => {
			if (intervalRef.current) {
				clearInterval(intervalRef.current);
			}
		};
	}, [totalPages]);

	// Reset interval when manually changing page
	const goToPage = (pageIndex: number) => {
		setCurrentPage(pageIndex);

		// Reset the interval timer when manually changing page
		if (intervalRef.current) {
			clearInterval(intervalRef.current);
		}

		intervalRef.current = setInterval(() => {
			setCurrentPage((prev) => (prev + 1) % totalPages);
		}, 3000);
	};

	return (
		<MaxWidthWrapper className=' py-8'>
			<div className='flex justify-between items-center mb-6'>
				<h2 className='h1-24-black uppercase'>READY FOR ORDER</h2>
				{/* <Link href='/products'>
					<Button variant='outline' className='border-brand text-brand-black hover:bg-brand hover:text-brand-black'>
						All Products <ChevronRight className='ml-1 h-4 w-4' />
					</Button>
				</Link> */}
			</div>

			<div className='relative'>
				{/* Slider container */}
				<div ref={sliderRef} className='overflow-hidden'>
					<div className='grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4'>
						{products.map((product: IProduct) => (
							<ProductCard
								key={product._id}
								_id={product._id}
								name={product.name}
								slug={product?.slug}
								images={product.images}
								actualPrice={product.actualPrice}
								price={product.price}
							/>
						))}
					</div>
				</div>

				{/* Pagination dots */}
				<div className='flex justify-center mt-6 gap-2'>
					{Array.from({ length: totalPages }).map((_, index) => (
						<button
							key={index}
							onClick={() => goToPage(index)}
							className={`h-2 rounded-full transition-all ${
								currentPage === index ? "w-6 bg-brand" : "w-2 bg-gray-300"
							}`}
							aria-label={`Go to page ${index + 1}`}
						/>
					))}
				</div>
			</div>
		</MaxWidthWrapper>
	);
}
