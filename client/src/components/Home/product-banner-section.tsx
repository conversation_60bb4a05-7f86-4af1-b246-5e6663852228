"use client";

import Image from "next/image";
import Link from "next/link";
import MaxWidthWrapper from "../ui/max-width-wrapper";

export default function ProductBanner({ banners }: any) {
	console.log({ banners });

	return (
		<MaxWidthWrapper className='container mx-auto px-3 sm:px-4 py-4 sm:py-6'>
			<div className='grid grid-cols-2 md:grid-cols-2 gap-4 sm:gap-6'>
				{/* First Product Banner */}
				<Link
					href={banners[0]?.linkUrl || "#"}
					className='relative overflow-hidden rounded-xl transition-transform duration-300 hover:scale-[1.02] block'
				>
					{/* Skeleton loader */}
					{/* {!imageLoaded1 && (
						<div className='w-full aspect-[4/3] sm:aspect-[16/9] bg-gray-100 animate-pulse rounded-lg'></div>
					)} */}

					{/* Product image */}
					<div className={`w-full transition-opacity duration-300`}>
						<Image
							src={banners[0]?.image || "/placeholder.svg"}
							alt={"Product banner"}
							width={800}
							height={600}
							className='w-full h-auto object-contain p-2 sm:p-4 rounded-xl'
							// onLoad={() => setImageLoaded1(true)}
							// priority
						/>
					</div>
				</Link>

				{/* Second Product Banner */}
				<Link
					href={banners[1]?.linkUrl || "#"}
					className='relative overflow-hidden rounded-xl transition-transform duration-300 hover:scale-[1.02]  block'
				>
					{/* Skeleton loader */}
					{/* {!imageLoaded2 && (
						<div className='w-full aspect-[4/3] sm:aspect-[16/9] bg-gray-100 animate-pulse rounded-lg'></div>
					)} */}

					{/* Product image */}
					<div className={`w-full transition-opacity duration-300`}>
						<Image
							src={banners[1]?.image || "/placeholder.svg"}
							alt={"Product banner"}
							width={800}
							height={600}
							className='w-full h-auto object-contain p-2 sm:p-4 rounded-xl'
							// onLoad={() => setImageLoaded2(true)}
							// priority
						/>
					</div>
				</Link>
			</div>
		</MaxWidthWrapper>
	);
}
