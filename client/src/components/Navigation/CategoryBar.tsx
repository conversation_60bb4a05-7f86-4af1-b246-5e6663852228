"use client";

import { Skeleton } from "@/components/ui/skeleton";
import { useGetCategoriesQuery } from "@/redux/apis/categoryApis";
import Link from "next/link";
import { useState } from "react";

type TCategory = {
	_id: string;
	name: string;
	slug: string;
	secondaryCategories: {
		_id: string;
		name: string;
		slug: string;
		tertiaryCategories: {
			_id: string;
			name: string;
			slug: string;
		}[];
	}[];
};

const TopCategoryBar = () => {
	// Mock the API call with our static data
	// const { data: categories, isLoading } = { data: mockCategories, isLoading: false };
	const { data: categories, isLoading } = useGetCategoriesQuery({});
	// console.log({ categoriesN });

	const [activeCategory, setActiveCategory] = useState<string | null>(null);

	return (
		<div className='py-2 shadow-md bg-white hidden xl:block'>
			<div className='px-2'>
				{isLoading || categories?.data?.length === 0 ? (
					<div className='flex gap-4 overflow-x-auto scrollbar-hide'>
						{Array.from({ length: 10 }, (_, index) => (
							<Skeleton className='h-8 w-[140px]' key={index} />
						))}
					</div>
				) : (
					<ul className='flex gap-1 w-full overflow-visible list-none justify-center'>
						{categories?.data?.map((category: TCategory) => {
							const hasSub = category.secondaryCategories?.length > 0;
							const isActive = activeCategory === category._id;

							// If there's no secondary category, just show a flat link
							if (!hasSub) {
								return (
									<li key={category._id} className='relative'>
										<Link href={`/category/${category.slug}`}>
											<span className='px-2 py-2  whitespace-nowrap transition-all duration-300 inline-block text-brand-black text-[14px] font-medium hover:bg-brand'>
												{category.name}
											</span>
										</Link>
									</li>
								);
							}

							// For categories with subcategories, create a dropdown
							return (
								<li
									key={category._id}
									className='group relative'
									onMouseEnter={() => setActiveCategory(category._id)}
									onMouseLeave={() => setActiveCategory(null)}
								>
									<Link href={`/category/${category.slug}`}>
										<span className='px-2 py-2  whitespace-nowrap transition-all duration-300 inline-flex items-center gap-1 p-15-black hover:bg-brand'>
											{category.name}
											<svg
												className={`w-4 h-4 ml-0.5 transition-transform duration-300 ${
													isActive ? "rotate-180" : ""
												} group-hover:rotate-180`}
												fill='none'
												stroke='currentColor'
												viewBox='0 0 24 24'
												xmlns='http://www.w3.org/2000/svg'
											>
												<path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M19 9l-7 7-7-7' />
											</svg>
										</span>
									</Link>

									{/* Secondary categories dropdown - positioned below the category with animation */}
									<div
										className={`absolute left-0 top-[100%] mt-0 w-56 bg-white shadow-lg  transition-all duration-300 ease-out z-50 ${
											isActive
												? "opacity-100 visible translate-y-0"
												: "opacity-0 invisible translate-y-2 group-hover:opacity-100 group-hover:visible group-hover:translate-y-0"
										}`}
									>
										<ul className='list-none mt-3'>
											{category.secondaryCategories.map((sec) => {
												const hasTertiary = sec.tertiaryCategories?.length > 0;

												if (!hasTertiary) {
													return (
														<li key={sec._id} className='m-0 p-0 mb-0 pb-0'>
															<Link href={`/category/${category.slug}/${sec.slug}`}>
																<span className='block px-4 py-[3px] p-14-black hover:bg-brand font-semibold '>
																	{sec.name}
																</span>
															</Link>
														</li>
													);
												}

												// For items with tertiary categories, create a nested dropdown
												return (
													<li key={sec._id} className='group/tertiary relative mb-0 pb-0 m-0 p-0'>
														<Link href={`/category/${category.slug}/${sec.slug}`}>
															<span className='flex items-center justify-between px-4 py-[3px] p-14-black hover:bg-brand group-hover/tertiary:bg-brand font-bold '>
																{sec.name}
																<svg
																	className='w-4 h-4 ml-2'
																	fill='none'
																	stroke='currentColor'
																	viewBox='0 0 24 24'
																	xmlns='http://www.w3.org/2000/svg'
																>
																	<path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M9 5l7 7-7 7' />
																</svg>
															</span>
														</Link>
														{/* Tertiary dropdown with slide-in animation from right */}
														<div className='absolute left-full top-0 w-56 bg-white shadow-lg  opacity-0 invisible transform translate-x-2 group-hover/tertiary:opacity-100 group-hover/tertiary:visible group-hover/tertiary:translate-x-0 transition-all duration-300 ease-out'>
															<ul className=' list-none'>
																{sec.tertiaryCategories.map((ter) => (
																	<li key={ter._id} className='m-0 p-0'>
																		<Link href={`/category/${category.slug}/${sec.slug}/${ter.slug}`}>
																			<span className='block px-4 py-[3px] p-14-black hover:bg-brand font-semibold '>
																				{ter.name}
																			</span>
																		</Link>
																	</li>
																))}
															</ul>
														</div>
													</li>
												);
											})}
										</ul>
									</div>
								</li>
							);
						})}
					</ul>
				)}
			</div>
		</div>
	);
};

export default TopCategoryBar;
