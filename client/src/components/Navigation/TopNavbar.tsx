"use client";

import { NavItems } from "@/constants";
import { useGetCategoriesQuery, useGetPrimaryCategoriesQuery } from "@/redux/apis/categoryApis";
import { useGetMinimalProductsQuery } from "@/redux/apis/productApis";
import { selectCurrentToken } from "@/redux/authSlice";
import { useAppSelector } from "@/redux/hooks";
import { Menu, Search, User, X } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useEffect, useState } from "react";
import AuthModal from "../common/auth-modal";
import MaxWidthWrapper from "../ui/max-width-wrapper";
import { Skeleton } from "../ui/skeleton";
import TopCategoryBar from "./CategoryBar";

const TopNavbar = () => {
	const [searchFocus, setSearchFocus] = useState(false);
	const cartItems = useAppSelector((state) => state.cart.items);
	const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
	const [searchPopoverOpen, setSearchPopoverOpen] = useState(false);
	const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
	const [searchValue, setSearchValue] = useState("");
	const [showSearchDropdown, setShowSearchDropdown] = useState(false);
	const { data: categoryData, isLoading: isCategoriesLoading } = useGetPrimaryCategoriesQuery([
		{ name: "search", value: searchValue }
	]);
	const { data: productsData, isLoading: isProductsLoading } = useGetMinimalProductsQuery([
		{ name: "search", value: searchValue }
	]);
	console.log({ categoryData, productsData });

	// Dummy search data
	const searchSuggestions = {
		categories: categoryData?.data || [],
		products: productsData?.data || [],
		recentSearches: ["iPhone 14", "MacBook", "AirPods", "iPad"]
	};

	const isUserLoggedIn = useAppSelector(selectCurrentToken) ? true : false;

	// Force scroll to top on initial load to ensure topbar is visible
	useEffect(() => {
		window.scrollTo(0, 0);
	}, []);

	const pathname = usePathname();
	const toggleMobileMenu = () => {
		setMobileMenuOpen(!mobileMenuOpen);
		// Close search popover when menu is opened
		if (!mobileMenuOpen) setSearchPopoverOpen(false);
	};

	const toggleSearchPopover = () => {
		setSearchPopoverOpen(!searchPopoverOpen);
		// Close mobile menu when search is opened
		if (!searchPopoverOpen) setMobileMenuOpen(false);
	};

	return (
		<>
			{/* Fixed position topbar for mobile */}
			<div className='bg-white border-b text-brand-black h-[60px] xl:h-[82px] w-full fixed top-0 left-0 right-0 z-50'>
				<MaxWidthWrapper className='h-full'>
					{/* Mobile View */}
					<div className='flex xl:hidden items-center justify-between h-full w-full'>
						{/* Mobile Menu Toggle */}
						<button className='text-brand-black p-2' onClick={toggleMobileMenu}>
							{mobileMenuOpen ? <X className='h-6 w-6' /> : <Menu className='h-6 w-6' />}
						</button>

						{/* Logo */}
						<Link href='/' className='transition-opacity duration-200 hover:opacity-90'>
							<Image src='/exchanger.png' alt='gadgets' width={120} height={40} />
						</Link>

						{/* Mobile Search Icon */}
						<button className='text-brand-black p-2' onClick={toggleSearchPopover}>
							<Search className='h-6 w-6' />
						</button>
					</div>

					{/* Desktop View - Original Layout */}
					<div className='hidden xl:grid grid-cols-2 gap-8 items-center h-full'>
						<div className='flex items-center gap-6'>
							{/* Logo */}
							<Link href='/' className='transition-opacity duration-200 hover:opacity-90'>
								<Image src='/exchanger.png' alt='gadgets' width={120} height={40} />
							</Link>

							{/* Search Bar */}
							<div className='relative flex-grow mx-3 max-w-xl'>
								<input
									type='text'
									placeholder='Search'
									value={searchValue}
									onChange={(e) => setSearchValue(e.target.value)}
									className={`w-full py-2 px-4 rounded-md bg-white text-black h-[45px] focus:outline-none transition-all duration-200 ${
										searchFocus ? "ring-2 ring-brand shadow-sm" : "border border-gray-200 hover:border-gray-300"
									}`}
									onFocus={() => {
										setSearchFocus(true);
										setShowSearchDropdown(true);
									}}
									onBlur={() => {
										setSearchFocus(false);
										// Delay hiding dropdown to allow clicks
										setTimeout(() => setShowSearchDropdown(false), 200);
									}}
								/>
								<button className='absolute right-0 top-0 h-full px-4 text-brand-black transition-transform duration-200 hover:scale-110'>
									<Search />
								</button>

								{/* Search Dropdown */}
								{showSearchDropdown && (
									<div className='absolute top-full left-0 mt-1 bg-white border border-gray-200 rounded-lg shadow-lg z-50 max-h-96 overflow-y-auto'>
										<div className='w-[calc(100vw-2rem)] max-w-4xl p-4'>
											{/* Recent Searches */}
											{/* {searchValue === "" && (
												<div className='mb-4'>
													<h3 className='text-sm font-semibold text-gray-700 mb-2'>Recent Searches</h3>
													<div className='flex flex-wrap gap-2'>
														{searchSuggestions.recentSearches.map((search, index) => (
															<button
																key={index}
																className='px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm hover:bg-gray-200 transition-colors'
																onClick={() => setSearchValue(search)}
															>
																{search}
															</button>
														))}
													</div>
												</div>
											)} */}

											<div className='grid grid-cols-1 md:grid-cols-4 gap-6'>
												{/* Left Column */}
												<div className='col-span-1'>
													{/* Categories */}
													<div className='mb-4'>
														<h3 className='text-sm font-semibold text-gray-700 mb-2'>Categories</h3>
														<div className='space-y-1'>
															{searchSuggestions.categories.map((category) => (
																<Link
																	key={category._id}
																	href={`/category/${category.slug}`}
																	className='block px-3 py-2 text-gray-700 hover:bg-gray-100 rounded-md transition-colors'
																	onClick={() => setShowSearchDropdown(false)}
																>
																	<div className='flex items-center gap-2'>
																		<svg
																			className='w-4 h-4 text-gray-400'
																			fill='none'
																			stroke='currentColor'
																			viewBox='0 0 24 24'
																		>
																			<path
																				strokeLinecap='round'
																				strokeLinejoin='round'
																				strokeWidth={2}
																				d='M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z'
																			/>
																		</svg>
																		{category.name}
																	</div>
																</Link>
															))}
														</div>
													</div>
												</div>

												{/* Right Column */}
												<div className='col-span-3'>
													{/* Products */}
													<div>
														<h3 className='text-sm font-semibold text-gray-700 mb-2'>Products</h3>
														<div className='grid grid-cols-1 sm:grid-cols-3 gap-3'>
															{searchSuggestions.products.map((product) => (
																<Link
																	key={product._id}
																	href={`/product/${product.slug}`}
																	className='flex items-center gap-3 p-2 hover:bg-gray-100 rounded-md transition-colors'
																	onClick={() => setShowSearchDropdown(false)}
																>
																	<img
																		src={product.images[0] || "/placeholder.svg"}
																		alt={product.name}
																		className='w-12 h-12 object-cover rounded-md'
																	/>
																	<div className='flex-1'>
																		<p className='text-sm font-medium text-gray-900'>{product.name}</p>
																		<p className='text-sm text-brand font-semibold'>{product.price}</p>
																	</div>
																</Link>
															))}
														</div>
													</div>
												</div>
											</div>

											{/* View All Results */}
											{searchValue && (
												<div className='mt-4 pt-3 border-t border-gray-200'>
													<Link
														href={`/search?q=${encodeURIComponent(searchValue)}`}
														className='block w-full text-center py-2 text-brand font-medium hover:bg-gray-50 rounded-md transition-colors'
														onClick={() => setShowSearchDropdown(false)}
													>
														View all results for "{searchValue}"
													</Link>
												</div>
											)}
										</div>
									</div>
								)}
							</div>
						</div>

						{/* Navigation Items */}
						<div className='flex items-center justify-between space-x-8'>
							<></>
							{NavItems.map((item, index) => (
								<Link
									href={item?.link}
									key={item?.link}
									className='flex gap-3 items-center group relative py-2 px-1 transition-all duration-200 hover:bg-white/5 rounded-md'
								>
									<div className='text-brand-black mb-1 transition-transform duration-200 group-hover:scale-110 relative'>
										<span className=' text-brand-black'>{item?.icon}</span>
										{/* Badge for cart items - checking if this is the cart item */}
										{item?.title?.toLowerCase().includes("cart") && cartItems.length > 0 && (
											<div className='absolute -top-2 -right-2 bg-brand-dark-500 text-white text-xs font-bold rounded-full w-5 h-5 flex items-center justify-center animate-pulse-subtle'>
												{cartItems.length}
											</div>
										)}
									</div>
									<div className='flex flex-col'>
										<div className=' text-brand-black font-medium transition-colors duration-200 group-hover:text-brand-black'>
											{item?.title}
										</div>
										<div className='text-xs text-gray-600 transition-opacity duration-500 group-hover:text-gray-500'>
											{item?.subTitle}
										</div>
									</div>

									{/* Subtle bottom border that appears on hover */}
									<div className='absolute bottom-0 left-0 w-0 h-[2px] bg-brand transition-all duration-300 group-hover:w-full'></div>
								</Link>
							))}

							{isUserLoggedIn ? (
								<>
									<Link
										href='/account'
										className='flex gap-3 items-center group relative py-2 px-1 transition-all duration-200 hover:bg-white/5 rounded-md'
									>
										<div className='text-brand mb-1 transition-transform duration-200 group-hover:scale-110 relative'>
											<User size={36} strokeWidth={1} className=' text-brand-black' />
										</div>
										<div className='flex flex-col'>
											<div className=' text-brand-black font-medium transition-colors duration-200 group-hover:text-brand-black text-start'>
												Account
											</div>
											<div className='text-xs text-gray-600 transition-opacity duration-200 group-hover:text-gray-500'>
												Update Profile
											</div>
										</div>

										{/* Subtle bottom border that appears on hover */}
										<div className='absolute bottom-0 left-0 w-0 h-[2px] bg-brand transition-all duration-300 group-hover:w-full'></div>
									</Link>
								</>
							) : (
								<>
									<button
										className='flex gap-3 items-center group relative py-2 px-1 transition-all duration-200 hover:bg-white/5 rounded-md'
										onClick={() => setIsAuthModalOpen(true)}
									>
										<div className='text-brand mb-1 transition-transform duration-200 group-hover:scale-110 relative'>
											<User size={36} strokeWidth={1} className=' text-brand-black' />
										</div>
										<div className='flex flex-col'>
											<div className='p-15 text-brand-black font-medium transition-colors duration-200 group-hover:text-brand-black text-start'>
												Account
											</div>
											<div className='text-xs text-gray-600 transition-opacity duration-200 group-hover:text-gray-500'>
												{isUserLoggedIn ? "Update Profile" : "Register or Login"}
											</div>
										</div>

										{/* Subtle bottom border that appears on hover */}
										<div className='absolute bottom-0 left-0 w-0 h-[2px] bg-brand transition-all duration-300 group-hover:w-full'></div>
									</button>
								</>
							)}
						</div>
					</div>
				</MaxWidthWrapper>
			</div>

			{/* Spacer to prevent content from being hidden under fixed header */}
			<div className='h-[55px] md:h-[82px] w-full'></div>

			{/* Mobile Search Popover */}
			<div
				className={`fixed top-[60px] left-0 right-0 bg-white shadow-md z-40 transform transition-transform duration-300 ease-in-out ${
					searchPopoverOpen ? "translate-y-0" : "-translate-y-full"
				} xl:hidden`}
			>
				<div className='p-4'>
					<div className='relative'>
						<input
							type='text'
							placeholder='Search'
							value={searchValue}
							onChange={(e) => setSearchValue(e.target.value)}
							className='w-full py-2 px-4 rounded-md bg-white text-black h-[45px] border border-gray-200 focus:outline-none focus:ring-2 focus:ring-brand shadow-sm'
							autoFocus={searchPopoverOpen}
						/>
						{/* Mobile Search Results */}
						{searchPopoverOpen && (
							<div className='mt-2 bg-white border border-gray-200 rounded-lg shadow-lg max-h-80 overflow-y-auto'>
								<div className='p-3'>
									{/* Recent Searches for Mobile */}
									{/* {searchValue === "" && (
										<div className='mb-3'>
											<h3 className='text-sm font-semibold text-gray-700 mb-2'>Recent</h3>
											<div className='flex flex-wrap gap-2'>
												{searchSuggestions.recentSearches.map((search, index) => (
													<button
														key={index}
														className='px-2 py-1 bg-gray-100 text-gray-700 rounded-full text-xs hover:bg-gray-200'
														onClick={() => setSearchValue(search)}
													>
														{search}
													</button>
												))}
											</div>
										</div>
									)} */}

									{/* Categories for Mobile */}
									<div className='mb-3'>
										<h3 className='text-sm font-semibold text-gray-700 mb-2'>Categories</h3>
										{searchSuggestions.categories.map((category) => (
											<Link
												key={category._id}
												href={`/category/${category.slug}`}
												className='block px-2 py-2 text-sm text-gray-700 hover:bg-gray-100 rounded'
												onClick={() => setSearchPopoverOpen(false)}
											>
												{category.name}
											</Link>
										))}
									</div>

									{/* Products for Mobile */}
									<div>
										<h3 className='text-sm font-semibold text-gray-700 mb-2'>Products</h3>
										{searchSuggestions.products.map((product) => (
											<Link
												key={product._id}
												href={`/product/${product.slug}`}
												className='flex items-center gap-2 p-2 hover:bg-gray-100 rounded'
												onClick={() => setSearchPopoverOpen(false)}
											>
												<img
													src={product.image || "/placeholder.svg"}
													alt={product.name}
													className='w-10 h-10 object-cover rounded'
												/>
												<div className='flex-1'>
													<p className='text-sm font-medium text-gray-900 truncate'>{product.name}</p>
													<p className='text-xs text-brand font-semibold'>{product.price}</p>
												</div>
											</Link>
										))}
									</div>
								</div>
							</div>
						)}
					</div>
				</div>
			</div>

			{/* Mobile Menu */}
			<div
				className={`fixed inset-0 bg-white z-40 transform transition-transform duration-300 ease-in-out ${
					mobileMenuOpen ? "translate-x-0" : "-translate-x-full"
				} xl:hidden`}
				style={{ top: "82px", height: "calc(100vh - 82px)" }}
			>
				<div className='h-full overflow-y-auto'>
					<MobileCategoryMenu closeMenu={() => setMobileMenuOpen(false)} />
				</div>
			</div>

			{/* Desktop Category Bar - Only show if not on ads route */}
			{!pathname?.includes("/ads") && !pathname?.includes("/blog") && <TopCategoryBar />}
			<AuthModal open={isAuthModalOpen} onOpenChange={setIsAuthModalOpen} />
		</>
	);
};
type TCategory = {
	_id: string;
	name: string;
	slug: string;
	secondaryCategories: {
		_id: string;
		name: string;
		slug: string;
		tertiaryCategories: {
			_id: string;
			name: string;
			slug: string;
		}[];
	}[];
};

// Mobile Category Menu Component
const MobileCategoryMenu = ({ closeMenu }: any) => {
	const [expandedCategories, setExpandedCategories] = useState<Record<string, boolean>>({});
	const [expandedSubCategories, setExpandedSubCategories] = useState<Record<string, boolean>>({});
	const { data: categories, isLoading } = useGetCategoriesQuery(undefined, {
		refetchOnFocus: true,
		refetchOnReconnect: true
	});

	const toggleCategory = (categoryId: string) => {
		setExpandedCategories((prev) => ({
			...prev,
			// @ts-ignore
			[categoryId]: !prev[categoryId]
		}));
	};

	const toggleSubCategory = (subCategoryId: string) => {
		setExpandedSubCategories((prev) => ({
			...prev,
			// @ts-ignore
			[subCategoryId]: !prev[subCategoryId]
		}));
	};

	return (
		<div className='bg-gray-100 min-h-full'>
			{isLoading || categories?.data?.length === 0 ? (
				<div className='border-b border-gray-200 flex flex-col gap-3 p-3'>
					{Array.from({ length: 20 }, (_, index) => (
						<Skeleton className='h-8 w-full' key={index} />
					))}
				</div>
			) : (
				<div>
					{categories?.data.map((category: TCategory) => (
						<div key={category._id} className='border-b border-gray-200'>
							<div
								className='flex justify-between items-center px-4 py-3 bg-white'
								onClick={() => toggleCategory(category._id)}
							>
								<Link
									href={`/category/${category.slug}`}
									className='font-medium text-gray-800 flex-grow'
									onClick={(e) => {
										if (category.secondaryCategories?.length > 0) {
											e.preventDefault();
										} else {
											closeMenu();
										}
									}}
								>
									{category.name}
								</Link>
								{category.secondaryCategories?.length > 0 && (
									<button className='p-1'>
										{/* @ts-ignore */}
										{expandedCategories[category._id] ? (
											<svg className='w-5 h-5' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
												<path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M5 15l7-7 7 7' />
											</svg>
										) : (
											<svg className='w-5 h-5' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
												<path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M19 9l-7 7-7-7' />
											</svg>
										)}
									</button>
								)}
							</div>

							{/* Secondary Categories */}
							{expandedCategories[category._id] && category.secondaryCategories?.length > 0 && (
								<div className='bg-gray-50'>
									{category.secondaryCategories.map((subCategory: any) => (
										<div key={subCategory._id} className='border-t border-gray-200'>
											<div
												className='flex justify-between items-center px-6 py-2'
												onClick={() => toggleSubCategory(subCategory._id)}
											>
												<Link
													href={`/category/${category.slug}/${subCategory.slug}`}
													className='text-gray-700 flex-grow'
													onClick={(e) => {
														if (subCategory.tertiaryCategories?.length > 0) {
															e.preventDefault();
														} else {
															closeMenu();
														}
													}}
												>
													{subCategory.name}
												</Link>
												{subCategory.tertiaryCategories?.length > 0 && (
													<button className='p-1'>
														{expandedSubCategories[subCategory._id] ? (
															<svg className='w-4 h-4' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
																<path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M5 15l7-7 7 7' />
															</svg>
														) : (
															<svg className='w-4 h-4' fill='none' stroke='currentColor' viewBox='0 0 24 24'>
																<path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M19 9l-7 7-7-7' />
															</svg>
														)}
													</button>
												)}
											</div>

											{/* Tertiary Categories */}
											{expandedSubCategories[subCategory._id] && subCategory.tertiaryCategories?.length > 0 && (
												<div className='bg-gray-100'>
													{subCategory.tertiaryCategories.map((terCategory: any) => (
														<Link
															key={terCategory._id}
															href={`/category/${category.slug}/${subCategory.slug}/${terCategory.slug}`}
															onClick={closeMenu}
														>
															<div className='px-8 py-2 text-gray-600 border-t border-gray-200'>{terCategory.name}</div>
														</Link>
													))}
												</div>
											)}
										</div>
									))}
								</div>
							)}
						</div>
					))}
				</div>
			)}
		</div>
	);
};

export default TopNavbar;
