"use client";
import { selectCurrentToken } from "@/redux/authSlice";
import { useAppSelector } from "@/redux/hooks";
import { CalendarDays, Gift, GitCompareArrows, ShoppingCart, User } from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { useState } from "react";
import AuthModal from "../common/auth-modal";
import { useCart } from "@/hooks/useCart";

const MobileBottomNav = () => {
	const { cartItems } = useCart();
	const pathname = usePathname();
	const NavItems = [
		{
			title: "Offers",
			link: "/offers",
			icon: <Gift size={27} strokeWidth={1} className=' ' />
		},
		{
			title: "Cart",
			link: "/cart",
			icon: <ShoppingCart size={27} strokeWidth={1} className=' ' />
		},
		{
			title: "Pre-Order",
			link: "/pre-order",
			icon: <CalendarDays size={27} strokeWidth={1} className=' ' />
		},
		{
			title: "Compare",
			link: "/compare",
			icon: <GitCompareArrows size={27} strokeWidth={1} />
		}
	];

	const [isAuthModalOpen, setIsAuthModalOpen] = useState(false);
	const isUserLoggedIn = useAppSelector(selectCurrentToken) ? true : false;

	return (
		<>
			{/* Spacer to prevent content from being hidden under fixed header */}
			<div className='h-[78px] lg:hidden w-full bg-brand-black'></div>
			<div className='fixed lg:hidden bottom-0 left-0 right-0 bg-brand-black text-white p-2 flex justify-around items-center z-50 shadow-lg border-t border-brand-dark-800'>
				{NavItems.map((item) => {
					const isActive = pathname === item.link;
					return (
						<Link
							key={item.link}
							href={item.link}
							className='relative flex flex-col items-center gap-1 px-3 py-1.5 rounded-lg transition-all duration-300'
						>
							<div className={`text-2xl ${isActive ? "text-brand" : "text-gray-300"}`}>
								<span className='text-brand h-5 w-5'>{item.icon}</span>
								{isActive && (
									<span className='absolute -top-1 -right-1 flex h-2 w-2'>
										<span className='animate-ping absolute inline-flex h-full w-full rounded-full bg-[#FBDD0A] opacity-75'></span>
										<span className='relative inline-flex rounded-full h-2 w-2 bg-[#FBDD0A]'></span>
									</span>
								)}
							</div>
							<span className={`text-[10px] font-medium ${isActive ? "text-white" : "text-gray-400"}`}>
								{item.title}
								{item.title === "Cart" && `${cartItems.length > 0 ? ` (${cartItems.length})` : ""}`}
							</span>
							{isActive && <div className='absolute -bottom-2 w-12 h-1 bg-[#FBDD0A] rounded-full' />}
						</Link>
					);
				})}
				{/* Account Option */}
				{isUserLoggedIn ? (
					<Link
						href='/account'
						className='relative flex flex-col items-center gap-1 px-3 py-1.5 rounded-lg transition-all duration-300'
					>
						<div className={`text-2xl ${pathname === "/account" ? "text-brand" : "text-gray-300"}`}>
							<User size={27} strokeWidth={1} className=' text-brand' />
							{pathname === "/account" && (
								<span className='absolute -top-1 -right-1 flex h-2 w-2'>
									<span className='animate-ping absolute inline-flex h-full w-full rounded-full bg-[#FBDD0A] opacity-75'></span>
									<span className='relative inline-flex rounded-full h-2 w-2 bg-[#FBDD0A]'></span>
								</span>
							)}
						</div>
						<span className={`text-[10px] font-medium ${pathname === "/account" ? "text-white" : "text-gray-400"}`}>
							Account
						</span>
						{pathname === "/account" && <div className='absolute -bottom-2 w-12 h-1 bg-[#FBDD0A] rounded-full' />}
					</Link>
				) : (
					<button
						onClick={() => setIsAuthModalOpen(true)}
						className='relative flex flex-col items-center gap-1 px-3 py-1.5 rounded-lg transition-all duration-300'
					>
						<div className='text-2xl text-gray-300'>
							<User size={27} strokeWidth={1} className=' text-brand' />
						</div>
						<span className='text-[10px] font-medium text-gray-400'>Login</span>
					</button>
				)}
			</div>
			<AuthModal open={isAuthModalOpen} onOpenChange={setIsAuthModalOpen} />
		</>
	);
};

export default MobileBottomNav;
