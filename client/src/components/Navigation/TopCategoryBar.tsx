"use client";

import { useGetCategoriesQuery } from "@/redux/apis/categoryApis";
import Link from "next/link";
import { useState } from "react";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuPortal,
	DropdownMenuSub,
	DropdownMenuSubContent,
	DropdownMenuSubTrigger,
	DropdownMenuTrigger
} from "../ui/dropdown-menu";
import { Skeleton } from "../ui/skeleton";

type TCategory = {
	_id: string;
	name: string;
	slug: string;
	secondaryCategories: {
		_id: string;
		name: string;
		slug: string;
		tertiaryCategories: {
			_id: string;
			name: string;
			slug: string;
		}[];
	}[];
};

const TopCategoryBar = () => {
	const { data: categories, isLoading } = useGetCategoriesQuery(undefined, {
		refetchOnFocus: true,
		refetchOnReconnect: true
	});

	const [openDropdown, setOpenDropdown] = useState<string | null>(null);

	return (
		<div className='py-2 shadow-md bg-white hidden lg:block'>
			<div className='container mx-auto'>
				{isLoading || categories?.data?.length === 0 ? (
					<div className='flex gap-4 overflow-x-auto scrollbar-hide'>
						{Array.from({ length: 10 }, (_, index) => (
							<Skeleton className='h-8 w-[140px]' key={index} />
						))}
					</div>
				) : (
					<div className='flex gap-4 overflow-x-auto scrollbar-hide'>
						{categories?.data?.map((category: TCategory) => {
							const hasSub = category.secondaryCategories?.length > 0;

							// If there's no secondary category, just show a flat link
							if (!hasSub) {
								return (
									<Link key={category._id} href={`/categories/${category.slug}`}>
										<div className='px-4 py-2 text-gray-800  whitespace-nowrap hover:bg-[#fbdd0a] transition-all duration-300 font-semibold'>
											{category.name}
										</div>
									</Link>
								);
							}

							return (
								<div
									key={category._id}
									onMouseEnter={() => hasSub && setOpenDropdown(category._id)}
									onMouseLeave={() => setOpenDropdown(null)}
								>
									<DropdownMenu open={openDropdown === category._id}>
										<Link key={category._id} href={`/categories/${category.slug}`}>
											<DropdownMenuTrigger className='px-4 py-2 text-gray-800  whitespace-nowrap hover:bg-[#fbdd0a] focus:outline-none transition-all duration-300 font-semibold'>
												{category.name}
											</DropdownMenuTrigger>
										</Link>

										{hasSub && (
											<DropdownMenuContent className='w-56'>
												{category.secondaryCategories.map((sec) => {
													const hasTertiary = sec.tertiaryCategories?.length > 0;

													// If there's no tertiary, just show a flat link
													if (!hasTertiary) {
														return (
															<Link key={sec._id} href={`/categories/${category.slug}/${sec.slug}`}>
																<DropdownMenuItem className='cursor-pointer focus:bg-[#fbdd0a] transition-all duration-300 font-semibold'>
																	{sec.name}
																</DropdownMenuItem>
															</Link>
														);
													}

													// If there *is* tertiary, show nested submenu
													return (
														<DropdownMenuSub key={sec._id}>
															<Link key={sec._id} href={`/categories/${category.slug}/${sec.slug}`}>
																<DropdownMenuSubTrigger className='cursor-pointer font-semibold'>
																	{sec.name}
																</DropdownMenuSubTrigger>
															</Link>

															<DropdownMenuPortal>
																<DropdownMenuSubContent>
																	{sec.tertiaryCategories.map((ter) => (
																		<Link key={ter._id} href={`/categories/${category.slug}/${sec.slug}/${ter.slug}`}>
																			<DropdownMenuItem className='cursor-pointer focus:bg-[#fbdd0a] transition-all duration-300 font-semibold'>
																				{ter.name}
																			</DropdownMenuItem>
																		</Link>
																	))}
																</DropdownMenuSubContent>
															</DropdownMenuPortal>
														</DropdownMenuSub>
													);
												})}
											</DropdownMenuContent>
										)}
									</DropdownMenu>
								</div>
							);
						})}
					</div>
				)}
			</div>
		</div>
	);
};

export default TopCategoryBar;
