import { Facebook, Instagram, Linkedin, MapPin, Phone, InstagramIcon as <PERSON><PERSON><PERSON>, Youtube } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import MaxWidthWrapper from "../ui/max-width-wrapper";

export default function SiteFooter() {
	// Social media links array
	const socialLinks = [
		{ icon: Facebook, href: "#", label: "Facebook" },
		{ icon: Instagram, href: "#", label: "Instagram" },
		{ icon: Youtube, href: "#", label: "Youtube" },
		{ icon: Tik<PERSON>, href: "#", label: "TikTok" },
		{ icon: Linkedin, href: "#", label: "LinkedIn" }
	];

	// App store links array
	const appStoreLinks = [
		{
			image: "/placeholder.svg?height=40&width=120&text=App+Store",
			alt: "Download on the App Store",
			href: "#",
			width: 120,
			height: 40
		},
		{
			image: "/placeholder.svg?height=40&width=120&text=Google+Play",
			alt: "Get it on Google Play",
			href: "#",
			width: 120,
			height: 40
		}
	];

	// About Us links array
	const aboutLinks = [
		{ href: "/about", text: "About Us" },
		{ href: "/order-tracking", text: "Order Tracking" },
		{ href: "/blog", text: "Blog" },
		{ href: "/press", text: "Press Coverage" },
		{ href: "/careers", text: "Careers" },
		{ href: "/complain", text: "Complain / Advice" },
		{ href: "/contact", text: "Contact Us" },
		{ href: "/faqs", text: "FAQs" }
	];

	// Policy links array
	const policyLinks = [
		{ href: "/privacy-policy", text: "Privacy Policy" },
		{ href: "/emi-policy", text: "EMI and Payment Policy" },
		{ href: "/warranty-policy", text: "Warranty Policy" },
		{ href: "/exchange-policy", text: "Exchange Policy" },
		{ href: "/delivery-policy", text: "Delivery Policy" },
		{ href: "/pre-order-policy", text: "Pre-Order Policy" },
		{ href: "/refund-policy", text: "Refund Policy" },
		{ href: "/return-policy", text: "Return Policy" }
	];

	// Store locations array
	const storeLocations = [
		{
			lines: ["Basement 2, Shop 26,", "Bashundhara City Shopping Complex"]
		},
		{
			lines: ["Level- 5, Block- A, Shop- 6, 7, 8,", "Bashundhara City Shopping Complex"]
		},
		{
			lines: ["Level 4, Zone A (West Court), Shop 28D,", "Jamuna Future Park"]
		},
		{
			lines: ["Level 4, Shop 505,", "Mascot Plaza - Uttara"]
		}
	];

	return (
		<footer className='bg-brand-black text-white'>
			<MaxWidthWrapper className='container mx-auto px-4 py-8 md:py-12'>
				{/* Mobile Layout */}
				<div className='block md:hidden space-y-8'>
					{/* SUPPORT Section */}
					<div className='text-center'>
						<h3 className='text-brand font-semibold mb-4 text-sm tracking-wider'>SUPPORT</h3>

						{/* Phone Number */}
						<div className='mb-4'>
							<Link
								href='tel:09678148148'
								className='flex items-center justify-center border border-gray-700 rounded-full p-3 mx-auto w-fit hover:border-brand transition-colors'
							>
								<Phone className='h-4 w-4 mr-3 text-white' />
								<span className='text-sm'>09678148148</span>
							</Link>
						</div>

						{/* Store Locator */}
						<div className='mb-6'>
							<Link
								href='/stores'
								className='flex items-center justify-center border border-gray-700 rounded-full p-3 mx-auto w-fit hover:border-brand transition-colors'
							>
								<MapPin className='h-4 w-4 mr-3 text-white' />
								<span className='text-sm'>Find Our Stores</span>
							</Link>
						</div>

						{/* Social Media Icons */}
						<div className='flex justify-center gap-3 mb-6'>
							{socialLinks.map((social, index) => {
								const Icon = social.icon;
								return (
									<Link
										key={index}
										href={social.href}
										className='bg-gray-800 p-2 rounded-full hover:bg-brand hover:text-black transition-colors'
									>
										<Icon className='h-4 w-4' />
										<span className='sr-only'>{social.label}</span>
									</Link>
								);
							})}
						</div>

						{/* App Store Links */}
						<div className='flex justify-center gap-3'>
							{appStoreLinks.map((app, index) => (
								<Link key={index} href={app.href} className='hover:opacity-80 transition-opacity'>
									<Image
										src={app.image || "/placeholder.svg"}
										alt={app.alt}
										width={app.width}
										height={app.height}
										className='h-10 w-auto rounded'
									/>
								</Link>
							))}
						</div>
					</div>

					{/* About Us Section */}
					<div className='text-center'>
						<h3 className='text-brand font-semibold mb-4 text-sm tracking-wider'>ABOUT US</h3>
						<div className='grid grid-cols-2 gap-2 text-xs'>
							{aboutLinks.map((link, index) => (
								<Link key={index} href={link.href} className='text-gray-300 hover:text-brand transition-colors py-1'>
									{link.text}
								</Link>
							))}
						</div>
					</div>

					{/* Policy Section */}
					<div className='text-center'>
						<h3 className='text-brand font-semibold mb-4 text-sm tracking-wider'>POLICY</h3>
						<div className='grid grid-cols-2 gap-2 text-xs'>
							{policyLinks.map((link, index) => (
								<Link key={index} href={link.href} className='text-gray-300 hover:text-brand transition-colors py-1'>
									{link.text}
								</Link>
							))}
						</div>
					</div>

					{/* Stay Connected Section */}
					<div className='text-center'>
						<h3 className='text-brand font-semibold mb-4 text-sm tracking-wider'>STAY CONNECTED</h3>
						<div className='space-y-3 text-xs'>
							<p className='text-white font-medium'>Exchanger BD</p>
							{storeLocations.map((location, index) => (
								<div key={index} className='text-gray-300'>
									{location.lines.map((line, lineIndex) => (
										<p key={lineIndex} className={lineIndex === 0 ? "mb-1" : ""}>
											{line}
										</p>
									))}
								</div>
							))}
							<div className='pt-2'>
								<p className='text-gray-300'>
									Email:{" "}
									<Link href='mailto:<EMAIL>' className='text-brand hover:underline'>
										<EMAIL>
									</Link>
								</p>
							</div>
						</div>
					</div>
				</div>

				{/* Desktop Layout */}
				<div className='hidden md:grid grid-cols-2 lg:grid-cols-4 gap-8'>
					{/* SUPPORT Column */}
					<div>
						<h3 className='text-brand font-semibold mb-6'>SUPPORT</h3>

						{/* Phone Number */}
						<div className='mb-6'>
							<Link
								href='tel:09678148148'
								className='flex items-center border border-gray-700 rounded-full p-2 pl-4 pr-6 w-fit hover:border-brand transition-colors'
							>
								<Phone className='h-5 w-5 mr-3 text-white' />
								<span className='p-15-white'>09678148148</span>
							</Link>
						</div>

						{/* Store Locator */}
						<div className='mb-6'>
							<Link
								href='/stores'
								className='flex items-center border border-gray-700 rounded-full p-2 pl-4 pr-6 w-fit hover:border-brand transition-colors'
							>
								<MapPin className='h-5 w-5 mr-3 text-white' />
								<span className='p-15-white'>Find Our Stores</span>
							</Link>
						</div>

						{/* Social Media Icons */}
						<div className='flex gap-2 mb-6'>
							{socialLinks.map((social, index) => {
								const Icon = social.icon;
								return (
									<Link
										key={index}
										href={social.href}
										className='bg-brand-dark-800 p-2 rounded-full hover:bg-brand hover:text-black transition-colors'
									>
										<Icon className='h-5 w-5' />
										<span className='sr-only'>{social.label}</span>
									</Link>
								);
							})}
						</div>

						{/* App Store Links */}
						<div className='flex flex-wrap gap-2'>
							{appStoreLinks.map((app, index) => (
								<Link key={index} href={app.href} className='hover:opacity-80 transition-opacity'>
									<Image
										src={app.image || "/placeholder.svg"}
										alt={app.alt}
										width={app.width}
										height={app.height}
										className='h-10 w-auto'
									/>
								</Link>
							))}
						</div>
					</div>

					{/* About Us Column */}
					<div>
						<h3 className='text-brand font-semibold mb-6'>About Us</h3>
						<ul className='space-y-3 list-none'>
							{aboutLinks.map((link, index) => (
								<li key={index}>
									<Link href={link.href} className='p-14-white hover:text-brand transition-colors'>
										{link.text}
									</Link>
								</li>
							))}
						</ul>
					</div>

					{/* Policy Column */}
					<div>
						<h3 className='text-brand font-semibold mb-6'>Policy</h3>
						<ul className='space-y-3 list-none'>
							{policyLinks.map((link, index) => (
								<li key={index}>
									<Link href={link.href} className='p-14-white hover:text-brand transition-colors'>
										{link.text}
									</Link>
								</li>
							))}
						</ul>
					</div>

					{/* Stay Connected Column */}
					<div>
						<h3 className='text-brand font-semibold mb-6'>Stay Connected</h3>
						<div className='space-y-5'>
							<p className='p-15-white font-medium'>Exchanger BD</p>

							{storeLocations.map((location, index) => (
								<div key={index}>
									{location.lines.map((line, lineIndex) => (
										<p key={lineIndex} className={`p-14-white ${lineIndex === 0 ? "mb-1" : ""}`}>
											{line}
										</p>
									))}
								</div>
							))}

							<div>
								<p className='p-14-white mb-1'>
									Email:{" "}
									<Link href='mailto:<EMAIL>' className='text-brand hover:underline'>
										<EMAIL>
									</Link>
								</p>
							</div>
						</div>
					</div>
				</div>
			</MaxWidthWrapper>

			{/* Compare Button */}
			<div className='fixed bottom-24 md:bottom-6 right-6 z-50'>
				<Link
					href='/compare'
					className='flex flex-col items-center justify-center bg-black border border-brand rounded-md p-2 hover:bg-brand-dark-800 transition-colors'
				>
					<svg width='24' height='24' viewBox='0 0 24 24' fill='none' xmlns='http://www.w3.org/2000/svg'>
						<path
							d='M9 22H15C20 22 22 20 22 15V9C22 4 20 2 15 2H9C4 2 2 4 2 9V15C2 20 4 22 9 22Z'
							stroke='#FFC700'
							strokeWidth='1.5'
							strokeLinecap='round'
							strokeLinejoin='round'
						/>
						<path
							d='M7.33008 14.49L9.71008 11.4C10.0501 10.96 10.6801 10.88 11.1201 11.22L12.9501 12.66C13.3901 13 14.0201 12.92 14.3601 12.49L16.6701 9.51001'
							stroke='#FFC700'
							strokeWidth='1.5'
							strokeLinecap='round'
							strokeLinejoin='round'
						/>
					</svg>
					<span className='text-brand text-xs mt-1'>Compare</span>
				</Link>
			</div>

			{/* Copyright */}
			<div className='border-t border-[#322c02] py-4'>
				<div className='container mx-auto px-4'>
					<p className='p-12-white text-center'>
						© {new Date().getFullYear()} Thanks From Exchanger BD | All rights reserved
					</p>
				</div>
			</div>
		</footer>
	);
}
