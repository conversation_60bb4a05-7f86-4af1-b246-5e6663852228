import { JSX, memo } from "react";
import { Button } from "../ui/button";
import { Pagination, PaginationContent, PaginationItem, PaginationLink } from "../ui/pagination";

const PaginationCompo = ({
	page,
	totalPages,
	setPage,
	className
}: {
	page: number;
	totalPages: number;
	setPage: (page: number) => void;
	className?: string;
}) => {
	const handlePageChange = (newPage: number) => {
		if (newPage >= 1 && newPage <= totalPages) {
			setPage(newPage);
		}
	};

	const renderPaginationItems = () => {
		const items: JSX.Element[] = [];
		for (let i = 1; i <= totalPages; i++) {
			items.push(
				<PaginationItem key={i}>
					<PaginationLink
						isActive={i === page}
						href='#'
						onClick={(e) => {
							e.preventDefault();
							handlePageChange(i);
						}}
					>
						{i}
					</PaginationLink>
				</PaginationItem>
			);
		}
		return items;
	};

	if (totalPages === 1) return null;

	return (
		<Pagination aria-label='Page navigation example' className={className}>
			<PaginationContent className='flex justify-center flex-wrap'>
				{/* Previous Button */}
				<PaginationItem>
					<Button variant='outline' size={"icon"} onClick={() => handlePageChange(page - 1)} disabled={page === 1}>
						<span aria-hidden='true'>&laquo;</span>
					</Button>
				</PaginationItem>

				{/* Pagination Numbers */}
				{renderPaginationItems()}

				{/* Next Button */}
				<PaginationItem>
					<Button
						variant='outline'
						size={"icon"}
						onClick={() => handlePageChange(page + 1)}
						disabled={page === totalPages}
					>
						<span aria-hidden='true'>&raquo;</span>
					</Button>
				</PaginationItem>
			</PaginationContent>
		</Pagination>
	);
};

export default memo(PaginationCompo);
