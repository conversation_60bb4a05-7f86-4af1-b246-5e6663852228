"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Check, Heart, RotateCcw, ShoppingCart, Star, Truck } from "lucide-react";
import Image from "next/image";
import { useState } from "react";

interface ProductInfoProps {
	product: any; // Using any for simplicity, but you should define a proper type
}

export default function ProductInfo({ product }: ProductInfoProps) {
	const [selectedVariant, setSelectedVariant] = useState<any>(null);
	const [quantity, setQuantity] = useState(1);

	const handleVariantChange = (variantIndex: string) => {
		const index = Number.parseInt(variantIndex);
		setSelectedVariant(product.variants[index]);
	};

	const handleQuantityChange = (value: string) => {
		setQuantity(Number.parseInt(value));
	};

	const displayPrice = selectedVariant ? selectedVariant.price : product.price;
	const displayActualPrice = selectedVariant ? selectedVariant.price : product.actualPrice;
	const formattedPrice = displayPrice.toLocaleString("en-IN");
	const formattedActualPrice = (displayActualPrice / 100).toLocaleString("en-IN");

	const discount = Math.round(((product.actualPrice / 100 - product.price) / (product.actualPrice / 100)) * 100);

	return (
		<div className='flex flex-col gap-6'>
			{/* Brand Name */}
			<div className='flex items-center gap-1'>
				<Image height={10} width={10} src='https://cdn.worldvectorlogo.com/logos/apple-14.svg' alt='Apple Logo' />
				<p className='p-14-black font-semibold'>Apple</p>
			</div>
			{/* Product Title */}
			<h1 className='h1-24-black'>{product.name}</h1>

			{/* Ratings */}
			<div className='flex items-center gap-2'>
				<div className='flex'>
					{[1, 2, 3, 4, 5].map((star) => (
						<Star key={star} className={`h-4 w-4 ${star <= 4 ? "fill-brand text-brand" : "text-gray-300"}`} />
					))}
				</div>
				<span className='p-13-black'>4.0</span>
				<span className='p-13-black text-gray-500'>(125 reviews)</span>
			</div>

			{/* Price */}
			<div className='flex items-center gap-3'>
				<span className='h1-20-black'>₹{formattedPrice}</span>
				<span className='p-14-black line-through text-gray-500'>₹{formattedActualPrice}</span>
				<Badge className='bg-brand text-brand-black'>{discount}% OFF</Badge>
			</div>

			{/* Variants */}
			{product.variants && product.variants.length > 0 && (
				<div className='space-y-4'>
					{/* Storage Variant */}
					{product.variants.some((v: any) => v.storage) && (
						<div>
							<Label htmlFor='storage' className='p-14-black mb-2 block'>
								Storage
							</Label>
							<RadioGroup
								id='storage'
								defaultValue='0'
								onValueChange={handleVariantChange}
								className='flex flex-wrap gap-2'
							>
								{product.variants.map(
									(variant: any, index: number) =>
										variant.storage && (
											<Label
												key={index}
												htmlFor={`storage-${index}`}
												className='border cursor-pointer rounded-md p-2 flex items-center gap-2 [&:has(:checked)]:bg-brand [&:has(:checked)]:text-brand-black'
											>
												<RadioGroupItem id={`storage-${index}`} value={index.toString()} />
												{variant.storage}
											</Label>
										)
								)}
							</RadioGroup>
						</div>
					)}

					{/* Region Variant */}
					{product.variants.some((v: any) => v.region) && (
						<div>
							<Label htmlFor='region' className='p-14-black mb-2 block'>
								Region
							</Label>
							<RadioGroup
								id='region'
								defaultValue='0'
								onValueChange={handleVariantChange}
								className='flex flex-wrap gap-2'
							>
								{product.variants.map(
									(variant: any, index: number) =>
										variant.region && (
											<Label
												key={index}
												htmlFor={`region-${index}`}
												className='border cursor-pointer rounded-md p-2 flex items-center gap-2 [&:has(:checked)]:bg-brand [&:has(:checked)]:text-brand-black'
											>
												<RadioGroupItem id={`region-${index}`} value={index.toString()} />
												{variant.region}
											</Label>
										)
								)}
							</RadioGroup>
						</div>
					)}

					{/* Condition Variant */}
					{product.variants.some((v: any) => v.condition) && (
						<div>
							<Label htmlFor='condition' className='p-14-black mb-2 block'>
								Condition
							</Label>
							<RadioGroup
								id='condition'
								defaultValue='0'
								onValueChange={handleVariantChange}
								className='flex flex-wrap gap-2'
							>
								{product.variants.map(
									(variant: any, index: number) =>
										variant.condition && (
											<Label
												key={index}
												htmlFor={`condition-${index}`}
												className='border cursor-pointer rounded-md p-2 flex items-center gap-2 [&:has(:checked)]:bg-brand [&:has(:checked)]:text-brand-black'
											>
												<RadioGroupItem id={`condition-${index}`} value={index.toString()} />
												{variant.condition}
											</Label>
										)
								)}
							</RadioGroup>
						</div>
					)}
				</div>
			)}

			{/* Quantity */}
			<div>
				<Label htmlFor='quantity' className='p-14-black mb-2 block'>
					Quantity
				</Label>
				<Select defaultValue='1' onValueChange={handleQuantityChange}>
					<SelectTrigger className='w-24'>
						<SelectValue placeholder='Select' />
					</SelectTrigger>
					<SelectContent>
						{[1, 2, 3, 4, 5].map((num) => (
							<SelectItem key={num} value={num.toString()}>
								{num}
							</SelectItem>
						))}
					</SelectContent>
				</Select>
			</div>

			{/* Stock Status */}
			<div className='flex items-center gap-2'>
				<Check className='h-4 w-4 text-green-500' />
				<span className='p-13-black text-green-500'>In Stock ({product.stock} available)</span>
			</div>

			{/* Action Buttons */}
			<div className='flex flex-col sm:flex-row gap-3'>
				<Button className='bg-brand hover:bg-brand/90 text-brand-black p-16-black'>
					<ShoppingCart className='h-5 w-5 mr-2' />
					Add to Cart
				</Button>
				<Button variant='outline' className='border-brand text-brand hover:bg-brand/10 p-16-black'>
					<Heart className='h-5 w-5 mr-2' />
					Add to Wishlist
				</Button>
			</div>

			{/* Delivery Info */}
			<div className='border-t border-b py-4 space-y-3'>
				<div className='flex items-start gap-3'>
					<Truck className='h-5 w-5 text-brand-black mt-0.5' />
					<div>
						<p className='p-14-black font-medium'>Free Delivery</p>
						<p className='p-12-black text-gray-500'>Enter your postal code for delivery availability</p>
					</div>
				</div>
				<div className='flex items-start gap-3'>
					<RotateCcw className='h-5 w-5 text-brand-black mt-0.5' />
					<div>
						<p className='p-14-black font-medium'>Return Policy</p>
						<p className='p-12-black text-gray-500'>Free 30-day return policy</p>
					</div>
				</div>
			</div>

			{/* Short Description */}
			<div>
				<p className='p-14-black'>{product.description}</p>
			</div>
		</div>
	);
}
