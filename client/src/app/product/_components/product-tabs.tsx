"use client";

import { motion } from "framer-motion";
import { useState } from "react";

export default function ProductTabs({ additionalInfo, description, warrantyInfo }: any) {
	const [activeTab, setActiveTab] = useState<"specification" | "description" | "warranty">("specification");

	return (
		<div className='mb-16'>
			{/* Tabs */}
			<div className='flex flex-wrap border bg-brand-light-100 overflow-x-auto'>
				<button
					className={`px-3 sm:px-4 md:px-8 py-2 sm:py-3 font-medium text-sm sm:text-base whitespace-nowrap flex-1 sm:flex-none ${
						activeTab === "specification"
							? "bg-brand text-brand-black"
							: "bg-brand-light-100 text-gray-700 hover:bg-brand-light-200"
					}`}
					onClick={() => setActiveTab("specification")}
				>
					Specification
				</button>
				<button
					className={`px-3 sm:px-4 md:px-8 py-2 sm:py-3 font-medium text-sm sm:text-base whitespace-nowrap flex-1 sm:flex-none ${
						activeTab === "description"
							? "bg-brand text-brand-black"
							: "bg-brand-light-100 text-gray-700 hover:bg-gray-100"
					}`}
					onClick={() => setActiveTab("description")}
				>
					Description
				</button>
				<button
					className={`px-3 sm:px-4 md:px-8 py-2 sm:py-3 font-medium text-sm sm:text-base whitespace-nowrap flex-1 sm:flex-none ${
						activeTab === "warranty"
							? "bg-brand text-brand-black"
							: "bg-brand-light-100 text-gray-700 hover:bg-gray-100"
					}`}
					onClick={() => setActiveTab("warranty")}
				>
					Warranty
				</button>
			</div>

			{/* Tab Content */}
			<div className='border border-t-0 p-3 sm:p-4 md:p-6'>
				{activeTab === "specification" && (
					<motion.div initial={{ opacity: 0 }} animate={{ opacity: 1 }} transition={{ duration: 0.3 }}>
						<h2 className='text-lg sm:text-xl font-bold mb-4 pb-2 border-b border-brand inline-block'>Specification</h2>
						<div className='overflow-x-auto -mx-3 sm:mx-0'>
							<table className='w-full min-w-[500px]'>
								<tbody>
									{additionalInfo?.map((item: any, index: number) => (
										<tr className='border-b' key={index}>
											<td className='py-2 sm:py-3 px-3 sm:px-4 font-medium w-1/3 sm:w-1/4 md:w-1/5 align-top'>
												{item?.key}
											</td>
											<td className='py-2 sm:py-3 px-3 sm:px-4'>{item?.value}</td>
										</tr>
									))}
								</tbody>
							</table>
						</div>
					</motion.div>
				)}

				{activeTab === "description" && (
					<motion.div
						initial={{ opacity: 0 }}
						animate={{ opacity: 1 }}
						transition={{ duration: 0.3 }}
						className='rich-text-content'
					>
						<h2 className='text-lg sm:text-xl font-bold mb-4 pb-2 border-b border-brand inline-block'>Description</h2>
						<div
							className='prose prose-sm sm:prose prose-headings:font-bold prose-headings:text-gray-900 prose-p:text-gray-700 prose-img:rounded-lg prose-img:my-8 prose-li:text-gray-700 max-w-none'
							dangerouslySetInnerHTML={{ __html: description }}
						/>
					</motion.div>
				)}

				{activeTab === "warranty" && (
					<motion.div
						initial={{ opacity: 0 }}
						animate={{ opacity: 1 }}
						transition={{ duration: 0.3 }}
						className='prose prose-sm sm:prose max-w-none'
					>
						<h2 className='text-lg sm:text-xl font-bold mb-4 pb-2 border-b border-brand inline-block'>Warranty</h2>
						<div dangerouslySetInnerHTML={{ __html: warrantyInfo || "No warranty information available." }} />
					</motion.div>
				)}
			</div>
		</div>
	);
}
