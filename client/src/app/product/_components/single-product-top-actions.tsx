"use client";

import { Popover, <PERSON>overContent, PopoverTrigger } from "@/components/ui/popover";
import { useToast } from "@/hooks/use-toast";
import { Clipboard, Home, Share2, ShoppingCart } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import {
	FacebookIcon,
	FacebookShareButton,
	LinkedinIcon,
	LinkedinShareButton,
	TwitterIcon,
	TwitterShareButton,
	WhatsappIcon,
	WhatsappShareButton
} from "react-share";

const SingleProductTopActions = ({ product }: any) => {
	const router = useRouter();
	const { toast } = useToast();
	const url = `${window.location.href}`;
	const title = product?.name;
	return (
		<div className='flex flex-col sm:flex-row sm:items-center sm:justify-between mb-6 sm:mb-12 mt-4 sm:mt-6 gap-4'>
			<div className='flex items-center gap-2 text-sm overflow-x-auto pb-2 sm:pb-0'>
				<Link href='/' className='text-gray-800 whitespace-nowrap'>
					<Home size={16} className='inline mr-1' />
				</Link>
				<span>/</span>
				<Link href={`/products/${product?.primaryCategory?.slug}`} className='text-gray-800 whitespace-nowrap'>
					{product?.primaryCategory?.name}
				</Link>
				<span>/</span>
				<span className='text-gray-500 whitespace-nowrap'>{product?.name}</span>
			</div>
			<div className='flex items-center gap-2'>
				<button
					onClick={() => {
						router.push(`/compare?device=${product?.slug}`);
					}}
					className='flex items-center py-2 px-3 bg-brand-light-100 text-brand-dark-700 font-medium hover:bg-brand-light-200 rounded-sm text-xs sm:text-sm'
				>
					<span className='mr-2'>
						<ShoppingCart size={16} className='sm:size-18' />
					</span>
					<span className='whitespace-nowrap'>Add to compare</span>
				</button>
				<span>|</span>
				{/* share to social medias */}
				<Popover>
					<PopoverTrigger asChild>
						<button className='p-2 rounded hover:bg-muted transition'>
							<Share2 size={18} className='text-brand-dark-700' />
						</button>
					</PopoverTrigger>
					<PopoverContent className='flex flex-col gap-3 p-4 w-fit'>
						<div
							onClick={() => {
								navigator.clipboard.writeText(url);
								toast({
									title: "Link copied",
									description: "The product link has been copied to your clipboard."
								});
							}}
							className=' cursor-pointer'
							title='Copy Link'
						>
							<Clipboard size={30} className='text-sky-700' />
						</div>
						<FacebookShareButton className=' cursor-pointer' url={url} title={title}>
							<FacebookIcon size={32} round />
						</FacebookShareButton>
						<TwitterShareButton url={url} title={title}>
							<TwitterIcon size={32} round />
						</TwitterShareButton>
						<LinkedinShareButton url={url} title={title}>
							<LinkedinIcon size={32} round />
						</LinkedinShareButton>
						<WhatsappShareButton url={url} title={title}>
							<WhatsappIcon size={32} round />
						</WhatsappShareButton>
					</PopoverContent>
				</Popover>
			</div>
		</div>
	);
};

export default SingleProductTopActions;
