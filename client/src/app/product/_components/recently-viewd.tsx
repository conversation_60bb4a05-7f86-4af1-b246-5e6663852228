import { useGetMinimalProductsQuery } from "@/redux/apis/productApis";
import { getRecentlyViewed } from "@/utils/useRecentlyViewed";
import { User } from "lucide-react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import { useMemo } from "react";

export default function RecentlyViewed() {
	const router = useRouter();
	// Get IDs from localStorage once
	const viewed = useMemo(() => {
		if (typeof window === "undefined") return []; // Safe for SSR
		return getRecentlyViewed(); // returns string[]
	}, []);

	// Convert to comma-separated string
	const productIdString = viewed.join(",");

	const skip = viewed.length === 0;

	const {
		data: products = [],
		isLoading,
		isError
	} = useGetMinimalProductsQuery([{ name: "_ids", value: productIdString }], { skip });
	const handleBuyNow = (id: string) => {
		if (!id) return;
		router.push(`/checkout?buyNow=${id}&quantity=${1}`);
	};

	return (
		<div className='border rounded-lg'>
			<h2 className='text-xl font-bold text-brand-black mb-4 p-4 bg-brand-light-100'>Recently Viewed</h2>
			<div className='space-y-4 p-4'>
				{isLoading && <p className='text-sm text-gray-500'>Loading...</p>}
				{isError && <p className='text-sm text-red-500'>Failed to load recently viewed products.</p>}

				{!isLoading && !isError && products?.data?.length === 0 && (
					<p className='text-sm text-gray-400'>You haven't viewed any products yet.</p>
				)}
				{!isLoading &&
					!isError &&
					products &&
					products?.data?.length > 0 &&
					products?.data?.map((product) => (
						<div key={product._id} className='flex items-center gap-4 pb-4 border-b last:border-b-0'>
							<div className='w-16 h-16 relative'>
								<Image
									src={product?.images[0] || "/placeholder.svg"}
									alt={product.name}
									fill
									className='object-contain'
								/>
							</div>
							<div className='flex-1'>
								<h3 className='font-medium text-sm line-clamp-2'>{product.name}</h3>
								<p className='text-brand-dark-100 font-bold'>{product.price}₹</p>
							</div>
							<div className='flex flex-col gap-2'>
								<button
									onClick={() => handleBuyNow(product?._id)}
									className='bg-brand text-brand-black text-xs px-3 py-1 rounded'
								>
									Buy Now
								</button>
								<button
									onClick={() => {
										router.push(`/compare?device=${product?.slug}`);
									}}
									className='text-xs flex items-center justify-center gap-1'
								>
									<User className='h-3 w-3' />
									Add to compare
								</button>
							</div>
						</div>
					))}
			</div>
		</div>
	);
}
