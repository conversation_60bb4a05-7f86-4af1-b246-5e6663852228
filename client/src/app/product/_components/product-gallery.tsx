"use client";

import { cn } from "@/lib/utils";
import { ChevronLeft, ChevronRight } from "lucide-react";
import Image from "next/image";
import { useState } from "react";

interface ProductGalleryProps {
	images: string[];
	variants?: Array<{
		color?: {
			name: string;
			value: string;
		};
		images?: string[];
	}>;
}

export default function ProductGallery({ images, variants }: ProductGalleryProps) {
	const [currentImageIndex, setCurrentImageIndex] = useState(0);
	const [activeImages, setActiveImages] = useState(images);

	// Combine all images from main product and variants
	const allImages = [...images];
	variants?.forEach((variant) => {
		if (variant.images && variant.images.length > 0) {
			allImages.push(...variant.images);
		}
	});

	// Remove duplicates
	const uniqueImages = [...new Set(allImages)];

	const handleThumbnailClick = (index: number) => {
		setCurrentImageIndex(index);
	};

	const handlePrevious = () => {
		setCurrentImageIndex((prev) => (prev === 0 ? uniqueImages.length - 1 : prev - 1));
	};

	const handleNext = () => {
		setCurrentImageIndex((prev) => (prev === uniqueImages.length - 1 ? 0 : prev + 1));
	};

	const handleVariantClick = (variantImages?: string[]) => {
		if (variantImages && variantImages.length > 0) {
			setActiveImages(variantImages);
			setCurrentImageIndex(0);
		} else {
			setActiveImages(images);
			setCurrentImageIndex(0);
		}
	};

	return (
		<div className='flex flex-col gap-4'>
			{/* Main Image */}
			<div className='relative aspect-square rounded-md overflow-hidden border border-gray-200'>
				<Image
					src={uniqueImages[currentImageIndex] || "/placeholder.svg"}
					alt='Product image'
					fill
					className='object-cover'
					priority
				/>

				{/* Navigation Arrows */}
				<button
					onClick={handlePrevious}
					className='absolute left-2 top-1/2 -translate-y-1/2 bg-white rounded-full p-1 shadow-md'
					aria-label='Previous image'
				>
					<ChevronLeft className='h-6 w-6 text-brand-black' />
				</button>
				<button
					onClick={handleNext}
					className='absolute right-2 top-1/2 -translate-y-1/2 bg-white rounded-full p-1 shadow-md'
					aria-label='Next image'
				>
					<ChevronRight className='h-6 w-6 text-brand-black' />
				</button>
			</div>

			{/* Thumbnails */}
			<div className='flex gap-2 overflow-x-auto pb-2'>
				{uniqueImages.map((image, index) => (
					<button
						key={index}
						onClick={() => handleThumbnailClick(index)}
						className={cn(
							"relative w-16 h-16 rounded-md overflow-hidden border-2",
							currentImageIndex === index ? "border-brand" : "border-gray-200"
						)}
					>
						<Image
							src={image || "/placeholder.svg"}
							alt={`Product thumbnail ${index + 1}`}
							fill
							className='object-cover'
						/>
					</button>
				))}
			</div>

			{/* Color Variants */}
			{variants && variants.length > 0 && variants.some((v) => v.color) && (
				<div className='mt-4'>
					<p className='p-13-black mb-2'>Color</p>
					<div className='flex gap-2'>
						<button
							onClick={() => handleVariantClick(images)}
							className={cn(
								"w-8 h-8 rounded-full border-2",
								activeImages === images ? "border-brand" : "border-gray-200"
							)}
							style={{ backgroundColor: "#000000" }}
							aria-label='Default color'
						/>
						{variants.map(
							(variant, index) =>
								variant.color && (
									<button
										key={index}
										onClick={() => handleVariantClick(variant.images)}
										className={cn(
											"w-8 h-8 rounded-full border-2",
											activeImages === variant.images ? "border-brand" : "border-gray-200"
										)}
										style={{ backgroundColor: variant.color.value }}
										aria-label={variant.color.name}
									/>
								)
						)}
					</div>
				</div>
			)}
		</div>
	);
}
