"use client";

import type React from "react";

import { PlusIcon as HouseP<PERSON>, Maximize2, MessageCircle, Minus, Plus, X } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useRef, useState } from "react";

import { Dialog, DialogClose, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { Toaster } from "@/components/ui/toaster";
import { toast } from "@/hooks/use-toast";
import { useCart } from "@/hooks/useCart";
import { extractVariantOptions } from "@/utils/extractVariantOptions";
import { addToRecentlyViewed } from "@/utils/useRecentlyViewed";
import { AnimatePresence, motion } from "framer-motion";
import { useRouter } from "next/navigation";
import ProductTabs from "../_components/product-tabs";
import RecentlyViewed from "../_components/recently-viewd";
import RelatedProducts from "./related-products";
import SingleProductTopActions from "./single-product-top-actions";

// Recently viewed products data
const recentlyViewedProducts = [
	{
		id: "anker-547",
		name: "Anker 547 PowerExpand 7-in-1 USB-C Hub",
		price: 4900,
		image: "/nothing-phone.jpg"
	},
	{
		id: "iphone-16",
		name: "iPhone 16",
		price: 96500,
		image: "/nothing-phone.jpg"
	},
	{
		id: "galaxy-s25",
		name: "Galaxy S25 Ultra 5G",
		price: 122500,
		image: "/nothing-phone.jpg"
	},
	{
		id: "ipad-11",
		name: "iPad 11th Gen - 2025",
		price: 49500,
		image: "/nothing-phone.jpg"
	}
];

export default function SingleProductPage({ data }: any) {
	// State for product selections
	const [quantity, setQuantity] = useState(1);
	const [imageModalOpen, setImageModalOpen] = useState(false);
	const [allImages, setAllImages] = useState([data?.images]);
	const [variants, setVariants] = useState(data?.variants || []);
	const [selectedImage, setSelectedImage] = useState(allImages[0]);
	const { addItem } = useCart();
	const router = useRouter();

	useEffect(() => {
		const variantImgs = Array.isArray(data?.variants) ? data.variants.flatMap((v: any) => v.images || []) : [];
		const allImgs = [...data.images, ...variantImgs];
		setVariants(data?.variants);
		setAllImages(allImgs);
		setSelectedImage(allImgs[0]);
	}, [data]);
	useEffect(() => {
		if (data?._id) {
			addToRecentlyViewed(data._id);
		}
	}, [data?._id]);

	// State for image zoom
	const [showZoom, setShowZoom] = useState(false);
	const [zoomPosition, setZoomPosition] = useState({ x: 0, y: 0 });
	const imageContainerRef = useRef<HTMLDivElement>(null);
	const [selectedOptions, setSelectedOptions] = useState({
		color: null,
		region: null,
		storage: null,
		condition: null
	});

	const [matchedVariant, setMatchedVariant] = useState<any>(null);
	// Handle quantity change
	const decreaseQuantity = () => {
		if (quantity > 1) {
			setQuantity(quantity - 1);
		}
	};

	const increaseQuantity = () => {
		setQuantity(quantity + 1);
	};

	const handleQuantityChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const value = Number.parseInt(e.target.value);
		if (!isNaN(value) && value > 0) {
			setQuantity(value);
		}
	};

	// Handle add to cart
	const handleAddToCart = (e: React.MouseEvent) => {
		if (variants.length > 0 && !matchedVariant) {
			toast({
				title: "Failed to add to cart!",
				description: `Please select at least one variant!`
			});
			return;
		}
		e.stopPropagation();
		e.preventDefault();
		addItem({
			id: data?._id,
			quantity: quantity,
			...(matchedVariant && { variantId: matchedVariant?._id })
		});
	};

	// Handle buy now
	const handleBuyNow = () => {
		if (!data?._id) return;
		if (variants.length > 0 && !matchedVariant) {
			toast({
				title: "Failed to buy now!",
				description: `Please select at least one variant!`
			});
			return;
		}

		router.push(`/checkout?buyNow=${data._id}&quantity=${quantity}`);
	};

	const handleMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
		if (imageContainerRef.current) {
			const { left, top, width, height } = imageContainerRef.current.getBoundingClientRect();

			// Calculate relative position within the image (0 to 1)
			const relativeX = (e.clientX - left) / width;
			const relativeY = (e.clientY - top) / height;

			// Ensure values are within bounds
			const boundedX = Math.max(0, Math.min(1, relativeX));
			const boundedY = Math.max(0, Math.min(1, relativeY));

			// Set the position for the zoom window
			setZoomPosition({ x: boundedX, y: boundedY });
		}
	};
	useEffect(() => {
		// Step 1: Try exact match first
		const exactMatch = variants.find(
			(variant) =>
				variant.color?.name === selectedOptions.color &&
				variant.region === selectedOptions.region &&
				variant.storage === selectedOptions.storage &&
				variant.condition === selectedOptions.condition
		);

		if (exactMatch) {
			setMatchedVariant(exactMatch);
			return;
		}

		// Step 2: Weighted matching based on priority
		const priorityWeights = {
			storage: 4,
			region: 3,
			condition: 2,
			color: 1
		};

		const bestMatch =
			variants
				.map((variant) => {
					let score = 0;
					if (selectedOptions.storage && variant.storage === selectedOptions.storage) score += priorityWeights.storage;
					if (selectedOptions.region && variant.region === selectedOptions.region) score += priorityWeights.region;
					if (selectedOptions.condition && variant.condition === selectedOptions.condition)
						score += priorityWeights.condition;
					if (selectedOptions.color && variant.color?.name === selectedOptions.color) score += priorityWeights.color;

					return { variant, score };
				})
				.sort(
					(a, b) => (b.score !== a.score ? b.score - a.score : b.variant.stock - a.variant.stock) // tie-breaker: more stock
				)[0]?.variant || null;

		setMatchedVariant(bestMatch);

		if (
			variants.length &&
			selectedOptions.color == null &&
			selectedOptions.condition === null &&
			selectedOptions.region === null &&
			selectedOptions.storage === null
		) {
			setMatchedVariant(null);
		}
	}, [selectedOptions, variants]);

	const variantOptions = extractVariantOptions(variants);
	// console.log({ variantOptions, variants, selectedOptions, matchedVariant }, "single_product");

	return (
		<div className='max-w-7xl mx-auto px-4 sm:px-6 py-4 sm:py-6'>
			{/* Breadcrumb */}
			<SingleProductTopActions product={data} />

			<div className='grid grid-cols-1 md:grid-cols-12 gap-4 md:gap-8'>
				{/* Product Image Section */}
				<div className='relative border rounded-lg p-3 sm:p-4 col-span-1 md:col-span-5'>
					<div className='absolute top-4 left-4 z-10 bg-brand text-brand-black px-2 py-1 text-[12px] font-medium rounded'>
						{(((data?.price - data?.actualPrice) / data?.actualPrice) * 100).toFixed(2)} % OFF
					</div>

					{/* Main product image with zoom functionality */}
					<div
						className='flex justify-center mb-4 h-[300px] sm:h-[400px] relative cursor-zoom-in'
						ref={imageContainerRef}
						onMouseEnter={() => {
							setShowZoom(true);
						}}
						onMouseLeave={() => {
							setShowZoom(false);
						}}
						onMouseMove={handleMouseMove}
					>
						<AnimatePresence mode='wait'>
							<motion.div
								key={selectedImage}
								initial={{ opacity: 0 }}
								animate={{ opacity: 1 }}
								exit={{ opacity: 0 }}
								transition={{ duration: 0.3, ease: "easeInOut" }}
								className='absolute inset-0 flex items-center justify-center'
							>
								<Image
									src={selectedImage || "/placeholder.svg"}
									alt={`${data.name} - (zoomed)`}
									width={400}
									height={500}
									className='object-contain'
								/>
							</motion.div>
						</AnimatePresence>

						{/* Zoom view - fixed on the right side using absolute positioning with larger dimensions */}
						{showZoom && (
							<div
								className='hidden md:block fixed z-20 w-[400px] h-[500px] border-2 border-gray-200 overflow-hidden bg-white shadow-xl rounded-lg'
								style={{
									left: imageContainerRef.current ? imageContainerRef.current.getBoundingClientRect().right + 20 : 0,
									top: imageContainerRef.current ? imageContainerRef.current.getBoundingClientRect().top : 0
								}}
							>
								<div
									className='w-[1200px] h-[1500px] relative'
									style={{
										transform: `translate(${-zoomPosition.x * 800}px, ${-zoomPosition.y * 1000}px)`
									}}
								>
									<Image
										src={selectedImage || "/placeholder.svg"}
										alt={`${data?.name} - (zoomed)`}
										fill
										className='object-contain'
									/>
								</div>
							</div>
						)}
					</div>

					<div className='absolute bottom-4 right-4'>
						<button
							className='border p-1 rounded bg-white hover:bg-gray-100 transition-colors'
							onClick={() => {
								setImageModalOpen(true);
							}}
						>
							<Maximize2 size={22} />
						</button>
					</div>

					{/* Images */}
					<div className='flex gap-2 mt-8 justify-center'>
						{allImages.map((image: any, index: number) => (
							<motion.div
								key={index}
								className={`border p-1 rounded cursor-pointer ${
									selectedImage === image ? "border border-brand" : "border"
								}`}
								onClick={() => setSelectedImage(image)}
								whileHover={{ scale: 1.05 }}
								whileTap={{ scale: 0.95 }}
								transition={{ duration: 0.2 }}
							>
								<Image
									src={image || "/placeholder.svg"}
									alt={`${data.name}`}
									width={60}
									height={80}
									className='object-contain'
								/>
							</motion.div>
						))}
					</div>
				</div>

				{/* Product Details Section */}
				<div className='col-span-1 md:col-span-7'>
					<div className='uppercase text-gray-500 text-[12px] mb-2'>{data?.brand?.name}</div>
					<h1 className='text-xl text-brand-black font-bold mb-4'>{data?.name}</h1>

					<div className='flex items-center flex-wrap gap-4 mb-4'>
						<div className='flex items-center bg-brand-light-100 px-[8px] py-[4px] text-[13px]'>
							<span className='text-brand-dark-800 mr-2'>Cash Discount Price:</span>
							<span className='line-through text-brand-dark-500 mr-2'>{data.actualPrice}$</span>
							<span className='font-bold text-xl text-brand-black'>{data?.price}$</span>
						</div>
						<div className='flex items-center bg-brand-light-100 px-[8px] py-[8px] text-[13px]'>
							<span className='text-brand-dark-800 mr-4'>Status:</span>
							<span className='text-green-500 font-bold'>{data?.stock > 0 ? "In Stock" : "Out of Stock"}</span>
						</div>
						<div className='flex items-center bg-brand-light-100 px-[8px] py-[8px] text-[13px]'>
							<span className='text-brand-dark-800 mr-4'>Product Code:</span>
							<span className='text-brand-black font-bold'>{data?.slug}</span>
						</div>
					</div>
					{/* EMI CTA */}
					<div className='flex flex-col sm:flex-row items-start sm:items-center justify-between gap-2 sm:gap-4 bg-gray-100 px-4 py-3 rounded-md text-sm sm:text-base mb-3'>
						<div className='flex items-center gap-2 text-gray-800'>
							<HousePlus size={18} className='text-brand-dark-300' />
							<span>EMI Available. Contact for more details</span>
						</div>
						<Link
							href='https://wa.me/+8801234567890'
							className='text-brand-dark-300 font-semibold hover:underline transition-all'
						>
							Contact <span className='pl-1'>&gt;</span>
						</Link>
					</div>
					{/* contact whatsapp CTA */}

					<Link
						href='https://wa.me/+8801234567890'
						className='inline-flex items-center gap-2 bg-gradient-to-r from-green-400 to-green-600 text-white px-4 py-1 rounded-sm mb-8 text-[11px]'
					>
						<MessageCircle size={18} />
						<span>
							Message
							<br />
							on Whatsapp
						</span>
					</Link>
					{/* variants management */}
					<div className='mb-6'>
						{variantOptions.color && (
							<div className='flex items-center gap-3'>
								<p>Color:</p>
								<div className='flex items-center gap-2'>
									{variantOptions.color.map((c, i) => (
										<button
											key={i}
											onClick={() => setSelectedOptions((prev) => ({ ...prev, color: c.value }))}
											style={{ backgroundColor: c.value, width: 24, height: 24 }}
											title={c.name}
											className={`rounded border-2 ${
												selectedOptions.color === c.value ? "border-brand" : "border-gray-400"
											}`}
										/>
									))}
								</div>
							</div>
						)}
						{variantOptions.region && (
							<div className='flex items-center gap-3 mt-3'>
								<p>Region:</p>
								<div className='flex items-center gap-2'>
									{variantOptions.region.map((r, i) => (
										<button
											key={i}
											onClick={() => setSelectedOptions((prev) => ({ ...prev, region: r }))}
											className={`px-4 py-1 rounded hover:bg-brand ${
												selectedOptions.region === r ? "bg-brand" : "bg-gray-200"
											}`}
										>
											{r}
										</button>
									))}
								</div>
							</div>
						)}

						{variantOptions.storage && (
							<div className='flex items-center gap-3 mt-3'>
								<p>Storage:</p>
								<div className='flex items-center gap-2'>
									{variantOptions.storage.map((s, i) => (
										<button
											key={i}
											onClick={() => setSelectedOptions((prev) => ({ ...prev, storage: s }))}
											className={`px-4 py-1 rounded hover:bg-brand ${
												selectedOptions.storage === s ? "bg-brand" : "bg-gray-200"
											}`}
										>
											{s}GB
										</button>
									))}
								</div>
							</div>
						)}

						{variantOptions.condition && (
							<div className='flex items-center gap-3 mt-3'>
								<p className='font-medium'>Condition:</p>
								<div className='flex items-center gap-2'>
									{variantOptions.condition.map((c, i) => (
										<button
											key={i}
											onClick={() => setSelectedOptions((prev) => ({ ...prev, condition: c }))}
											className={`px-4 py-1 rounded hover:bg-brand ${
												selectedOptions.condition === c ? "bg-brand" : "bg-gray-200"
											}`}
										>
											{c}
										</button>
									))}
								</div>
							</div>
						)}
					</div>
					{/* quantity and add to cart/buy now management */}
					<div className='flex flex-col sm:flex-row items-start sm:items-center gap-4 mt-8'>
						<div className='flex'>
							<motion.button
								className='border border-brand px-3 sm:px-4 py-2 rounded-l-md text-xl font-bold'
								onClick={decreaseQuantity}
								whileTap={{ scale: 0.95 }}
							>
								<Minus size={16} />
							</motion.button>
							<input
								type='text'
								value={quantity}
								onChange={handleQuantityChange}
								className='border-t border-b border-brand w-12 sm:w-16 text-center py-2'
							/>
							<motion.button
								className='border border-brand px-3 sm:px-4 py-2 rounded-r-md text-xl font-bold'
								onClick={increaseQuantity}
								whileTap={{ scale: 0.95 }}
							>
								<Plus size={16} />
							</motion.button>
						</div>

						<div className='flex gap-2 w-full sm:w-auto'>
							<motion.button
								className='bg-brand text-brand-black px-4 sm:px-8 py-2 rounded-sm font-medium flex-1 sm:flex-none'
								onClick={handleBuyNow}
								whileHover={{ scale: 1.03 }}
								whileTap={{ scale: 0.97 }}
								transition={{ duration: 0.2 }}
							>
								Buy Now
							</motion.button>

							<motion.button
								className='border border-brand text-brand-dark-800 px-4 sm:px-6 py-2 rounded-sm font-medium flex-1 sm:flex-none'
								onClick={handleAddToCart}
								whileHover={{ scale: 1.03 }}
								whileTap={{ scale: 0.97 }}
								transition={{ duration: 0.2 }}
							>
								Add to Cart
							</motion.button>
						</div>
					</div>
				</div>
			</div>

			<RelatedProducts category={data?.primaryCategory?.slug} />

			{/* Product Information Tabs and Recently Viewed Section */}
			<div className='mt-8 sm:mt-12 grid grid-cols-1 md:grid-cols-3 gap-6 md:gap-8'>
				<div className='md:col-span-2'>
					<ProductTabs
						additionalInfo={data?.additionalInfo}
						description={data?.description}
						warrantyInfo={data?.warrantyInfo}
					/>
				</div>
				<div>
					<RecentlyViewed />
				</div>
			</div>

			{/* Image Modal */}
			<Dialog open={imageModalOpen} onOpenChange={setImageModalOpen}>
				<DialogTitle></DialogTitle>
				<DialogContent className='sm:max-w-[90vw] sm:max-h-[90vh] max-w-[95vw] max-h-[95vh] p-0 overflow-hidden'>
					<div className='relative w-full h-full flex items-center justify-center bg-black'>
						<DialogClose className='absolute top-2 sm:top-4 right-2 sm:right-4 z-10'>
							<motion.button
								className='rounded-full bg-white/10 p-1 sm:p-2 hover:bg-white/20 transition-colors'
								whileHover={{ scale: 1.1 }}
								whileTap={{ scale: 0.9 }}
							>
								<X className='h-5 w-5 sm:h-6 sm:w-6 text-white' />
							</motion.button>
						</DialogClose>
						<div className='w-full h-full flex items-center justify-center p-2 sm:p-4'>
							<AnimatePresence mode='wait'>
								<motion.div
									key={selectedImage}
									initial={{ opacity: 0 }}
									animate={{ opacity: 1 }}
									exit={{ opacity: 0 }}
									transition={{ duration: 0.3 }}
									className='w-full h-full flex items-center justify-center'
								>
									<Image
										src={selectedImage || "/placeholder.svg"}
										alt={data?.name || "No image found"}
										width={800}
										height={1000}
										className='object-contain max-h-[80vh]'
									/>
								</motion.div>
							</AnimatePresence>
						</div>
					</div>
				</DialogContent>
			</Dialog>

			<Toaster />
		</div>
	);
}
