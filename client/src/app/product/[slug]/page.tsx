import MaxWidthWrapper from "@/components/ui/max-width-wrapper";
import { fetchFromServer } from "@/utils/fetchFromServer";
import SingleProductPage from "../_components/single-product-page";

export async function generateMetadata({ params }: { params: Promise<{ slug: string }> }) {
	const resolvedParams = await params;

	const res = await fetchFromServer(`/products/slug/${resolvedParams.slug}`);
	if (!res?.data) {
		return {
			title: "Product Not Found | Exchanger BD",
			description: "The requested product could not be found."
		};
	}

	return {
		title: `${res.data.name} | Exchanger BD`,
		description: `${res.data.description || "Exchanger BD"}`
	};
}

const page = async ({ params }: { params: Promise<{ slug: string }> }) => {
	const resolvedParams = await params;
	const res = await fetchFromServer(`/products/slug/${resolvedParams.slug}`);

	if (!res?.data) {
		return (
			<MaxWidthWrapper>
				<div className='text-center py-20 h-[65vh] flex flex-col justify-center items-center'>
					<h1 className='text-2xl font-semibold'>Product not found</h1>
					<p className='text-gray-500 mt-2'>The product you are looking for does not exist or an error occurred.</p>
				</div>
			</MaxWidthWrapper>
		);
	}

	return (
		<MaxWidthWrapper>
			<SingleProductPage data={res.data} />
		</MaxWidthWrapper>
	);
};

export default page;
