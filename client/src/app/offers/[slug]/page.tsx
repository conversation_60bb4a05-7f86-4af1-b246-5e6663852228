import MaxWidthWrapper from "@/components/ui/max-width-wrapper";
import { fetchFromServer } from "@/utils/fetchFromServer";
import SingleOfferPage from "../_components/SingleOfferPage";

export async function generateMetadata({ params }: { params: Promise<{ slug: string }> }) {
	const resolvedParams = await params;
	const res = await fetchFromServer(`/offers/slug/${resolvedParams.slug}`);
	return {
		title: `${res?.data?.name} || Exchanger BD`,
		description: `${res?.data?.description} || Exchanger BD`
	};
}

const Page = async ({ params }: { params: Promise<{ slug: string }> }) => {
	const resolvedParams = await params;
	const products = await fetchFromServer(`/products/offer/${resolvedParams.slug}`);
	return (
		<MaxWidthWrapper>
			<SingleOfferPage products={products} />
		</MaxWidthWrapper>
	);
};

export default Page;
