"use client";

import ProductCard from "@/components/common/product-card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { IProduct } from "@/interfaces";
import { ChevronLeft, ChevronRight } from "lucide-react";
import Image from "next/image";
import { useEffect, useState } from "react";

export default function SingleOfferPage({ products }: any) {
	console.log(products, "products");
	const [currentPage, setCurrentPage] = useState(1);
	const [sortOption, setSortOption] = useState("default");
	const [timeLeft, setTimeLeft] = useState({
		days: 0,
		hours: 9,
		minutes: 1,
		seconds: 3
	});
	const productsPerPage = 6;

	// Countdown timer
	useEffect(() => {
		const timer = setInterval(() => {
			setTimeLeft((prev) => {
				if (prev.seconds > 0) {
					return { ...prev, seconds: prev.seconds - 1 };
				} else if (prev.minutes > 0) {
					return { ...prev, minutes: prev.minutes - 1, seconds: 59 };
				} else if (prev.hours > 0) {
					return { ...prev, hours: prev.hours - 1, minutes: 59, seconds: 59 };
				} else if (prev.days > 0) {
					return { ...prev, days: prev.days - 1, hours: 23, minutes: 59, seconds: 59 };
				}
				return prev;
			});
		}, 1000);

		return () => clearInterval(timer);
	}, []);

	// Pagination controls
	const totalPages = products?.meta?.totalPages;
	const paginate = (pageNumber: number) => setCurrentPage(pageNumber);
	const nextPage = () => setCurrentPage((prev) => Math.min(prev + 1, totalPages));
	const prevPage = () => setCurrentPage((prev) => Math.max(prev - 1, 1));

	return (
		<div className='py-4'>
			{/* Banner */}
			<div className='w-full relative rounded-lg overflow-hidden'>
				<Image
					src='/large-banner.png'
					alt='iPhone 16 Series Banner'
					width={1400}
					height={250}
					className='w-full h-auto object-cover'
					priority
				/>
			</div>

			{/* Header and Sort */}
			<div className='max-w-7xl mx-auto px-2 sm:px-4 py-4 sm:py-8'>
				<div className='flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4 bg-brand-light-100 p-4 rounded-lg'>
					<h1 className='text-xl sm:text-2xl font-medium text-brand-black'>Exclusive Offer on iPhone 16 Series</h1>
					<div className='flex items-center gap-3 self-start sm:self-auto'>
						<span className='text-gray-700 text-sm sm:text-base'>Sort By:</span>
						<Select value={sortOption} onValueChange={setSortOption}>
							<SelectTrigger className='w-[200px] bg-white'>
								<SelectValue placeholder='Select sort option' />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value='default'>Default</SelectItem>
								<SelectItem value='price-low-high'>Price: Low to High</SelectItem>
								<SelectItem value='price-high-low'>Price: High to Low</SelectItem>
								<SelectItem value='name-a-z'>Name: A to Z</SelectItem>
								<SelectItem value='name-z-a'>Name: Z to A</SelectItem>
							</SelectContent>
						</Select>
					</div>
				</div>

				{/* Countdown Timer */}
				<div className='flex justify-center sm:justify-end my-6'>
					<div className='flex flex-wrap items-center justify-center sm:justify-start gap-2 sm:gap-2'>
						<span className='text-brand-dark-700 font-medium text-sm sm:text-base w-full sm:w-auto text-center sm:text-left'>
							OFFER ENDS IN
						</span>
						<div className='flex gap-1'>
							<div className='bg-brand text-brand-black w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded text-xs sm:text-base'>
								{timeLeft.days.toString().padStart(2, "0").split("")[0]}
							</div>
							<div className='bg-brand text-brand-black w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded text-xs sm:text-base'>
								{timeLeft.days.toString().padStart(2, "0").split("")[1]}
							</div>
							<span className='text-gray-500 text-xs sm:text-sm'>Days</span>
						</div>
						<div className='flex gap-1'>
							<div className='bg-brand text-brand-black w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded text-xs sm:text-base'>
								{timeLeft.hours.toString().padStart(2, "0").split("")[0]}
							</div>
							<div className='bg-brand text-brand-black w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded text-xs sm:text-base'>
								{timeLeft.hours.toString().padStart(2, "0").split("")[1]}
							</div>
							<span className='text-gray-500 text-xs sm:text-sm'>Hrs</span>
						</div>
						<div className='flex gap-1'>
							<div className='bg-brand text-brand-black w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded text-xs sm:text-base'>
								{timeLeft.minutes.toString().padStart(2, "0").split("")[0]}
							</div>
							<div className='bg-brand text-brand-black w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded text-xs sm:text-base'>
								{timeLeft.minutes.toString().padStart(2, "0").split("")[1]}
							</div>
							<span className='text-gray-500 text-xs sm:text-sm'>Min</span>
						</div>
						<div className='flex gap-1'>
							<div className='bg-brand text-brand-black w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded text-xs sm:text-base'>
								{timeLeft.seconds.toString().padStart(2, "0").split("")[0]}
							</div>
							<div className='bg-brand text-brand-black w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center rounded text-xs sm:text-base'>
								{timeLeft.seconds.toString().padStart(2, "0").split("")[1]}
							</div>
							<span className='text-gray-500 text-xs sm:text-sm'>Sec</span>
						</div>
					</div>
				</div>

				{/* Product Grid */}
				<div className='grid grid-cols-2 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-6 gap-2 sm:gap-4'>
					{products?.data?.map((product: IProduct) => (
						<ProductCard
							key={product._id}
							_id={product._id}
							name={product.name}
							slug={product.slug}
							images={product.images}
							actualPrice={product.actualPrice}
							price={product.price}
						/>
					))}
				</div>

				{/* Pagination */}
				<div className='flex justify-center mt-8'>
					<div className='flex items-center gap-1 sm:gap-2'>
						<button
							onClick={prevPage}
							disabled={currentPage === 1}
							className={`p-1 sm:p-2 rounded ${
								currentPage === 1 ? "text-gray-400" : "text-gray-700 hover:bg-gray-100"
							}`}
							aria-label='Previous page'
						>
							<ChevronLeft size={16} className='sm:w-5 sm:h-5' />
						</button>

						{Array.from({ length: totalPages }, (_, i) => i + 1).map((number) => {
							// On mobile, only show current page and immediate neighbors
							const isMobileVisible =
								number === 1 ||
								number === totalPages ||
								Math.abs(number - currentPage) <= 1 ||
								(number === 2 && currentPage === 1) ||
								(number === totalPages - 1 && currentPage === totalPages);

							if (!isMobileVisible && window.innerWidth < 640) {
								if ((number === 2 && currentPage > 3) || (number === totalPages - 1 && currentPage < totalPages - 2)) {
									return (
										<span
											key={`ellipsis-${number}`}
											className='text-gray-500 w-6 h-6 sm:w-8 sm:h-8 flex items-center justify-center'
										>
											...
										</span>
									);
								}
								return null;
							}

							return (
								<button
									key={number}
									onClick={() => paginate(number)}
									className={`w-6 h-6 sm:w-8 sm:h-8 text-xs sm:text-sm rounded ${
										currentPage === number ? "bg-brand text-brand-black" : "text-gray-700 hover:bg-gray-100"
									}`}
								>
									{number}
								</button>
							);
						})}

						<button
							onClick={nextPage}
							disabled={currentPage === totalPages}
							className={`p-1 sm:p-2 rounded ${
								currentPage === totalPages ? "text-gray-400" : "text-gray-700 hover:bg-gray-100"
							}`}
							aria-label='Next page'
						>
							<ChevronRight size={16} className='sm:w-5 sm:h-5' />
						</button>
					</div>
				</div>
			</div>
		</div>
	);
}
