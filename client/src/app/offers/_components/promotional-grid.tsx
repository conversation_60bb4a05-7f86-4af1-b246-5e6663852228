"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card";
import {
	Pagination,
	PaginationContent,
	PaginationItem,
	PaginationLink,
	PaginationNext,
	PaginationPrevious
} from "@/components/ui/pagination";
import type { IOffer } from "@/interfaces";
import { cn } from "@/lib/utils";
import { format } from "date-fns";
import { Calendar, Clock, ExternalLink } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useState } from "react";

interface OffersResponse {
	success: boolean;
	message: string;
	meta: {
		page: number;
		limit: number;
		totalItems: number;
		totalPages: number;
	};
	data: IOffer[];
}

export default function PromotionalGrid({ offers }: { offers: OffersResponse }) {
	const [currentPage, setCurrentPage] = useState(1);
	const [isLoading, setIsLoading] = useState(false);
	const [offersData, setOffersData] = useState<OffersResponse | null>(offers);

	// Function to fetch offers for a specific page
	const fetchOffers = async (page: number) => {
		try {
			setIsLoading(true);
			// Replace with your actual API endpoint
			const response = await fetch(`/api/offers?page=${page}&limit=${offers.meta.limit}`);
			const data = await response.json();
			setOffersData(data);
		} catch (error) {
			console.error("Error fetching offers:", error);
		} finally {
			setIsLoading(false);
		}
	};

	// Handle page change
	const handlePageChange = (page: number) => {
		setCurrentPage(page);
		fetchOffers(page);
	};

	// Initialize with the first page data
	useEffect(() => {
		if (offers) {
			setOffersData(offers);
			setCurrentPage(offers?.meta?.page);
		}
	}, [offers]);

	if (!offersData) return <div className='py-20 text-center'>Loading offers...</div>;

	return (
		<div className='space-y-10'>
			<div
				className={cn(
					"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8",
					isLoading && "opacity-60 pointer-events-none"
				)}
			>
				{offersData?.data?.map((offer: IOffer) => (
					<Card
						key={offer._id}
						className='overflow-hidden border-0 shadow-lg hover:shadow-xl transition-all duration-300 h-full flex flex-col bg-white rounded-xl'
					>
						<div className='relative h-56 md:h-64 lg:h-72 overflow-hidden'>
							<Image
								src={offer.profilePicture || "/placeholder.svg?height=300&width=400&query=promotional offer"}
								alt={offer.name}
								fill
								className='object-cover transition-transform hover:scale-105 duration-500'
							/>
							<div className='absolute top-0 left-0 w-full h-full bg-gradient-to-b from-black/40 to-transparent pointer-events-none' />

							<div className='absolute top-4 right-4 bg-white/90 backdrop-blur-sm px-3 py-1.5 rounded-full text-xs font-medium flex items-center gap-1.5 shadow-sm'>
								<Clock className='h-3.5 w-3.5 text-gray-600' />
								<span>Limited Time</span>
							</div>
						</div>

						<CardContent className='flex-1 p-6'>
							<div className='flex items-center gap-1.5 mb-3 text-gray-500 bg-gray-50 w-fit px-3 py-1.5 rounded-full'>
								<Calendar className='h-4 w-4' />
								<span className='text-xs font-medium'>
									{format(new Date(offer.startDate), "MMM dd, yyyy")} -{" "}
									{format(new Date(offer.endDate), "MMM dd, yyyy")}
								</span>
							</div>

							<h3 className='font-bold text-xl mb-3 line-clamp-2 text-gray-800'>{offer.name}</h3>

							<p className='text-sm text-gray-600 line-clamp-3 leading-relaxed'>{offer.description}</p>
						</CardContent>

						<CardFooter className='p-6 pt-0'>
							<Button
								className='w-full bg-gradient-to-r from-brand to-brand hover:from-brand/90 hover:to-brand/70 text-brand-black font-medium rounded-lg py-2.5 shadow-md hover:shadow-lg transition-all duration-200 group'
								asChild
							>
								<Link href={`/offers/${offer.slug}`} className='flex items-center justify-center gap-2'>
									View Details
									<ExternalLink className='h-4 w-4 transition-transform group-hover:translate-x-1' />
								</Link>
							</Button>
						</CardFooter>
					</Card>
				))}
			</div>

			{offersData?.meta?.totalPages > 1 && (
				<div className='flex flex-col items-center gap-4'>
					<Pagination className='mt-8'>
						<PaginationContent>
							<PaginationItem>
								<PaginationPrevious
									href='#'
									onClick={(e) => {
										e.preventDefault();
										if (currentPage > 1) handlePageChange(currentPage - 1);
									}}
									className={cn(currentPage === 1 ? "pointer-events-none opacity-50" : "")}
								/>
							</PaginationItem>

							{Array.from({ length: offersData.meta.totalPages }).map((_, index) => {
								const pageNumber = index + 1;

								// Show first page, last page, current page, and pages around current page
								if (
									pageNumber === 1 ||
									pageNumber === offersData.meta.totalPages ||
									(pageNumber >= currentPage - 1 && pageNumber <= currentPage + 1)
								) {
									return (
										<PaginationItem key={index}>
											<PaginationLink
												href='#'
												onClick={(e) => {
													e.preventDefault();
													handlePageChange(pageNumber);
												}}
												isActive={currentPage === pageNumber}
												className={
													currentPage === pageNumber
														? "bg-indigo-600 text-white hover:bg-indigo-700"
														: "hover:bg-gray-100"
												}
											>
												{pageNumber}
											</PaginationLink>
										</PaginationItem>
									);
								}

								// Show ellipsis for gaps
								if (
									(pageNumber === 2 && currentPage > 3) ||
									(pageNumber === offersData.meta.totalPages - 1 && currentPage < offersData.meta.totalPages - 2)
								) {
									return (
										<PaginationItem key={index}>
											<span className='flex h-10 w-10 items-center justify-center'>...</span>
										</PaginationItem>
									);
								}

								return null;
							})}

							<PaginationItem>
								<PaginationNext
									href='#'
									onClick={(e) => {
										e.preventDefault();
										if (currentPage < offersData.meta.totalPages) handlePageChange(currentPage + 1);
									}}
									className={cn(currentPage === offersData.meta.totalPages ? "pointer-events-none opacity-50" : "")}
								/>
							</PaginationItem>
						</PaginationContent>
					</Pagination>

					<div className='text-center text-sm text-gray-500'>
						Showing {(currentPage - 1) * offersData.meta.limit + 1} to{" "}
						{Math.min(currentPage * offersData.meta.limit, offersData.meta.totalItems)} of {offersData.meta.totalItems}{" "}
						offers
					</div>
				</div>
			)}
		</div>
	);
}
