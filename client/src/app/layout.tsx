import MobileBottomNav from "@/components/Navigation/MobileBottomNav";
import SiteFooter from "@/components/Navigation/SiteFooter";
import TopNavbar from "@/components/Navigation/TopNavbar";
import { Toaster } from "@/components/ui/toaster";
import ReduxProvider from "@/redux/ReduxProvider";
import type { Metadata } from "next";
import { <PERSON><PERSON><PERSON>, <PERSON>eist_Mono } from "next/font/google";
import "./globals.css";

const geistSans = Geist({
	variable: "--font-geist-sans",
	subsets: ["latin"]
});

const geistMono = Geist_Mono({
	variable: "--font-geist-mono",
	subsets: ["latin"]
});

export const metadata: Metadata = {
	title: "Exchanger BD",
	description: "Exchanger BD is a platform for exchanging or buy gadgets."
};

export default function RootLayout({
	children
}: Readonly<{
	children: React.ReactNode;
}>) {
	return (
		<html lang='en' className='overflow-x-hidden'>
			<body className={`${geistSans.variable} ${geistMono.variable} antialiased`}>
				<ReduxProvider>
					<TopNavbar />
					{children}
					<Toaster />
					<SiteFooter />
					<MobileBottomNav />
				</ReduxProvider>
			</body>
		</html>
	);
}
