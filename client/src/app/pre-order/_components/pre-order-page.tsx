"use client";

import type React from "react";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { ImagePlus, Loader2 } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Toaster } from "@/components/ui/toaster";
import { useToast } from "@/hooks/use-toast";
import { useCreatePreOrderMutation } from "@/redux/apis/preOrderApi";

// Define the form schema with validation
const formSchema = z.object({
	productInformation: z.string().min(1, "Product information is required"),
	name: z.string().min(2, "Name must be at least 2 characters"),
	phone: z.string().min(10, "Please enter a valid phone number"),
	email: z.string().email("Please enter a valid email address"),
	address: z.string().min(5, "Address must be at least 5 characters"),
	agreeToTerms: z.literal(true, {
		errorMap: () => ({ message: "You must agree to the terms and conditions" })
	})
});

type FormValues = z.infer<typeof formSchema>;

export default function PreOrderPage() {
	const [selectedImage, setSelectedImage] = useState<File | null>(null);
	const [imagePreview, setImagePreview] = useState<string | null>(null);
	const { toast } = useToast();

	// RTK Query mutation hook
	const [submitPreOrder, { isLoading }] = useCreatePreOrderMutation();

	// Initialize form with React Hook Form and Zod validation
	const form = useForm<FormValues>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			productInformation: "",
			name: "",
			phone: "",
			email: "",
			address: "",
			agreeToTerms: true
		}
	});

	const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		if (e.target.files && e.target.files[0]) {
			const file = e.target.files[0];
			setSelectedImage(file);

			// Create preview URL
			const reader = new FileReader();
			reader.onload = () => {
				setImagePreview(reader.result as string);
			};
			reader.readAsDataURL(file);
		}
	};

	const onSubmit = async (data: FormValues) => {
		try {
			// Create FormData object
			const formDataToSend = new FormData();

			// Append data
			formDataToSend.append("data", JSON.stringify(data));

			// Append image if selected
			if (selectedImage) {
				formDataToSend.append("image", selectedImage);
			}

			// Submit using RTK Query mutation
			await submitPreOrder(formDataToSend).unwrap();

			// Show success toast
			toast({
				title: "Success",
				description: "Pre-order submitted successfully"
			});

			// Reset form after successful submission
			form.reset({
				productInformation: "",
				name: "",
				phone: "",
				email: "",
				address: "",
				agreeToTerms: true
			});
			setSelectedImage(null);
			setImagePreview(null);
		} catch (err: any) {
			// Show error toast
			toast({
				title: "Error",
				description: err.data?.message || "Submission failed!",
				variant: "destructive"
			});
		}
	};

	return (
		<div className='max-w-5xl mx-auto py-2 md:py-12 px-4 sm:px-6'>
			<Toaster />

			{/* Header */}
			<div className='text-center mb-6'>
				<h1 className='text-xl md:text-4xl font-bold bg-gradient-to-r from-brand-black to-brand-dark-800 bg-clip-text text-transparent mb-3'>
					Looking For Something Different?
				</h1>
				<p className='text-brand-black text-md'>Complete the form below to place your pre-order</p>
			</div>

			{/* Pre-order Form */}
			<Card className='border-slate-200 shadow-md border-0'>
				<CardHeader className='bg-slate-50 border-b border-slate-100'>
					<CardTitle className='text-xl text-brand-black'>Pre-Order Form</CardTitle>
					<CardDescription>Fill in your details to pre-order your desired product</CardDescription>
				</CardHeader>
				<CardContent className='pt-6'>
					<Form {...form}>
						<form onSubmit={form.handleSubmit(onSubmit)} className='space-y-3 md:space-y-6'>
							{/* Product Information */}
							<FormField
								control={form.control}
								name='productInformation'
								render={({ field }) => (
									<FormItem>
										<FormLabel className='text-slate-700 font-medium'>Product Information</FormLabel>
										<FormControl>
											<Input
												{...field}
												placeholder='Product Information'
												className='bg-white border-slate-200 focus:border-slate-400'
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							{/* Image Upload */}
							<div className='space-y-2'>
								<Label htmlFor='image' className='text-slate-700 font-medium'>
									Insert Image
								</Label>
								<div className='mt-1'>
									<div className='flex items-center justify-center w-full'>
										<label
											htmlFor='image-upload'
											className='flex flex-col items-center justify-center w-full h-36 border-2 border-slate-200 border-dashed rounded-lg cursor-pointer bg-white hover:bg-slate-50 transition-colors'
										>
											{imagePreview ? (
												<div className='relative w-full h-full p-2'>
													<img
														src={imagePreview || "/placeholder.svg"}
														alt='Preview'
														className='w-full h-full object-contain rounded'
													/>
												</div>
											) : (
												<div className='flex flex-col items-center justify-center pt-5 pb-6'>
													<div className='p-3 rounded-full bg-slate-50 mb-2'>
														<ImagePlus className='w-6 h-6 text-slate-400' />
													</div>
													<p className='text-sm text-slate-500 font-medium'>Click to upload an image</p>
													<p className='text-xs text-slate-400 mt-1'>PNG, JPG, GIF up to 10MB</p>
												</div>
											)}
											<input
												id='image-upload'
												type='file'
												accept='image/*'
												className='hidden'
												onChange={handleImageChange}
											/>
										</label>
									</div>
								</div>
							</div>

							{/* Name */}
							<FormField
								control={form.control}
								name='name'
								render={({ field }) => (
									<FormItem>
										<FormLabel className='text-slate-700 font-medium'>Name</FormLabel>
										<FormControl>
											<Input
												{...field}
												placeholder='Enter your full name'
												className='bg-white border-slate-200 focus:border-slate-400'
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							{/* Phone and Email (side by side on larger screens) */}
							<div className='grid grid-cols-1 md:grid-cols-2 gap-3 md:gap-6'>
								<FormField
									control={form.control}
									name='phone'
									render={({ field }) => (
										<FormItem>
											<FormLabel className='text-slate-700 font-medium'>Phone</FormLabel>
											<FormControl>
												<Input
													{...field}
													placeholder='Enter your phone number'
													className='bg-white border-slate-200 focus:border-slate-400'
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
								<FormField
									control={form.control}
									name='email'
									render={({ field }) => (
										<FormItem>
											<FormLabel className='text-slate-700 font-medium'>Email</FormLabel>
											<FormControl>
												<Input
													{...field}
													type='email'
													placeholder='Enter your email address'
													className='bg-white border-slate-200 focus:border-slate-400'
												/>
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
							</div>

							{/* Address */}
							<FormField
								control={form.control}
								name='address'
								render={({ field }) => (
									<FormItem>
										<FormLabel className='text-slate-700 font-medium'>Address</FormLabel>
										<FormControl>
											<Textarea
												{...field}
												placeholder='Enter your delivery address'
												className='bg-white border-slate-200 focus:border-slate-400 min-h-[120px] resize-none'
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							{/* Terms and Conditions */}
							<FormField
								control={form.control}
								name='agreeToTerms'
								render={({ field }) => (
									<FormItem className='flex flex-row items-start space-x-3 space-y-0 rounded-md p-4 bg-slate-50'>
										<FormControl>
											<Checkbox checked={field.value} onCheckedChange={field.onChange} />
										</FormControl>
										<div className='space-y-1 leading-none'>
											<FormLabel className='text-sm text-brand-black'>
												I hereby accept the terms and conditions of pre-order
											</FormLabel>
											<FormDescription className='text-xs text-slate-500'>
												I have read the pre-order terms and conditions carefully.
											</FormDescription>
										</div>
										<FormMessage />
									</FormItem>
								)}
							/>

							{/* Submit Button */}
							<Button
								type='submit'
								className='w-full bg-brand text-brand-black font-semibold py-2 rounded-md hover:bg-brand/90 transition-colors'
								disabled={isLoading}
							>
								{isLoading ? (
									<>
										<Loader2 className='mr-2 h-4 w-4 animate-spin' />
										Submitting...
									</>
								) : (
									"Submit Pre-Order"
								)}
							</Button>
						</form>
					</Form>
				</CardContent>
			</Card>

			{/* How to Pre-Order Section */}
			<div className='mt-16'>
				<div className='text-center mb-8'>
					<h2 className='text-2xl font-bold text-brand-black mb-2'>How to Pre-Order</h2>
					<p className='text-slate-600'>Watch the video and learn more about the pre-order process</p>
				</div>

				<Card className='overflow-hidden border-slate-200 shadow-md'>
					<div className='aspect-video w-full'>
						<iframe
							className='w-full h-full'
							src='https://www.youtube.com/embed/dQw4w9WgXcQ'
							title='How to Pre-Order at Apple Gadgets'
							frameBorder='0'
							allow='accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture'
							allowFullScreen
						></iframe>
					</div>
				</Card>
			</div>
		</div>
	);
}
