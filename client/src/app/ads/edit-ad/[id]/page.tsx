"use client";

import type React from "react";

import { ArrowLef<PERSON>, Check, Upload, X } from "lucide-react";
import Link from "next/link";
import { useParams, useRouter } from "next/navigation";
import { useEffect, useState } from "react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";

import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import MaxWidthWrapper from "@/components/ui/max-width-wrapper";
import { useGetAdQuery, useUpdateAdMutation } from "@/redux/apis/adsApis";
import { zodResolver } from "@hookform/resolvers/zod";
import Image from "next/image";
import { useForm } from "react-hook-form";
import { z } from "zod";

export default function EditAdPage() {
	const router = useRouter();
	const { toast } = useToast();
	const [images, setImages] = useState<string[]>([]);
	const [isSuccess, setIsSuccess] = useState(false);

	const params = useParams();
	const adId = params.id as string;

	// Redux hooks
	const { data: adResponse, isLoading, error } = useGetAdQuery(adId);
	const [updateAd, { isLoading: isSubmitting }] = useUpdateAdMutation();

	// Form validation schema - matching create-ad structure
	const formSchema = z.object({
		title: z.string().min(5, "Title must be at least 5 characters").max(100, "Title cannot exceed 100 characters"),
		description: z
			.string()
			.min(20, "Description must be at least 20 characters")
			.max(1000, "Description cannot exceed 1000 characters"),
		category: z.string({ required_error: "Please select a category" }),
		condition: z.string({ required_error: "Please select a condition" }),
		price: z.coerce.number().positive("Price must be greater than 0"),
		location: z.string().min(3, "Please enter a valid location"),
		// Optional fields like in create-ad
		phone: z.string().optional(),
		email: z.string().email("Please enter a valid email address").optional()
	});

	type FormValues = z.infer<typeof formSchema>;

	// Form handling
	const form = useForm<FormValues>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			title: "",
			description: "",
			category: "",
			condition: "",
			price: undefined,
			location: "",
			phone: "",
			email: ""
		}
	});

	// Effect to populate form when data is loaded
	useEffect(() => {
		if (adResponse?.data) {
			const ad = adResponse.data;

			// Set images
			setImages(ad.images || []);

			// Reset form with fetched data
			form.reset({
				title: ad.title || "",
				description: ad.description || "",
				category: ad.category || "",
				condition: ad.condition || "",
				price: ad.price || undefined,
				location: ad.location || "",
				// @ts-ignore
				phone: ad.phone || "",
				// @ts-ignore
				email: ad.email || ""
			});
		}
	}, [adResponse, form]);

	// Handle form submission
	const onSubmit = async (data: FormValues) => {
		// Validate images
		if (images.length === 0) {
			toast({
				title: "Image required",
				description: "Please add at least one image of your product",
				variant: "destructive"
			});
			return;
		}

		try {
			// Call the update mutation
			const response = await updateAd({
				id: adId,
				data: {
					title: data.title,
					description: data.description,
					category: data.category,
					condition: data.condition,
					price: data.price,
					location: data.location,
					// @ts-ignore
					phone: data.phone,
					email: data.email,
					user: adResponse?.data?.user,
					images: images
				}
			}).unwrap();

			if (response.success) {
				setIsSuccess(true);
				toast({
					title: "Ad updated successfully!",
					description: "Your changes have been saved and your ad has been updated."
				});
			}
		} catch (error) {
			console.error("Failed to update ad:", error);
			toast({
				title: "Failed to update ad",
				description: "There was an error updating your ad. Please try again.",
				variant: "destructive"
			});
		}
	};

	// Handle image upload
	const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
		if (e.target.files && e.target.files.length > 0) {
			const newImages = [...images];
			for (let i = 0; i < e.target.files.length; i++) {
				if (newImages.length < 8) {
					const file = e.target.files[i];
					const imageUrl = URL.createObjectURL(file);
					newImages.push(imageUrl);
				}
			}
			setImages(newImages);
		}
	};

	const removeImage = (index: number) => {
		const newImages = [...images];
		newImages.splice(index, 1);
		setImages(newImages);
	};

	// Handle error state
	if (error) {
		return (
			<div className='container max-w-3xl mx-auto py-10'>
				<Card>
					<CardContent className='flex flex-col items-center justify-center p-6 text-center'>
						<h1 className='mb-2 text-2xl font-bold text-destructive'>Error Loading Ad</h1>
						<p className='mb-6 text-muted-foreground'>The ad you're trying to edit could not be found or loaded.</p>
						<Link href='/ads/my-ads'>
							<Button variant='outline'>Back to My Ads</Button>
						</Link>
					</CardContent>
				</Card>
			</div>
		);
	}

	// Success state
	if (isSuccess) {
		return (
			<div className='container max-w-3xl mx-auto py-10'>
				<Card>
					<CardContent className='flex flex-col items-center justify-center p-6 text-center'>
						<div className='mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-emerald-100 text-emerald-700 dark:bg-emerald-900 dark:text-emerald-300'>
							<Check className='h-6 w-6' />
						</div>
						<h1 className='mb-2 text-2xl font-bold'>Ad Updated Successfully!</h1>
						<p className='mb-6 text-muted-foreground'>Your changes have been saved and your ad has been updated.</p>
						<div className='flex gap-4'>
							<Link href='/ads/my-ads'>
								<Button variant='outline'>View My Ads</Button>
							</Link>
							<Link href={`/ads/my-ads`}>
								<Button className='bg-emerald-600 hover:bg-emerald-700'>View Updated Ad</Button>
							</Link>
						</div>
					</CardContent>
				</Card>
			</div>
		);
	}

	// Loading state
	if (isLoading) {
		return (
			<div className='flex min-h-screen flex-col'>
				<header className='w-full border-b'>
					<MaxWidthWrapper className='container flex h-16 items-center justify-between py-4'>
						<div className='flex items-center gap-2'>
							<Link href='/ads/my-ads'>
								<Button variant='ghost' size='icon' className='mr-2'>
									<ArrowLeft className='h-5 w-5' />
									<span className='sr-only'>Back</span>
								</Button>
							</Link>
							<h1 className='text-lg font-semibold'>Edit Ad</h1>
						</div>
					</MaxWidthWrapper>
				</header>
				<main className='flex-1 container py-6 md:py-10'>
					<div className='mx-auto max-w-3xl'>
						<div className='flex flex-col items-center justify-center space-y-4 py-20'>
							<div className='h-8 w-8 animate-spin rounded-full border-4 border-emerald-600 border-t-transparent'></div>
							<p className='text-muted-foreground'>Loading ad data...</p>
						</div>
					</div>
				</main>
			</div>
		);
	}

	return (
		<div className='flex min-h-screen flex-col'>
			<header className='w-full border-b'>
				<MaxWidthWrapper className='container flex h-16 items-center justify-between py-4'>
					<div className='flex items-center gap-2'>
						<Link href='/ads/my-ads'>
							<Button variant='ghost' size='icon' className='mr-2'>
								<ArrowLeft className='h-5 w-5' />
								<span className='sr-only'>Back</span>
							</Button>
						</Link>
						<h1 className='text-lg font-semibold'>Edit Ad</h1>
					</div>
				</MaxWidthWrapper>
			</header>
			<main className='flex-1 container py-6 md:py-10'>
				<div className='mx-auto max-w-3xl'>
					<Form {...form}>
						<form onSubmit={form.handleSubmit(onSubmit)} className='space-y-8'>
							<div className='space-y-4'>
								<h2 className='text-xl font-semibold'>Ad Details</h2>
								<div className='grid gap-4'>
									<FormField
										control={form.control}
										name='title'
										render={({ field }) => (
											<FormItem>
												<FormLabel>Title</FormLabel>
												<FormControl>
													<Input {...field} placeholder='e.g., iPhone 13 Pro - Excellent Condition' />
												</FormControl>
												<FormDescription>Be specific and include key details about your item</FormDescription>
												<FormMessage />
											</FormItem>
										)}
									/>

									<FormField
										control={form.control}
										name='description'
										render={({ field }) => (
											<FormItem>
												<FormLabel>Description</FormLabel>
												<FormControl>
													<Textarea
														{...field}
														placeholder='Describe your item in detail. Include condition, features, and reason for selling.'
														className='min-h-[120px]'
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>

									<div className='grid gap-6 sm:grid-cols-2'>
										<FormField
											control={form.control}
											name='category'
											render={({ field }) => (
												<FormItem>
													<FormLabel>Category</FormLabel>
													<Select onValueChange={field.onChange} value={field.value}>
														<FormControl>
															<SelectTrigger>
																<SelectValue placeholder='Select category' />
															</SelectTrigger>
														</FormControl>
														<SelectContent>
															<SelectItem value='smartphones'>Smartphones</SelectItem>
															<SelectItem value='feature-phones'>Feature Phones</SelectItem>
															<SelectItem value='tablets'>Tablets</SelectItem>
															<SelectItem value='smartwatches'>Smartwatches</SelectItem>
															<SelectItem value='earbuds'>Earbuds & Headphones</SelectItem>
															<SelectItem value='chargers'>Chargers & Power Banks</SelectItem>
															<SelectItem value='phone-cases'>Phone Cases & Covers</SelectItem>
															<SelectItem value='screen-protectors'>Screen Protectors</SelectItem>
															<SelectItem value='mobile-gaming'>Mobile Gaming Accessories</SelectItem>
															<SelectItem value='bluetooth-speakers'>Bluetooth Speakers</SelectItem>
															<SelectItem value='wearables'>Wearables</SelectItem>
															<SelectItem value='mobile-accessories'>Mobile Accessories</SelectItem>
															<SelectItem value='drones'>Drones</SelectItem>
															<SelectItem value='camera-gear'>Camera Gear</SelectItem>
															<SelectItem value='gps-devices'>GPS Devices</SelectItem>
															<SelectItem value='vr-headsets'>VR Headsets</SelectItem>
															<SelectItem value='smart-home-devices'>Smart Home Devices</SelectItem>
															<SelectItem value='sim-cards'>SIM Cards & Plans</SelectItem>
															<SelectItem value='repair-tools'>Phone Repair Tools</SelectItem>
															<SelectItem value='refurbished-phones'>Refurbished Phones</SelectItem>
														</SelectContent>
													</Select>
													<FormMessage />
												</FormItem>
											)}
										/>

										<FormField
											control={form.control}
											name='condition'
											render={({ field }) => (
												<FormItem>
													<FormLabel>Condition</FormLabel>
													<Select onValueChange={field.onChange} value={field.value}>
														<FormControl>
															<SelectTrigger>
																<SelectValue placeholder='Select condition' />
															</SelectTrigger>
														</FormControl>
														<SelectContent>
															<SelectItem value='new'>New</SelectItem>
															<SelectItem value='like-new'>Like New</SelectItem>
															<SelectItem value='excellent'>Excellent</SelectItem>
															<SelectItem value='good'>Good</SelectItem>
															<SelectItem value='fair'>Fair</SelectItem>
															<SelectItem value='for-parts'>For Parts</SelectItem>
														</SelectContent>
													</Select>
													<FormMessage />
												</FormItem>
											)}
										/>
									</div>

									<div className='grid gap-6 sm:grid-cols-2'>
										<FormField
											control={form.control}
											name='price'
											render={({ field }) => (
												<FormItem>
													<FormLabel>Price ($)</FormLabel>
													<FormControl>
														<Input
															type='number'
															placeholder='0.00'
															min='0'
															step='0.01'
															{...field}
															value={field.value || ""}
															onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : "")}
														/>
													</FormControl>
													<FormMessage />
												</FormItem>
											)}
										/>

										<FormField
											control={form.control}
											name='location'
											render={({ field }) => (
												<FormItem>
													<FormLabel>Location</FormLabel>
													<FormControl>
														<Input placeholder='City, State' {...field} />
													</FormControl>
													<FormMessage />
												</FormItem>
											)}
										/>
									</div>
								</div>
							</div>

							<Separator />

							<div className='space-y-4'>
								<div className='flex items-center justify-between'>
									<h2 className='text-xl font-semibold'>Images</h2>
									{images.length === 0 && <p className='text-sm text-destructive'>At least one image is required</p>}
								</div>
								<p className='text-sm text-muted-foreground'>
									Add up to 8 images. First image will be the cover (main) image.
								</p>

								<div className='grid grid-cols-2 gap-4 sm:grid-cols-4'>
									{images.map((image, index) => (
										<div key={index} className='relative aspect-square rounded-md border bg-muted'>
											<Image
												src={image || "/placeholder.svg"}
												alt={`Product image ${index + 1}`}
												width={500}
												height={500}
												className='h-full w-full rounded-md object-cover'
											/>
											<Button
												type='button'
												variant='destructive'
												size='icon'
												className='absolute right-1 top-1 h-6 w-6'
												onClick={() => removeImage(index)}
											>
												<X className='h-3 w-3' />
												<span className='sr-only'>Remove image</span>
											</Button>
											{index === 0 && (
												<span className='absolute left-1 top-1 rounded-md bg-background/80 px-1.5 py-0.5 text-xs font-medium'>
													Cover
												</span>
											)}
										</div>
									))}

									{images.length < 8 && (
										<div className='flex aspect-square items-center justify-center rounded-md border border-dashed'>
											<Label
												htmlFor='image-upload'
												className='flex cursor-pointer flex-col items-center justify-center gap-1 text-sm text-muted-foreground'
											>
												<Upload className='h-4 w-4' />
												<span>Upload</span>
												<Input
													id='image-upload'
													type='file'
													accept='image/*'
													multiple
													className='hidden'
													onChange={handleImageUpload}
												/>
											</Label>
										</div>
									)}
								</div>
							</div>

							<Separator />

							<div className='space-y-4'>
								<h2 className='text-xl font-semibold'>Contact Information (Optional)</h2>
								<div className='grid gap-4 sm:grid-cols-2'>
									<FormField
										control={form.control}
										name='phone'
										render={({ field }) => (
											<FormItem>
												<FormLabel>Phone Number</FormLabel>
												<FormControl>
													<Input type='tel' placeholder='(*************' {...field} />
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>

									<FormField
										control={form.control}
										name='email'
										render={({ field }) => (
											<FormItem>
												<FormLabel>Email</FormLabel>
												<FormControl>
													<Input type='email' placeholder='<EMAIL>' {...field} />
												</FormControl>
												<FormDescription>Your email won't be shown publicly</FormDescription>
												<FormMessage />
											</FormItem>
										)}
									/>
								</div>
							</div>

							<div className='flex justify-end gap-4'>
								<Link href='/ads/my-ads'>
									<Button variant='outline' type='button'>
										Cancel
									</Button>
								</Link>
								<Button type='submit' className='bg-brand text-brand-black hover:bg-brand/80' disabled={isSubmitting}>
									{isSubmitting ? "Saving..." : "Save Changes"}
								</Button>
							</div>
						</form>
					</Form>
				</div>
			</main>
		</div>
	);
}
