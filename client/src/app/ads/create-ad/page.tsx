"use client";

import type React from "react";

import { <PERSON><PERSON><PERSON><PERSON>, Check, Upload, X } from "lucide-react";
import Link from "next/link";
import { useState } from "react";

import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";

import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import MaxWidthWrapper from "@/components/ui/max-width-wrapper";
import { useCreateAdMutation } from "@/redux/apis/adsApis";
import { zodResolver } from "@hookform/resolvers/zod";
import Image from "next/image";
import { useForm } from "react-hook-form";
import { z } from "zod";

export default function CreateAdPage() {
	const { toast } = useToast();
	const [images, setImages] = useState<string[]>([]);
	const [isSuccess, setIsSuccess] = useState(false);

	// Redux mutation hook
	const [createAd, { isLoading: isSubmitting }] = useCreateAdMutation();

	// Form validation schema
	const formSchema = z.object({
		title: z.string().min(5, "Title must be at least 5 characters").max(100, "Title cannot exceed 100 characters"),
		description: z
			.string()
			.min(20, "Description must be at least 20 characters")
			.max(1000, "Description cannot exceed 1000 characters"),
		category: z.string({ required_error: "Please select a category" }),
		condition: z.string({ required_error: "Please select a condition" }),
		price: z.coerce.number().positive("Price must be greater than 0"),
		location: z.string().min(3, "Please enter a valid location"),
		// Make these optional since they're not in the UI
		phone: z
			.string()
			.regex(/^$$\d{3}$$ \d{3}-\d{4}$|^\d{10}$/, "Please enter a valid phone number")
			.optional(),
		email: z.string().email("Please enter a valid email address").optional()
	});

	type FormValues = z.infer<typeof formSchema>;

	// Form handling
	const form = useForm<FormValues>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			title: "",
			description: "",
			category: "",
			condition: "",
			price: undefined,
			location: ""
			// Remove these fields from defaultValues
			// phone: "",
			// email: ""
		}
	});

	const onSubmit = async (data: FormValues) => {
		// Validate images
		if (images.length === 0) {
			toast({
				title: "Image required",
				description: "Please add at least one image of your product",
				variant: "destructive"
			});
			return;
		}

		try {
			// Call the mutation with form data
			const response = await createAd({
				title: data.title,
				description: data.description,
				category: data.category,
				condition: data.condition,
				price: data.price,
				location: data.location,
				images: images
			}).unwrap();

			if (response.success) {
				setIsSuccess(true);
				toast({
					title: "Ad created successfully!",
					description: "Your ad has been posted and is now visible to everyone."
				});
			}
		} catch (error) {
			console.error("Failed to create ad:", error);
			toast({
				title: "Failed to create ad",
				description: "There was an error creating your ad. Please try again.",
				variant: "destructive"
			});
		}
	};

	// Mock function to handle image upload
	const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
		// In a real app, you would upload the file to a server
		// Here we're just creating a placeholder URL
		if (e.target.files && e.target.files.length > 0) {
			const newImages = [...images];
			for (let i = 0; i < e.target.files.length; i++) {
				if (newImages.length < 8) {
					const file = e.target.files[i];
					const imageUrl = URL.createObjectURL(file);
					newImages.push(imageUrl);
				}
			}
			setImages(newImages);
		}
	};

	const removeImage = (index: number) => {
		const newImages = [...images];
		newImages.splice(index, 1);
		setImages(newImages);
	};

	if (isSuccess) {
		return (
			<div className='container max-w-3xl mx-auto py-10'>
				<Card>
					<CardContent className='flex flex-col items-center justify-center p-6 text-center'>
						<div className='mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-emerald-100 text-emerald-700 dark:bg-emerald-900 dark:text-emerald-300'>
							<Check className='h-6 w-6' />
						</div>
						<h1 className='mb-2 text-2xl font-bold'>Ad Posted Successfully!</h1>
						<p className='mb-6 text-muted-foreground'>
							Your ad has been created and is now visible to potential buyers.
						</p>
						<div className='flex gap-4'>
							<Link href='/ads/my-ads'>
								<Button variant='outline'>View My Ads</Button>
							</Link>
							<Link href='/'>
								<Button className='bg-emerald-600 hover:bg-emerald-700'>Back to Home</Button>
							</Link>
						</div>
					</CardContent>
				</Card>
			</div>
		);
	}

	return (
		<div className='flex min-h-screen flex-col'>
			<header className='w-full border-b '>
				<MaxWidthWrapper className='container flex h-16 items-center justify-between py-4'>
					<div className='flex items-center gap-2'>
						<Link href='/ads'>
							<Button variant='ghost' size='icon' className='mr-2'>
								<ArrowLeft className='h-5 w-5' />
								<span className='sr-only'>Back</span>
							</Button>
						</Link>
						<h1 className='text-lg font-semibold'>Create New Ad</h1>
					</div>
				</MaxWidthWrapper>
			</header>
			<main className='flex-1 container py-6 md:py-10'>
				<div className='mx-auto max-w-3xl'>
					<Form {...form}>
						<form onSubmit={form.handleSubmit(onSubmit)} className='space-y-8'>
							{/* <div className='text-red-500 text-sm'>
								{Object.keys(form.formState.errors).length > 0 && (
									<div>Form has errors: {JSON.stringify(form.formState.errors)}</div>
								)}
							</div> */}
							<div className='space-y-4'>
								<h2 className='text-xl font-semibold'>Ad Details</h2>
								<div className='grid gap-4'>
									<FormField
										control={form.control}
										name='title'
										render={({ field }) => (
											<FormItem>
												<FormLabel>Title</FormLabel>
												<FormControl>
													<Input {...field} placeholder='e.g., iPhone 13 Pro - Excellent Condition' />
												</FormControl>
												<FormDescription>Be specific and include key details about your item</FormDescription>
												<FormMessage />
											</FormItem>
										)}
									/>

									<FormField
										control={form.control}
										name='description'
										render={({ field }) => (
											<FormItem>
												<FormLabel>Description</FormLabel>
												<FormControl>
													<Textarea
														{...field}
														placeholder='Describe your item in detail. Include condition, features, and reason for selling.'
														className='min-h-[120px]'
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										)}
									/>

									<div className='grid gap-6 sm:grid-cols-2'>
										<FormField
											control={form.control}
											name='category'
											render={({ field }) => (
												<FormItem>
													<FormLabel>Category</FormLabel>
													<Select onValueChange={field.onChange} defaultValue={field.value}>
														<FormControl>
															<SelectTrigger>
																<SelectValue placeholder='Select category' />
															</SelectTrigger>
														</FormControl>
														<SelectContent>
															<SelectItem value='smartphones'>Smartphones</SelectItem>
															<SelectItem value='feature-phones'>Feature Phones</SelectItem>
															<SelectItem value='tablets'>Tablets</SelectItem>
															<SelectItem value='smartwatches'>Smartwatches</SelectItem>
															<SelectItem value='earbuds'>Earbuds & Headphones</SelectItem>
															<SelectItem value='chargers'>Chargers & Power Banks</SelectItem>
															<SelectItem value='phone-cases'>Phone Cases & Covers</SelectItem>
															<SelectItem value='screen-protectors'>Screen Protectors</SelectItem>
															<SelectItem value='mobile-gaming'>Mobile Gaming Accessories</SelectItem>
															<SelectItem value='bluetooth-speakers'>Bluetooth Speakers</SelectItem>
															<SelectItem value='wearables'>Wearables</SelectItem>
															<SelectItem value='mobile-accessories'>Mobile Accessories</SelectItem>
															<SelectItem value='drones'>Drones</SelectItem>
															<SelectItem value='camera-gear'>Camera Gear</SelectItem>
															<SelectItem value='gps-devices'>GPS Devices</SelectItem>
															<SelectItem value='vr-headsets'>VR Headsets</SelectItem>
															<SelectItem value='smart-home-devices'>Smart Home Devices</SelectItem>
															<SelectItem value='sim-cards'>SIM Cards & Plans</SelectItem>
															<SelectItem value='repair-tools'>Phone Repair Tools</SelectItem>
															<SelectItem value='refurbished-phones'>Refurbished Phones</SelectItem>
														</SelectContent>
													</Select>
													<FormMessage />
												</FormItem>
											)}
										/>

										<FormField
											control={form.control}
											name='condition'
											render={({ field }) => (
												<FormItem>
													<FormLabel>Condition</FormLabel>
													<Select onValueChange={field.onChange} defaultValue={field.value}>
														<FormControl>
															<SelectTrigger>
																<SelectValue placeholder='Select condition' />
															</SelectTrigger>
														</FormControl>
														<SelectContent>
															<SelectItem value='new'>New</SelectItem>
															<SelectItem value='like-new'>Like New</SelectItem>
															<SelectItem value='excellent'>Excellent</SelectItem>
															<SelectItem value='good'>Good</SelectItem>
															<SelectItem value='fair'>Fair</SelectItem>
															<SelectItem value='for-parts'>For Parts</SelectItem>
														</SelectContent>
													</Select>
													<FormMessage />
												</FormItem>
											)}
										/>
									</div>

									<div className='grid gap-6 sm:grid-cols-2'>
										<FormField
											control={form.control}
											name='price'
											render={({ field }) => (
												<FormItem>
													<FormLabel>Price ($)</FormLabel>
													<FormControl>
														<Input
															type='number'
															placeholder='0.00'
															min='0'
															step='0.01'
															{...field}
															value={field.value || ""}
															onChange={(e) => field.onChange(e.target.value ? Number(e.target.value) : "")}
														/>
													</FormControl>
													<FormMessage />
												</FormItem>
											)}
										/>

										<FormField
											control={form.control}
											name='location'
											render={({ field }) => (
												<FormItem>
													<FormLabel>Location</FormLabel>
													<FormControl>
														<Input placeholder='City, State' {...field} />
													</FormControl>
													<FormMessage />
												</FormItem>
											)}
										/>
									</div>
								</div>
							</div>

							<Separator />

							<div className='space-y-4'>
								<div className='flex items-center justify-between'>
									<h2 className='text-xl font-semibold'>Images</h2>
									{images.length === 0 && <p className='text-sm text-destructive'>At least one image is required</p>}
								</div>
								<p className='text-sm text-muted-foreground'>
									Add up to 8 images. First image will be the cover (main) image.
								</p>

								<div className='grid grid-cols-2 gap-4 sm:grid-cols-4'>
									{images.map((image, index) => (
										<div key={index} className='relative aspect-square rounded-md border bg-muted'>
											<Image
												src={image || "/placeholder.svg"}
												alt={`Product image ${index + 1}`}
												width={500}
												height={500}
												className='h-full w-full rounded-md object-cover'
											/>
											<Button
												type='button'
												variant='destructive'
												size='icon'
												className='absolute right-1 top-1 h-6 w-6'
												onClick={() => removeImage(index)}
											>
												<X className='h-3 w-3' />
												<span className='sr-only'>Remove image</span>
											</Button>
											{index === 0 && (
												<span className='absolute left-1 top-1 rounded-md bg-background/80 px-1.5 py-0.5 text-xs font-medium'>
													Cover
												</span>
											)}
										</div>
									))}

									{images.length < 8 && (
										<div className='flex aspect-square items-center justify-center rounded-md border border-dashed'>
											<Label
												htmlFor='image-upload'
												className='flex cursor-pointer flex-col items-center justify-center gap-1 text-sm text-muted-foreground'
											>
												<Upload className='h-4 w-4' />
												<span>Upload</span>
												<Input
													id='image-upload'
													type='file'
													accept='image/*'
													multiple
													className='hidden'
													onChange={handleImageUpload}
												/>
											</Label>
										</div>
									)}
								</div>
							</div>

							<div className='flex justify-end gap-4'>
								<Link href='/'>
									<Button variant='outline' type='button'>
										Cancel
									</Button>
								</Link>
								<Button type='submit' className='bg-brand text-brand-black hover:bg-brand/80' disabled={isSubmitting}>
									{isSubmitting ? "Posting..." : "Post Ad"}
								</Button>
							</div>
						</form>
					</Form>
				</div>
			</main>
		</div>
	);
}
