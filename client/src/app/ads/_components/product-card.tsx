import Link from "next/link";

import { Badge } from "@/components/ui/badge";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { IAd } from "@/redux/apis/adsApis";
import Image from "next/image";
interface ProductCardProps {
	product: IAd & { image: string; daysAgo?: number; postedDate: string };
}

export function ProductCard({ product }: ProductCardProps) {
	return (
		<Link href={`/ads/ad/${product._id}`} className='group'>
			<Card className='overflow-hidden transition-all hover:shadow-md'>
				<CardHeader className='p-0'>
					<div className='aspect-video w-full overflow-hidden bg-muted'>
						<Image
							src={product.image || "/placeholder.svg"}
							alt={product.title}
							className='h-full w-full object-cover transition-transform group-hover:scale-105'
							width={400}
							height={225}
						/>
					</div>
				</CardHeader>
				<CardContent className='p-4'>
					<Badge className='mb-2 bg-brand-light-100 text-brand-black hover:bg-brand-light-200 '>
						{product.category}
					</Badge>
					<CardTitle className='line-clamp-1 text-lg'>{product.title}</CardTitle>
					<p className='mt-2 text-xl font-bold text-brand-dark-300'>${product.price}</p>
				</CardContent>
				<CardFooter className='flex items-center justify-between border-t p-4 pt-3'>
					<div className='text-sm text-muted-foreground'>{product.location}</div>
					<div className='text-sm text-muted-foreground'>{product.postedDate}</div>
				</CardFooter>
			</Card>
		</Link>
	);
}
