"use client";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { ChevronLeft, ChevronRight } from "lucide-react";
import Image from "next/image";
import { useEffect, useState } from "react";

export function FeaturedAdCarousel() {
	const [current, setCurrent] = useState(0);

	const slides = [
		{
			image: "/placeholder.svg?height=400&width=600&query=gaming+laptop",
			title: "Luxury Car - Low Mileage",
			price: "$24,999",
			category: "Vehicles"
		},
		{
			image: "/placeholder.svg?height=400&width=600&query=gaming+laptop",
			title: "Modern Apartment for Rent",
			price: "$1,800/mo",
			category: "Real Estate"
		},
		{
			image: "/placeholder.svg?height=400&width=600&query=gaming+laptop",
			title: "Gaming Laptop - High Performance",
			price: "$1,299",
			category: "Electronics"
		}
	];

	useEffect(() => {
		const interval = setInterval(() => {
			setCurrent((current) => (current === slides.length - 1 ? 0 : current + 1));
		}, 5000);
		return () => clearInterval(interval);
	}, [slides.length]);

	const prev = () => {
		setCurrent((current) => (current === 0 ? slides.length - 1 : current - 1));
	};

	const next = () => {
		setCurrent((current) => (current === slides.length - 1 ? 0 : current + 1));
	};

	return (
		<div className='relative overflow-hidden rounded-xl'>
			<div
				className='flex transition-transform duration-500 ease-out'
				style={{ transform: `translateX(-${current * 100}%)` }}
			>
				{slides.map((slide, index) => (
					<div key={index} className='relative min-w-full'>
						<Image
							src={slide.image || "/placeholder.svg"}
							alt={slide.title}
							className='aspect-[4/3] w-full object-cover'
							width={600}
							height={400}
						/>
						<div className='absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/80 to-transparent p-4 text-white'>
							<Badge className='mb-2 bg-brand text-brand-black hover:bg-brand/90'>{slide.category}</Badge>
							<h3 className='text-lg font-bold'>{slide.title}</h3>
							<p className='text-xl font-bold'>{slide.price}</p>
						</div>
					</div>
				))}
			</div>
			<Button
				variant='ghost'
				size='icon'
				className='absolute left-2 top-1/2 h-8 w-8 -translate-y-1/2 rounded-full bg-background/80 text-foreground'
				onClick={prev}
			>
				<ChevronLeft className='h-4 w-4' />
				<span className='sr-only'>Previous slide</span>
			</Button>
			<Button
				variant='ghost'
				size='icon'
				className='absolute right-2 top-1/2 h-8 w-8 -translate-y-1/2 rounded-full bg-background/80 text-foreground'
				onClick={next}
			>
				<ChevronRight className='h-4 w-4' />
				<span className='sr-only'>Next slide</span>
			</Button>
			<div className='absolute bottom-2 left-1/2 flex -translate-x-1/2 gap-1'>
				{slides.map((_, index) => (
					<Button
						key={index}
						variant='ghost'
						size='icon'
						className={`h-2 w-2 rounded-full p-0 ${current === index ? "bg-white" : "bg-white/50"}`}
						onClick={() => setCurrent(index)}
					>
						<span className='sr-only'>Go to slide {index + 1}</span>
					</Button>
				))}
			</div>
		</div>
	);
}
