import MaxWidthWrapper from "@/components/ui/max-width-wrapper";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { useGetFeaturedAdsQuery, useGetRecentAdsQuery } from "@/redux/apis/adsApis";
import { Clock, TrendingUp } from "lucide-react";
import { ProductCard } from "./product-card";

export function FeaturedAds() {
	const { data: featuredAds } = useGetFeaturedAdsQuery();
	const { data: recentAds } = useGetRecentAdsQuery();

	// Format date helper function
	const formatDate = (dateString?: Date) => {
		if (!dateString) return "Recently";

		const date = new Date(dateString);

		// Check if date is valid
		if (isNaN(date.getTime())) return "Recently";

		return date.toLocaleDateString();
	};

	return (
		<MaxWidthWrapper className='px-4 py-8 md:px-6 md:py-12'>
			<Tabs defaultValue='trending' className='w-full'>
				<div className='flex items-center justify-between'>
					<h2 className='text-2xl font-bold tracking-tight'>Ads</h2>
					<TabsList>
						<TabsTrigger value='trending' className='gap-1'>
							<TrendingUp className='h-4 w-4' />
							Featured
						</TabsTrigger>
						<TabsTrigger value='recent' className='gap-1'>
							<Clock className='h-4 w-4' />
							Recent
						</TabsTrigger>
					</TabsList>
				</div>
				<TabsContent value='trending' className='mt-6'>
					<div className='grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'>
						{featuredAds?.data?.map((product) => (
							<ProductCard
								key={product._id}
								product={{
									...product,
									image: product.images[0] || "/placeholder.svg",
									postedDate: formatDate(product.createdAt)
								}}
							/>
						))}
					</div>
				</TabsContent>
				<TabsContent value='recent' className='mt-6'>
					<div className='grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'>
						{recentAds?.data?.map((product) => (
							<ProductCard
								key={product._id}
								product={{
									...product,
									image: product.images[0] || "/placeholder.svg",
									postedDate: formatDate(product.createdAt)
								}}
							/>
						))}
					</div>
				</TabsContent>
			</Tabs>
		</MaxWidthWrapper>
	);
}
