import { ProductCard } from "./product-card";
import { useGetAllAdsQuery } from "@/redux/apis/adsApis";
interface SimilarAdsProps {
	category: string;
	currentAdId: string;
}

export function SimilarAds({ category, currentAdId }: SimilarAdsProps) {
	const { data: ads } = useGetAllAdsQuery();

	// Filter out the current ad and get ads in the same category
	const similarAds =
		ads?.data?.filter((ad) => ad._id !== currentAdId && ad.category.toLowerCase() === category.toLowerCase()) || [];

	// If there are not enough similar ads in the same category, get other ads
	const otherAds =
		similarAds.length < 4
			? ads?.data?.filter((ad) => ad._id !== currentAdId && ad.category.toLowerCase() !== category.toLowerCase()) || []
			: [];

	// Combine similar and other ads, taking only what we need to make 4 total
	const displayAds = [...similarAds, ...otherAds].slice(0, 4);

	// Format date helper function
	const formatDate = (dateString?: Date) => {
		if (!dateString) return "Recently";

		const date = new Date(dateString);

		// Check if date is valid
		if (isNaN(date.getTime())) return "Recently";

		return date.toLocaleDateString();
	};

	return (
		<div className='grid gap-6 sm:grid-cols-2 lg:grid-cols-4'>
			{displayAds.length > 0 ? (
				displayAds.map((ad) => (
					<ProductCard
						key={ad._id}
						product={{
							...ad,
							image: ad.images[0] || "/placeholder.svg",
							postedDate: formatDate(ad.createdAt)
						}}
					/>
				))
			) : (
				<div className='col-span-full text-center text-muted-foreground'>No similar ads available at the moment.</div>
			)}
		</div>
	);
}
