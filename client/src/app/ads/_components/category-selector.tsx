import {
	Battery,
	BatteryCharging,
	Cable,
	AlbumIcon as CoverIcon,
	Headphones,
	Laptop,
	Pencil,
	Plug,
	Smartphone,
	Speaker,
	Usb,
	Watch,
	WatchIcon as WatchCircle
} from "lucide-react";
import Link from "next/link";
import React from "react";

type Category = {
	name: string;
	icon: React.ElementType;
	slug: string;
};

export function CategorySelector() {
	const categories: Category[] = [
		{ name: "Phones & Tablets", icon: Smartphone, slug: "phones-and-tablets" },
		{ name: "MacBook", icon: Laptop, slug: "macbook" },
		{ name: "Hubs & Docks", icon: Usb, slug: "hubs-docks" },
		{ name: "<PERSON>yl<PERSON>", icon: Pencil, slug: "stylus" },
		{ name: "Smart Watch", icon: Watch, slug: "smart-watch" },
		{ name: "Watch Strap", icon: WatchCircle, slug: "watch-strap" },
		{ name: "Airpods", icon: Headphones, slug: "airpods" },
		{ name: "Wired Headphone", icon: Cable, slug: "wired-headphone" },
		{ name: "Wireless Headphone", icon: Headphones, slug: "wireless-headphone" },
		{ name: "Power Adapter", icon: Plug, slug: "power-adapter" },
		{ name: "Power Bank", icon: Battery, slug: "power-bank" },
		{ name: "Cable & Interconnects", icon: Cable, slug: "cable-interconnects" },
		{ name: "Wireless Charger", icon: BatteryCharging, slug: "wireless-charger" },
		{ name: "Speakers", icon: Speaker, slug: "speakers" },
		{ name: "Overhead Headphones", icon: Headphones, slug: "overhead-headphones" },
		{ name: "Cover & Glass", icon: CoverIcon, slug: "cover-glass" }
	];

	return (
		<div className='w-full'>
			<div className='grid grid-cols-4 sm:grid-cols-4 lg:grid-cols-8 gap-5'>
				{categories.map((category, index) => (
					<Link href={`/ads/category/${category.slug}`} key={index} className='block group'>
						<div className='relative bg-white border border-gray-100 rounded-lg shadow-sm transition-all duration-300 hover:shadow-md overflow-hidden h-[140px]'>
							{/* Top accent line */}
							<div className='h-1 w-full bg-brand opacity-0 group-hover:opacity-100 transition-opacity duration-300'></div>

							<div className='flex flex-col items-center justify-center h-full p-4'>
								<div className='relative mb-3'>
									{/* Background circle */}
									<div className='absolute inset-0 bg-gray-100 rounded-full transform scale-0 group-hover:scale-100 transition-transform duration-300 -z-10'></div>

									{/* Icon */}
									{React.createElement(category.icon, {
										size: 36,
										className: "text-gray-800 group-hover:text-brand transition-colors duration-300",
										strokeWidth: 1.25
									})}
								</div>

								<span className='text-center text-sm font-medium text-gray-800 mt-2'>{category.name}</span>
							</div>
						</div>
					</Link>
				))}
			</div>
		</div>
	);
}
