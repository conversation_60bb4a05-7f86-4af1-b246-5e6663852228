"use client";

import { <PERSON>Left, Calendar, MapPin, Phone } from "lucide-react";
import Link from "next/link";
import { useParams } from "next/navigation";
import { useState } from "react";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import MaxWidthWrapper from "@/components/ui/max-width-wrapper";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { useGetAdQuery } from "@/redux/apis/adsApis";
import Image from "next/image";
import { SimilarAds } from "../../_components/similar-ads";

export default function AdPage() {
	const [showContact, setShowContact] = useState(false);
	const params = useParams();
	const adId = params.id as string;

	const { data: adData, isLoading, error } = useGetAdQuery(adId);
	console.log({ adData });

	if (isLoading) {
		return (
			<div className='flex min-h-screen flex-col'>
				<header className='w-full border-b'>
					<MaxWidthWrapper className='flex h-16 items-center justify-between py-4'>
						<div className='flex items-center gap-2'>
							<Link href='/ads'>
								<Button variant='ghost' size='icon' className='mr-2'>
									<ArrowLeft className='h-5 w-5' />
									<span className='sr-only'>Back</span>
								</Button>
							</Link>
							<Skeleton className='h-6 w-[200px]' />
						</div>
					</MaxWidthWrapper>
				</header>
				<MaxWidthWrapper className='flex-1 py-6 md:py-10'>
					<div className='grid gap-6 lg:grid-cols-3 lg:gap-10'>
						<div className='lg:col-span-2'>
							<Skeleton className='h-[400px] w-full rounded-lg' />
							<div className='mt-6'>
								<Skeleton className='h-10 w-3/4' />
								<div className='mt-4'>
									<Skeleton className='h-6 w-1/2' />
								</div>
							</div>
						</div>
						<div>
							<Skeleton className='h-[300px] w-full rounded-lg' />
						</div>
					</div>
				</MaxWidthWrapper>
			</div>
		);
	}

	if (error || !adData) {
		return (
			<div className='flex min-h-screen flex-col'>
				<header className='w-full border-b'>
					<MaxWidthWrapper className='flex h-16 items-center justify-between py-4'>
						<div className='flex items-center gap-2'>
							<Link href='/ads'>
								<Button variant='ghost' size='icon' className='mr-2'>
									<ArrowLeft className='h-5 w-5' />
									<span className='sr-only'>Back</span>
								</Button>
							</Link>
							<h1 className='text-lg font-semibold'>Ad not found</h1>
						</div>
					</MaxWidthWrapper>
				</header>
				<MaxWidthWrapper className='flex-1 py-6 md:py-10'>
					<div className='flex flex-col items-center justify-center'>
						<h2 className='text-2xl font-bold'>Error loading ad</h2>
						<p className='text-muted-foreground'>The ad could not be found or there was an error loading it.</p>
						<Button className='mt-4' asChild>
							<Link href='/ads'>Browse other ads</Link>
						</Button>
					</div>
				</MaxWidthWrapper>
			</div>
		);
	}

	const ad = {
		id: adData.data._id || "",
		title: adData.data.title,
		price: adData.data.price,
		description: adData.data.description,
		location: adData.data.location,
		postedDate: adData.data.createdAt ? new Date(adData.data.createdAt).toLocaleDateString() : "Recently",
		category: adData.data.category,
		condition: adData.data.condition,
		images: adData.data.images.length > 0 ? adData.data.images : ["/placeholder.svg"],
		user: adData.data.user
	};

	return (
		<div className='flex min-h-screen flex-col'>
			<header className='w-full border-b '>
				<MaxWidthWrapper className=' flex h-16 items-center justify-between py-4'>
					<div className='flex items-center gap-2'>
						<Link href='/ads'>
							<Button variant='ghost' size='icon' className='mr-2'>
								<ArrowLeft className='h-5 w-5' />
								<span className='sr-only'>Back</span>
							</Button>
						</Link>
						<h1 className='text-lg font-semibold line-clamp-1'>{ad.title}</h1>
					</div>
					{/* <div className='flex items-center gap-2'>
						<Button variant='outline' size='icon'>
							<Heart className='h-5 w-5' />
							<span className='sr-only'>Save</span>
						</Button>
						<Button variant='outline' size='icon'>
							<Share2 className='h-5 w-5' />
							<span className='sr-only'>Share</span>
						</Button>
						<Dialog>
							<DialogTrigger asChild>
								<Button variant='outline' size='icon'>
									<Flag className='h-5 w-5' />
									<span className='sr-only'>Report</span>
								</Button>
							</DialogTrigger>
							<DialogContent>
								<DialogHeader>
									<DialogTitle>Report this ad</DialogTitle>
									<DialogDescription>
										Please let us know why you&apos;re reporting this ad. We take reports seriously and will review it
										promptly.
									</DialogDescription>
								</DialogHeader>
								<div className='grid gap-4 py-4'>
									<Button variant='outline' className='justify-start'>
										Prohibited item or service
									</Button>
									<Button variant='outline' className='justify-start'>
										Fraudulent ad
									</Button>
									<Button variant='outline' className='justify-start'>
										Duplicate listing
									</Button>
									<Button variant='outline' className='justify-start'>
										Incorrect category
									</Button>
									<Button variant='outline' className='justify-start'>
										Other issue
									</Button>
								</div>
							</DialogContent>
						</Dialog>
					</div> */}
				</MaxWidthWrapper>
			</header>
			<MaxWidthWrapper className='flex-1  py-6 md:py-10'>
				<div className='grid gap-6 lg:grid-cols-3 lg:gap-10'>
					<div className='lg:col-span-2'>
						<div className='mb-6'>
							<Tabs defaultValue='image-0' className='w-full'>
								<div className='relative rounded-lg bg-muted'>
									{ad.images.map((image, index) => (
										<TabsContent key={index} value={`image-${index}`} className='p-0'>
											<div className='flex justify-center items-center'>
												<Image
													src={image || "/placeholder.svg"}
													alt={`${ad.title} - Image ${index + 1}`}
													className='max-h-[500px] w-auto object-contain'
													width={800}
													height={500}
												/>
											</div>
										</TabsContent>
									))}
								</div>
								<TabsList className='mt-3 justify-start'>
									{ad.images.map((_, index) => (
										<TabsTrigger key={index} value={`image-${index}`} className='h-16 w-16 p-0'>
											<Image
												src={ad.images[index] || "/placeholder.svg"}
												alt={`Thumbnail ${index + 1}`}
												className='h-full w-full object-contain'
												width={64}
												height={64}
											/>
										</TabsTrigger>
									))}
								</TabsList>
							</Tabs>
						</div>

						<div className='space-y-6'>
							<div>
								<div className='flex items-center justify-between'>
									<h1 className='text-2xl font-bold md:text-3xl'>{ad.title}</h1>
									<p className='text-2xl font-bold text-brand-dark-300'>৳ {ad.price}</p>
								</div>
								<div className='mt-2 flex flex-wrap gap-2'>
									<Badge variant='secondary'>{ad.category}</Badge>
									<Badge variant='outline'>{ad.condition}</Badge>
								</div>
								<div className='mt-4 flex flex-wrap items-center gap-4 text-sm text-muted-foreground'>
									<div className='flex items-center gap-1'>
										<MapPin className='h-4 w-4' />
										<span>{ad.location}</span>
									</div>
									<div className='flex items-center gap-1'>
										<Calendar className='h-4 w-4' />
										<span>Posted {ad.postedDate}</span>
									</div>
								</div>
							</div>

							<Separator />

							<div>
								<h2 className='mb-3 text-xl font-semibold'>Description</h2>
								<p className='text-muted-foreground'>{ad.description}</p>
							</div>

							<Separator />

							{/* <div>
								<h2 className='mb-3 text-xl font-semibold'>Features</h2>
								<ul className='grid grid-cols-2 gap-2 sm:grid-cols-3'>
									{ad.features.map((feature, index) => (
										<li key={index} className='flex items-center gap-2'>
											<div className='h-2 w-2 rounded-full bg-brand' />
											<span>{feature}</span>
										</li>
									))}
								</ul>
							</div> */}
						</div>
					</div>

					<div className='space-y-6'>
						<Card>
							<CardContent className='p-6'>
								<div className='flex items-center gap-4'>
									<Avatar className='h-12 w-12'>
										<AvatarImage src={"/placeholder.svg"} alt={ad?.user?.name} />
										<AvatarFallback>{ad?.user?.name?.charAt(0)}</AvatarFallback>
									</Avatar>
									<div>
										<h3 className='font-semibold'>{ad?.user?.name}</h3>
										{/* <p className='text-sm text-muted-foreground'>
											Member since {format(new Date(ad?.user?.createdAt), "MMMM yyyy")}
										</p> */}
									</div>
								</div>
								{/* <div className='mt-4 grid gap-2'>
									<div className='flex items-center justify-between text-sm'>
										<span>Rating:</span>
										<span className='font-medium'>{ad.seller.rating}/5.0</span>
									</div>
									<div className='flex items-center justify-between text-sm'>
										<span>Active ads:</span>
										<span className='font-medium'>{ad.seller.adCount}</span>
									</div>
								</div> */}
								<Separator className='my-4' />
								{showContact ? (
									<div className='space-y-4'>
										<div className='rounded-md bg-muted p-3 text-center'>
											<div className='text-sm text-muted-foreground'>Contact number</div>
											<div className='flex items-center justify-center gap-1 text-lg font-semibold'>
												<Phone className='h-4 w-4 text-brand-dark-300' />
												{ad?.user?.phone}
											</div>
										</div>
										{/* <Button className='w-full' variant='outline'>
											Message Seller
										</Button> */}
									</div>
								) : (
									<Button
										className='w-full bg-brand text-brand-black hover:bg-brand/80'
										onClick={() => setShowContact(true)}
									>
										Show Contact Info
									</Button>
								)}
							</CardContent>
						</Card>

						<Card>
							<CardContent className='p-6'>
								<h3 className='mb-2 font-semibold'>Safety Tips</h3>
								<ul className='space-y-2 text-sm text-muted-foreground'>
									<li>Meet in a public, well-lit place</li>
									<li>Check the item before paying</li>
									<li>Don&apos;t share personal financial information</li>
									<li>Consider using secure payment methods</li>
								</ul>
							</CardContent>
						</Card>
					</div>
				</div>

				<div className='mt-12'>
					<h2 className='mb-6 text-2xl font-bold'>Similar Ads</h2>
					<SimilarAds category={ad.category} currentAdId={ad.id} />
				</div>
			</MaxWidthWrapper>
		</div>
	);
}
