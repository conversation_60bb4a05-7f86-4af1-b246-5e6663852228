"use client";

import {
	<PERSON>ert<PERSON>ircle,
	ArrowUpDown,
	CheckCircle,
	ChevronDown,
	Clock,
	Edit,
	Eye,
	EyeOff,
	MoreHorizontal,
	PlusCircle,
	Tag,
	Trash2
} from "lucide-react";
import Link from "next/link";
import { useState } from "react";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle
} from "@/components/ui/dialog";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuLabel,
	DropdownMenuSeparator,
	DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import MaxWidthWrapper from "@/components/ui/max-width-wrapper";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { useToast } from "@/hooks/use-toast";
import { useDeleteAdMutation, useGetMyAdsQuery } from "@/redux/apis/adsApis";
import Image from "next/image";

const userData = {
	name: "Alex Johnson",
	memberSince: "January 2022",
	totalAds: 5,
	activeAds: 2,
	soldItems: 1,
	totalViews: 908,
	totalInquiries: 40
};

export default function DashboardPage() {
	const { toast } = useToast();
	const [searchQuery, setSearchQuery] = useState("");
	const [selectedStatus, setSelectedStatus] = useState("all");
	const [sortBy, setSortBy] = useState("newest");
	const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
	const [adToDelete, setAdToDelete] = useState<string | null>(null);
	const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
	const { data: ads } = useGetMyAdsQuery();
	const [deleteAd, { isLoading: isDeleting }] = useDeleteAdMutation();
	console.log("ads", ads);
	// Filter ads based on search query and status
	const filteredAds = ads?.data?.filter((ad) => {
		const matchesSearch =
			ad.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
			ad.category.toLowerCase().includes(searchQuery.toLowerCase());
		const matchesStatus = selectedStatus === "all" || ad?.condition === selectedStatus;
		return matchesSearch && matchesStatus;
	});

	// Sort ads
	const sortedAds = [...(filteredAds ?? [])].sort((a, b) => {
		switch (sortBy) {
			case "newest":
				return new Date(b.createdAt ?? "").getTime() - new Date(a.createdAt ?? "").getTime();
			case "oldest":
				return new Date(a.createdAt ?? "").getTime() - new Date(b.createdAt ?? "").getTime();
			case "price_high":
				return b.price - a.price;
			case "price_low":
				return a.price - b.price;
			case "views":
				return (b?.views ?? 0) - (a?.views ?? 0);
			default:
				return 0;
		}
	});

	const handleDeleteAd = (adId: string) => {
		setAdToDelete(adId);
		setDeleteDialogOpen(true);
	};

	const confirmDelete = async () => {
		if (!adToDelete) return;

		try {
			const response = await deleteAd(adToDelete).unwrap();

			if (response.success) {
				toast({
					title: "Ad deleted successfully",
					description: "Your ad has been permanently removed."
				});
			}
		} catch (error) {
			console.error("Failed to delete ad:", error);
			toast({
				title: "Failed to delete ad",
				description: "There was an error deleting your ad. Please try again.",
				variant: "destructive"
			});
		} finally {
			setDeleteDialogOpen(false);
			setAdToDelete(null);
		}
	};

	const handleStatusChange = (adId: string, newStatus: string) => {
		// In a real app, this would make an API call to update the ad status
		toast({
			title: "Status updated",
			description: `Ad status changed to ${newStatus}.`
		});
	};

	// Helper function to render status badge
	const renderStatusBadge = (status: string) => {
		switch (status) {
			case "active":
				return (
					<Badge className='bg-emerald-100 text-emerald-800 hover:bg-emerald-200 dark:bg-emerald-900 dark:text-emerald-300'>
						<CheckCircle className='mr-1 h-3 w-3' /> Active
					</Badge>
				);
			case "pending":
				return (
					<Badge variant='outline' className='border-amber-500 text-amber-600 dark:text-amber-400'>
						<Clock className='mr-1 h-3 w-3' /> Pending
					</Badge>
				);
			case "expired":
				return (
					<Badge
						variant='secondary'
						className='bg-gray-200 text-gray-600 hover:bg-gray-300 dark:bg-gray-800 dark:text-gray-400'
					>
						<EyeOff className='mr-1 h-3 w-3' /> Expired
					</Badge>
				);
			case "sold":
				return (
					<Badge className='bg-blue-100 text-blue-800 hover:bg-blue-200 dark:bg-blue-900 dark:text-blue-300'>
						<CheckCircle className='mr-1 h-3 w-3' /> Sold
					</Badge>
				);
			default:
				return <Badge variant='outline'>{status}</Badge>;
		}
	};

	return (
		<div className='flex min-h-screen flex-col'>
			<header className=' w-full border-b '>
				<MaxWidthWrapper className=' flex h-16 items-center justify-between py-4'>
					<Link href='/ads' className='flex items-center gap-2'>
						<Tag className='h-6 w-6 text-brand-dark-300' />
						<span className='text-xl font-bold'>AdSpot</span>
					</Link>
					{/* <div className='hidden md:flex md:flex-1 md:items-center md:justify-center md:px-6'>
						<div className='relative w-full max-w-md'>
							<Search className='absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground' />
							<Input
								type='search'
								placeholder='Search for products...'
								className='w-full bg-background pl-8 md:w-full'
							/>
						</div>
					</div> */}
					<div className='flex items-center gap-4'>
						<Link href='/ads/my-ads'>
							<Button variant='outline' size='sm' className=' border-brand'>
								My Ads
							</Button>
						</Link>
						<Link href='/ads/create-ad'>
							<Button className='gap-1 bg-brand hover:bg-brand/80 text-brand-black'>
								<PlusCircle className='h-4 w-4' />
								Post Ad
							</Button>
						</Link>
					</div>
				</MaxWidthWrapper>
			</header>

			<MaxWidthWrapper className='flex-1  py-6 md:py-10'>
				<div className='flex flex-col gap-6'>
					{/* Dashboard Header */}
					<div className='flex flex-col md:flex-row md:items-center md:justify-between gap-4'>
						<div>
							<h1 className='text-2xl font-bold tracking-tight'>My Ads</h1>
							<p className='text-muted-foreground'>Manage and track all your posted advertisements</p>
						</div>
						{/* <div className='flex items-center gap-2'>
							<Link href='/create-ad'>
								<Button className='gap-1 bg-emerald-600 hover:bg-emerald-700'>
									<PlusCircle className='h-4 w-4' />
									Post New Ad
								</Button>
							</Link>
						</div> */}
					</div>

					{/* Stats Cards */}
					{/* <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
						<Card>
							<CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
								<CardTitle className='text-sm font-medium'>Total Ads</CardTitle>
								<Tag className='h-4 w-4 text-muted-foreground' />
							</CardHeader>
							<CardContent>
								<div className='text-2xl font-bold'>{userData.totalAds}</div>
								<p className='text-xs text-muted-foreground'>
									{userData.activeAds} active, {userData.soldItems} sold
								</p>
							</CardContent>
						</Card>
						<Card>
							<CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
								<CardTitle className='text-sm font-medium'>Total Views</CardTitle>
								<Eye className='h-4 w-4 text-muted-foreground' />
							</CardHeader>
							<CardContent>
								<div className='text-2xl font-bold'>{userData.totalViews}</div>
								<p className='text-xs text-muted-foreground'>
									~{Math.round(userData.totalViews / userData.totalAds)} per ad
								</p>
							</CardContent>
						</Card>
						<Card>
							<CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
								<CardTitle className='text-sm font-medium'>Total Inquiries</CardTitle>
								<AlertCircle className='h-4 w-4 text-muted-foreground' />
							</CardHeader>
							<CardContent>
								<div className='text-2xl font-bold'>{userData.totalInquiries}</div>
								<p className='text-xs text-muted-foreground'>
									~{Math.round(userData.totalInquiries / userData.totalAds)} per ad
								</p>
							</CardContent>
						</Card>
						<Card>
							<CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
								<CardTitle className='text-sm font-medium'>Member Since</CardTitle>
								<Clock className='h-4 w-4 text-muted-foreground' />
							</CardHeader>
							<CardContent>
								<div className='text-2xl font-bold'>{userData.memberSince}</div>
								<p className='text-xs text-muted-foreground'>{userData.name}</p>
							</CardContent>
						</Card>
					</div> */}

					{/* Filters and Controls */}
					{/* <div className='flex flex-col md:flex-row md:items-center md:justify-between gap-4'>
						<div className='flex flex-col sm:flex-row gap-2 sm:items-center'>
							<Select value={selectedStatus} onValueChange={setSelectedStatus}>
								<SelectTrigger className='w-[180px]'>
									<SelectValue placeholder='Filter by status' />
								</SelectTrigger>
								<SelectContent>
									<SelectGroup>
										<SelectLabel>Status</SelectLabel>
										<SelectItem value='all'>All Ads</SelectItem>
										<SelectItem value='active'>Active</SelectItem>
										<SelectItem value='pending'>Pending</SelectItem>
										<SelectItem value='expired'>Expired</SelectItem>
										<SelectItem value='sold'>Sold</SelectItem>
									</SelectGroup>
								</SelectContent>
							</Select>

							<Select value={sortBy} onValueChange={setSortBy}>
								<SelectTrigger className='w-[180px]'>
									<SelectValue placeholder='Sort by' />
								</SelectTrigger>
								<SelectContent>
									<SelectGroup>
										<SelectLabel>Sort by</SelectLabel>
										<SelectItem value='newest'>Newest First</SelectItem>
										<SelectItem value='oldest'>Oldest First</SelectItem>
										<SelectItem value='price_high'>Price: High to Low</SelectItem>
										<SelectItem value='price_low'>Price: Low to High</SelectItem>
										<SelectItem value='views'>Most Viewed</SelectItem>
									</SelectGroup>
								</SelectContent>
							</Select>
						</div>

						<div className='flex items-center gap-2'>
							<div className='md:hidden relative w-full'>
								<Search className='absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground' />
								<Input
									type='search'
									placeholder='Search your ads...'
									className='w-full bg-background pl-8'
									value={searchQuery}
									onChange={(e) => setSearchQuery(e.target.value)}
								/>
							</div>
							<div className='flex border rounded-md'>
								<Button
									variant={viewMode === "grid" ? "secondary" : "ghost"}
									size='sm'
									className='rounded-r-none'
									onClick={() => setViewMode("grid")}
								>
									Grid
								</Button>
								<Separator orientation='vertical' className='h-[22px] my-auto' />
								<Button
									variant={viewMode === "list" ? "secondary" : "ghost"}
									size='sm'
									className='rounded-l-none'
									onClick={() => setViewMode("list")}
								>
									List
								</Button>
							</div>
						</div>
					</div> */}

					{/* Results count */}
					<div>
						<p className='text-sm text-muted-foreground'>
							Showing {sortedAds.length} {sortedAds.length === 1 ? "ad" : "ads"}
							{selectedStatus !== "all" ? ` with status "${selectedStatus}"` : ""}
						</p>
					</div>

					{/* Grid View */}
					{viewMode === "grid" && (
						<div className='grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'>
							{sortedAds.map((ad) => (
								<Card key={ad._id} className='overflow-hidden'>
									<div className='relative'>
										<div className='aspect-video w-full overflow-hidden bg-muted'>
											<Image
												src={ad.images[0] || "/placeholder.svg"}
												alt={ad.title}
												className='h-full w-full object-cover'
												width={400}
												height={225}
											/>
										</div>
										<div className='absolute top-2 right-2'>
											<DropdownMenu>
												<DropdownMenuTrigger asChild>
													<Button variant='secondary' size='icon' className='h-8 w-8 bg-background/80 backdrop-blur'>
														<MoreHorizontal className='h-4 w-4' />
													</Button>
												</DropdownMenuTrigger>
												<DropdownMenuContent align='end'>
													<DropdownMenuLabel>Actions</DropdownMenuLabel>
													<DropdownMenuItem asChild>
														<Link href={`/ads/ad/${ad._id}`}>View Ad</Link>
													</DropdownMenuItem>
													<DropdownMenuItem asChild>
														<Link href={`/ads/edit-ad/${ad._id}`}>Edit Ad</Link>
													</DropdownMenuItem>
													<DropdownMenuSeparator />
													<DropdownMenuItem
														onClick={() => handleStatusChange(ad?._id ?? "", "active")}
														disabled={ad.condition === "active"}
													>
														Mark as Active
													</DropdownMenuItem>
													<DropdownMenuItem
														onClick={() => handleStatusChange(ad._id ?? "", "sold")}
														disabled={ad.condition === "sold"}
													>
														Mark as Sold
													</DropdownMenuItem>
													<DropdownMenuSeparator />
													<DropdownMenuItem
														onClick={() => handleDeleteAd(ad._id ?? "")}
														className='text-red-600 focus:text-red-600'
													>
														Delete Ad
													</DropdownMenuItem>
												</DropdownMenuContent>
											</DropdownMenu>
										</div>
									</div>
									<CardContent className='p-4'>
										<div className='flex items-center justify-between mb-2'>
											{renderStatusBadge(ad.condition)}
											<span className='text-sm text-muted-foreground'>
												{new Date(ad.createdAt ?? "").toLocaleDateString()}
											</span>
										</div>
										<h3 className='font-semibold line-clamp-1 mb-1'>{ad.title}</h3>
										<p className='text-lg font-bold text-brand-dark-300'>${ad.price}</p>
									</CardContent>
									<CardFooter className='flex items-center justify-between border-t p-4 pt-3'>
										<div className='flex items-center gap-4'>
											<div className='flex items-center gap-1 text-sm text-muted-foreground'>
												<Eye className='h-3 w-3' />
												<span>{ad?.views ?? 0}</span>
											</div>
											<div className='flex items-center gap-1 text-sm text-muted-foreground'>
												<AlertCircle className='h-3 w-3' />
												<span>{ad?.inquiries ?? 0}</span>
											</div>
										</div>
										<div className='flex gap-2'>
											<Link href={`/ads/edit-ad/${ad._id}`}>
												<Button variant='ghost' size='sm' className='h-8 w-8 p-0'>
													<Edit className='h-4 w-4' />
													<span className='sr-only'>Edit</span>
												</Button>
											</Link>
											<Button
												variant='ghost'
												size='sm'
												className='h-8 w-8 p-0 hover:text-red-600'
												onClick={() => handleDeleteAd(ad._id ?? "")}
											>
												<Trash2 className='h-4 w-4' />
												<span className='sr-only'>Delete</span>
											</Button>
										</div>
									</CardFooter>
								</Card>
							))}
						</div>
					)}

					{/* List View */}
					{viewMode === "list" && (
						<div className='rounded-md border'>
							<Table>
								<TableHeader>
									<TableRow>
										<TableHead className='w-[300px]'>Ad Details</TableHead>
										<TableHead>Status</TableHead>
										<TableHead>Price</TableHead>
										<TableHead>
											<div className='flex items-center gap-1'>
												Posted Date
												<ArrowUpDown className='h-3 w-3' />
											</div>
										</TableHead>
										<TableHead>Views</TableHead>
										<TableHead>Inquiries</TableHead>
										<TableHead className='text-right'>Actions</TableHead>
									</TableRow>
								</TableHeader>
								<TableBody>
									{sortedAds.map((ad) => (
										<TableRow key={ad._id}>
											<TableCell>
												<div className='flex items-center gap-3'>
													<div className='h-12 w-12 rounded-md overflow-hidden bg-muted'>
														<Image
															src={ad.images[0] || "/placeholder.svg"}
															alt={ad.title}
															className='h-full w-full object-cover'
															width={48}
															height={48}
														/>
													</div>
													<div>
														<div className='font-medium line-clamp-1'>{ad.title}</div>
														<div className='text-sm text-muted-foreground'>{ad.category}</div>
													</div>
												</div>
											</TableCell>
											<TableCell>{renderStatusBadge(ad.condition)}</TableCell>
											<TableCell className='font-medium'>${ad.price}</TableCell>
											<TableCell>{new Date(ad.createdAt ?? "").toLocaleDateString()}</TableCell>
											<TableCell>{ad?.views ?? 0}</TableCell>
											<TableCell>{ad?.inquiries ?? 0}</TableCell>
											<TableCell className='text-right'>
												<div className='flex justify-end gap-2'>
													<Link href={`/ads/ad/${ad._id}`}>
														<Button variant='ghost' size='sm' className='h-8 w-8 p-0'>
															<Eye className='h-4 w-4' />
															<span className='sr-only'>View</span>
														</Button>
													</Link>
													<Link href={`/ads/edit-ad/${ad._id}`}>
														<Button variant='ghost' size='sm' className='h-8 w-8 p-0'>
															<Edit className='h-4 w-4' />
															<span className='sr-only'>Edit</span>
														</Button>
													</Link>
													<Button
														variant='ghost'
														size='sm'
														className='h-8 w-8 p-0 hover:text-red-600'
														onClick={() => handleDeleteAd(ad._id ?? "")}
													>
														<Trash2 className='h-4 w-4' />
														<span className='sr-only'>Delete</span>
													</Button>
													<DropdownMenu>
														<DropdownMenuTrigger asChild>
															<Button variant='ghost' size='sm' className='h-8 w-8 p-0'>
																<ChevronDown className='h-4 w-4' />
																<span className='sr-only'>More</span>
															</Button>
														</DropdownMenuTrigger>
														<DropdownMenuContent align='end'>
															<DropdownMenuItem
																onClick={() => handleStatusChange(ad._id ?? "", "active")}
																disabled={ad.condition === "active"}
															>
																Mark as Active
															</DropdownMenuItem>
															<DropdownMenuItem
																onClick={() => handleStatusChange(ad._id ?? "", "sold")}
																disabled={ad.condition === "sold"}
															>
																Mark as Sold
															</DropdownMenuItem>
															<DropdownMenuSeparator />
															<DropdownMenuItem
																onClick={() => handleDeleteAd(ad._id ?? "")}
																className='text-red-600 focus:text-red-600'
															>
																Delete Ad
															</DropdownMenuItem>
														</DropdownMenuContent>
													</DropdownMenu>
												</div>
											</TableCell>
										</TableRow>
									))}
								</TableBody>
							</Table>
						</div>
					)}

					{/* Empty state */}
					{sortedAds.length === 0 && (
						<div className='flex flex-col items-center justify-center py-12 text-center'>
							<div className='rounded-full bg-muted p-6 mb-4'>
								<Tag className='h-10 w-10 text-muted-foreground' />
							</div>
							<h3 className='text-xl font-semibold mb-2'>No ads found</h3>
							<p className='text-muted-foreground mb-6 max-w-md'>
								{searchQuery
									? "We couldn't find any ads matching your search criteria."
									: "You haven't posted any ads yet. Create your first ad to get started!"}
							</p>
							<Link href='/create-ad'>
								<Button className='gap-1 bg-emerald-600 hover:bg-emerald-700'>
									<PlusCircle className='h-4 w-4' />
									Post New Ad
								</Button>
							</Link>
						</div>
					)}
				</div>
			</MaxWidthWrapper>

			{/* Delete Confirmation Dialog */}
			<Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>Delete Ad</DialogTitle>
						<DialogDescription>
							Are you sure you want to delete this ad? This action cannot be undone.
						</DialogDescription>
					</DialogHeader>
					<DialogFooter>
						<Button variant='outline' onClick={() => setDeleteDialogOpen(false)}>
							Cancel
						</Button>
						<Button variant='destructive' onClick={confirmDelete} disabled={isDeleting}>
							{isDeleting ? "Deleting..." : "Delete"}
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>
		</div>
	);
}
