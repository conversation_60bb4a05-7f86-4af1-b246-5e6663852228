"use client";

import { PlusCircle, Tag } from "lucide-react";
import Link from "next/link";

import { But<PERSON> } from "@/components/ui/button";
import MaxWidthWrapper from "@/components/ui/max-width-wrapper";
import { FeaturedAds } from "./_components/featrued-ads";

export default function Home() {
	return (
		<div className='flex min-h-screen flex-col'>
			<header className=' w-full border-b '>
				<MaxWidthWrapper className=' flex h-16 items-center justify-between py-4'>
					<Link href='/ads' className='flex items-center gap-2'>
						<Tag className='h-6 w-6 text-brand-dark-300' />
						<span className='text-xl font-bold'>AdSpot</span>
					</Link>
					{/* <div className='hidden md:flex md:flex-1 md:items-center md:justify-center md:px-6'>
						<div className='relative w-full max-w-md'>
							<Search className='absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground' />
							<Input
								type='search'
								placeholder='Search for products...'
								className='w-full bg-background pl-8 md:w-full'
							/>
						</div>
					</div> */}
					<div className='flex items-center gap-4'>
						<Link href='/ads/my-ads'>
							<Button variant='outline' size='sm' className=' border-brand'>
								My Ads
							</Button>
						</Link>
						<Link href='/ads/create-ad'>
							<Button className='gap-1 bg-brand hover:bg-brand/80 text-brand-black'>
								<PlusCircle className='h-4 w-4' />
								Post Ad
							</Button>
						</Link>
					</div>
				</MaxWidthWrapper>
			</header>
			<main className='flex-1'>
				{/* <section className='w-full py-6 md:py-12 lg:py-16 bg-gradient-to-b from-brand-light-100 to-white'>
					<MaxWidthWrapper className=''>
						<div className='grid gap-6 lg:grid-cols-[1fr_400px] lg:gap-12 xl:grid-cols-[1fr_600px]'>
							<div className='flex flex-col justify-center space-y-4'>
								<div className='space-y-2'>
									<h1 className='text-3xl font-bold tracking-tighter sm:text-5xl xl:text-6xl/none'>
										Find & Sell Products in Your Community
									</h1>
									<p className='max-w-[600px] text-muted-foreground md:text-xl'>
										Connect with local buyers and sellers. Post your ads for free and find great deals near you.
									</p>
								</div>
								<div className='flex flex-col gap-2 min-[400px]:flex-row'>
									<Link href='/ads/all'>
										<Button size='lg' className='bg-brand hover:bg-brand/80 text-brand-black'>
											Browse Ads
										</Button>
									</Link>
									<Link href='/ads/create-ad'>
										<Button size='lg' variant='outline'>
											Post Your Ad
										</Button>
									</Link>
								</div>
							</div>
							<div className='hidden md:block'>
								<FeaturedAdCarousel />
							</div>
						</div>
					</MaxWidthWrapper>
				</section> */}

				{/* <MaxWidthWrapper className='container px-4 py-8 md:px-6 md:py-12'>
					<div className='mb-8 flex items-center justify-between'>
						<h2 className='text-2xl font-bold tracking-tight'>Browse Categories</h2>
						<Link href='/categories' className='text-sm font-medium text-brand-dark-300 hover:underline '>
							View all categories
						</Link>
					</div>
					<CategorySelector />
				</MaxWidthWrapper> */}

				{/* featured ads */}
				<FeaturedAds />
			</main>
		</div>
	);
}
