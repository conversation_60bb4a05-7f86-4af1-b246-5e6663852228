"use client";

import { <PERSON><PERSON>ronDown, PlusCircle, Search, SlidersHorizontal, Tag, X } from "lucide-react";
import Link from "next/link";
import { useEffect, useState } from "react";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import MaxWidthWrapper from "@/components/ui/max-width-wrapper";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import {
	Sheet,
	SheetClose,
	SheetContent,
	SheetDescription,
	SheetFooter,
	SheetHeader,
	Sheet<PERSON>itle,
	SheetTrigger
} from "@/components/ui/sheet";
import { Slider } from "@/components/ui/slider";
import { ProductCard } from "../../_components/product-card";

// Category data mapping
const categoryData = {
	"phones-and-tablets": {
		name: "Phones & Tablets",
		icon: "Smartphone",
		description: "Browse the latest smartphones, tablets, and accessories from top brands.",
		featuredBrands: ["Apple", "Samsung", "Google", "OnePlus", "Xiaomi"],
		subcategories: ["Smartphones", "Tablets", "Accessories", "Cases", "Screen Protectors"],
		filters: ["Brand", "Model", "Storage", "Condition", "Color"]
	},
	macbook: {
		name: "MacBook",
		icon: "Laptop",
		description: "Find MacBook Pro, MacBook Air, and accessories at great prices.",
		featuredBrands: ["Apple"],
		subcategories: ["MacBook Pro", "MacBook Air", "Accessories", "Parts", "Chargers"],
		filters: ["Model", "Year", "Processor", "RAM", "Storage"]
	},
	"hubs-docks": {
		name: "Hubs & Docks",
		icon: "Usb",
		description: "Connect all your devices with premium hubs and docking stations.",
		featuredBrands: ["Anker", "Belkin", "CalDigit", "Dell", "HP"],
		subcategories: ["USB Hubs", "Docking Stations", "Port Replicators", "Adapters"],
		filters: ["Ports", "Connection Type", "Power Delivery", "Brand"]
	},
	stylus: {
		name: "Stylus",
		icon: "Pencil",
		description: "Digital pens and styluses for tablets, iPads, and touchscreen devices.",
		featuredBrands: ["Apple", "Samsung", "Wacom", "Adonit", "Logitech"],
		subcategories: ["Apple Pencil", "S Pen", "Universal Stylus", "Drawing Pens"],
		filters: ["Compatibility", "Pressure Sensitivity", "Connectivity", "Battery Life"]
	},
	"smart-watch": {
		name: "Smart Watch",
		icon: "Watch",
		description: "Stay connected with smartwatches from Apple, Samsung, Garmin, and more.",
		featuredBrands: ["Apple", "Samsung", "Garmin", "Fitbit", "Fossil"],
		subcategories: ["Apple Watch", "Galaxy Watch", "Fitness Trackers", "Luxury Smartwatches"],
		filters: ["Brand", "Size", "Features", "Connectivity", "Water Resistance"]
	}
};

// Mock data for ads
const mockAds = (category: string) => {
	interface Ad {
		id: string;
		title: string;
		price: number;
		location: string;
		category: string;
		condition: string;
		brand: string;
		postedDate: string;
		image: string;
	}

	const categorySpecificAds: Ad[] = [];
	const count = Math.floor(Math.random() * 10) + 15; // 15-25 ads

	for (let i = 0; i < count; i++) {
		let title, price, image, condition, brand;

		switch (category) {
			case "phones-and-tablets":
				brand = ["Apple", "Samsung", "Google", "OnePlus", "Xiaomi"][i % 5];
				title = [
					`${brand} Smartphone - 128GB`,
					`${brand} Tablet Pro - Like New`,
					`${brand} Phone - Latest Model`,
					`${brand} Foldable Phone - Barely Used`,
					`${brand} Mini Tablet - Great Condition`
				][i % 5];
				price = Math.floor(Math.random() * 800) + 200;
				image = i % 2 === 0 ? "/nothing-phone.jpg" : "/nothing-phone-2.jpg";
				condition = ["New", "Like New", "Excellent", "Good", "Fair"][i % 5];
				break;

			case "macbook":
				title = [
					'MacBook Pro 16" - M2 Pro',
					'MacBook Air 13" - M2',
					'MacBook Pro 14" - M1 Max',
					"MacBook Air - Latest Model",
					"MacBook Pro - Space Gray"
				][i % 5];
				price = Math.floor(Math.random() * 1000) + 800;
				image = "/tech-launch-event.png";
				condition = ["New", "Like New", "Excellent", "Good", "Fair"][i % 5];
				brand = "Apple";
				break;

			case "hubs-docks":
				brand = ["Anker", "Belkin", "CalDigit", "Dell", "HP"][i % 5];
				title = [
					`${brand} USB-C Hub - 7 Ports`,
					`${brand} Thunderbolt Dock - 4K Support`,
					`${brand} Docking Station - Dual Monitor`,
					`${brand} Travel Hub - Compact`,
					`${brand} Pro Dock - Power Delivery`
				][i % 5];
				price = Math.floor(Math.random() * 150) + 50;
				image = "/diverse-products-display.png";
				condition = ["New", "Like New", "Excellent", "Good", "Fair"][i % 5];
				break;

			case "stylus":
				brand = ["Apple", "Samsung", "Wacom", "Adonit", "Logitech"][i % 5];
				title = [
					`${brand} Pencil - 2nd Generation`,
					`${brand} Digital Stylus - Precision Tip`,
					`${brand} Drawing Pen - Artists Choice`,
					`${brand} Touch Pen - Universal`,
					`${brand} Smart Stylus - Rechargeable`
				][i % 5];
				price = Math.floor(Math.random() * 100) + 30;
				image = "/diverse-products-display.png";
				condition = ["New", "Like New", "Excellent", "Good", "Fair"][i % 5];
				break;

			case "smart-watch":
				brand = ["Apple", "Samsung", "Garmin", "Fitbit", "Fossil"][i % 5];
				title = [
					`${brand} Watch Series 7 - GPS`,
					`${brand} Fitness Tracker - Heart Monitor`,
					`${brand} Smartwatch - Latest Model`,
					`${brand} Watch - Cellular + GPS`,
					`${brand} Sport Watch - Waterproof`
				][i % 5];
				price = Math.floor(Math.random() * 300) + 150;
				image = "/diverse-products-display.png";
				condition = ["New", "Like New", "Excellent", "Good", "Fair"][i % 5];
				break;

			default:
				brand = ["Generic", "Unknown", "Various", "Mixed", "Other"][i % 5];
				title = `${brand} Product ${i + 1}`;
				price = Math.floor(Math.random() * 500) + 50;
				image = "/diverse-products-display.png";
				condition = ["New", "Like New", "Excellent", "Good", "Fair"][i % 5];
		}

		categorySpecificAds.push({
			id: `ad-${category}-${i}`,
			title,
			price,
			location: ["New York", "Los Angeles", "Chicago", "Houston", "Miami", "Seattle", "Denver", "Boston"][i % 8],
			category,
			condition,
			brand,
			postedDate: `${Math.floor(Math.random() * 30) + 1} days ago`,
			image
		});
	}

	return categorySpecificAds;
};

export default function CategoryPage() {
	const [searchQuery, setSearchQuery] = useState("");
	const [selectedBrands, setSelectedBrands] = useState<string[]>([]);
	const [selectedConditions, setSelectedConditions] = useState<string[]>([]);
	const [priceRange, setPriceRange] = useState<[number, number]>([0, 1000]);
	const [sortOption, setSortOption] = useState("newest");
	const [datePosted, setDatePosted] = useState("any");
	const [ads, setAds] = useState<any[]>([]);
	const [isFilterOpen, setIsFilterOpen] = useState(false);
	const [isMobile, setIsMobile] = useState(false);
	const [categoryInfo, setCategoryInfo] = useState<any>(null);

	// Check if mobile on mount
	useEffect(() => {
		const checkIfMobile = () => {
			setIsMobile(window.innerWidth < 768);
		};

		checkIfMobile();
		window.addEventListener("resize", checkIfMobile);

		return () => {
			window.removeEventListener("resize", checkIfMobile);
		};
	}, []);

	// Load category data and ads
	useEffect(() => {
		const slug = "params.slug";
		const info = categoryData[slug as keyof typeof categoryData] || {
			name: slug.charAt(0).toUpperCase() + slug.slice(1).replace(/-/g, " "),
			description: "Browse all items in this category.",
			featuredBrands: [],
			subcategories: [],
			filters: []
		};

		setCategoryInfo(info);
		setAds(mockAds(slug));

		// Reset filters when category changes
		setSelectedBrands([]);
		setSelectedConditions([]);
		setPriceRange([0, 1000]);
		setSortOption("newest");
		setDatePosted("any");
		setSearchQuery("");
	}, []);

	// Apply filters
	const filteredAds = ads.filter((ad) => {
		// Search query filter
		if (searchQuery && !ad.title.toLowerCase().includes(searchQuery.toLowerCase())) {
			return false;
		}

		// Brand filter
		if (selectedBrands.length > 0 && !selectedBrands.includes(ad.brand)) {
			return false;
		}

		// Condition filter
		if (selectedConditions.length > 0 && !selectedConditions.includes(ad.condition)) {
			return false;
		}

		// Price range filter
		if (ad.price < priceRange[0] || ad.price > priceRange[1]) {
			return false;
		}

		// Date posted filter
		if (datePosted !== "any") {
			const daysAgo = Number.parseInt(datePosted);
			const postedDays = Number.parseInt(ad.postedDate.split(" ")[0]);
			if (postedDays > daysAgo) {
				return false;
			}
		}

		return true;
	});

	// Sort ads
	const sortedAds = [...filteredAds].sort((a, b) => {
		switch (sortOption) {
			case "newest":
				return Number.parseInt(a.postedDate) - Number.parseInt(b.postedDate);
			case "oldest":
				return Number.parseInt(b.postedDate) - Number.parseInt(a.postedDate);
			case "price_low":
				return a.price - b.price;
			case "price_high":
				return b.price - a.price;
			default:
				return 0;
		}
	});

	const handleBrandChange = (brand: string) => {
		setSelectedBrands((prev) => (prev.includes(brand) ? prev.filter((b) => b !== brand) : [...prev, brand]));
	};

	const handleConditionChange = (condition: string) => {
		setSelectedConditions((prev) =>
			prev.includes(condition) ? prev.filter((c) => c !== condition) : [...prev, condition]
		);
	};

	const clearAllFilters = () => {
		setSearchQuery("");
		setSelectedBrands([]);
		setSelectedConditions([]);
		setPriceRange([0, 1000]);
		setDatePosted("any");
		setSortOption("newest");
	};

	// Filter sidebar content
	const FilterContent = () => (
		<div className='space-y-6'>
			{categoryInfo?.featuredBrands && categoryInfo.featuredBrands.length > 0 && (
				<div>
					<h3 className='font-medium mb-3'>Brand</h3>
					<div className='space-y-2 max-h-[200px] overflow-y-auto pr-2'>
						{categoryInfo.featuredBrands.map((brand: string) => (
							<div key={brand} className='flex items-center space-x-2'>
								<Checkbox
									id={`brand-${brand}`}
									checked={selectedBrands.includes(brand)}
									onCheckedChange={() => handleBrandChange(brand)}
								/>
								<Label htmlFor={`brand-${brand}`} className='text-sm font-normal cursor-pointer'>
									{brand}
								</Label>
							</div>
						))}
					</div>
				</div>
			)}

			{categoryInfo?.featuredBrands && categoryInfo.featuredBrands.length > 0 && <Separator />}

			<div>
				<h3 className='font-medium mb-3'>Price Range</h3>
				<div className='px-2'>
					<Slider
						defaultValue={[0, 1000]}
						max={1000}
						step={10}
						value={priceRange}
						onValueChange={(value) => setPriceRange(value as [number, number])}
						className='mb-6'
					/>
					<div className='flex items-center justify-between'>
						<div className='border rounded-md px-3 py-1'>${priceRange[0]}</div>
						<div className='border rounded-md px-3 py-1'>${priceRange[1]}</div>
					</div>
				</div>
			</div>

			<Separator />

			<div>
				<h3 className='font-medium mb-3'>Condition</h3>
				<div className='space-y-2'>
					{["New", "Like New", "Excellent", "Good", "Fair"].map((condition) => (
						<div key={condition} className='flex items-center space-x-2'>
							<Checkbox
								id={`condition-${condition}`}
								checked={selectedConditions.includes(condition)}
								onCheckedChange={() => handleConditionChange(condition)}
							/>
							<Label htmlFor={`condition-${condition}`} className='text-sm font-normal cursor-pointer'>
								{condition}
							</Label>
						</div>
					))}
				</div>
			</div>

			<Separator />

			<div>
				<h3 className='font-medium mb-3'>Date Posted</h3>
				<RadioGroup value={datePosted} onValueChange={setDatePosted}>
					<div className='flex items-center space-x-2'>
						<RadioGroupItem value='any' id='date-any' />
						<Label htmlFor='date-any' className='text-sm font-normal cursor-pointer'>
							Any time
						</Label>
					</div>
					<div className='flex items-center space-x-2'>
						<RadioGroupItem value='1' id='date-today' />
						<Label htmlFor='date-today' className='text-sm font-normal cursor-pointer'>
							Today
						</Label>
					</div>
					<div className='flex items-center space-x-2'>
						<RadioGroupItem value='3' id='date-3days' />
						<Label htmlFor='date-3days' className='text-sm font-normal cursor-pointer'>
							Last 3 days
						</Label>
					</div>
					<div className='flex items-center space-x-2'>
						<RadioGroupItem value='7' id='date-week' />
						<Label htmlFor='date-week' className='text-sm font-normal cursor-pointer'>
							Last week
						</Label>
					</div>
					<div className='flex items-center space-x-2'>
						<RadioGroupItem value='30' id='date-month' />
						<Label htmlFor='date-month' className='text-sm font-normal cursor-pointer'>
							Last month
						</Label>
					</div>
				</RadioGroup>
			</div>

			<Button variant='outline' className='w-full' onClick={clearAllFilters}>
				Clear All Filters
			</Button>
		</div>
	);

	if (!categoryInfo) {
		return (
			<div className='container flex items-center justify-center min-h-screen'>
				<div className='flex flex-col items-center'>
					<div className='h-8 w-8 animate-spin rounded-full border-4 border-emerald-600 border-t-transparent'></div>
					<p className='mt-4 text-muted-foreground'>Loading category...</p>
				</div>
			</div>
		);
	}

	return (
		<div className='flex min-h-screen flex-col'>
			<header className=' w-full border-b '>
				<MaxWidthWrapper className=' flex h-16 items-center justify-between py-4'>
					<Link href='/ads' className='flex items-center gap-2'>
						<Tag className='h-6 w-6 text-brand-dark-300' />
						<span className='text-xl font-bold'>AdSpot</span>
					</Link>
					<div className='hidden md:flex md:flex-1 md:items-center md:justify-center md:px-6'>
						<div className='relative w-full max-w-md'>
							<Search className='absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground' />
							<Input
								type='search'
								placeholder='Search for products...'
								className='w-full bg-background pl-8 md:w-full'
							/>
						</div>
					</div>
					<div className='flex items-center gap-4'>
						<Link href='/ads/my-ads'>
							<Button variant='outline' size='sm' className=' border-brand'>
								My Ads
							</Button>
						</Link>
						<Link href='/ads/create-ad'>
							<Button className='gap-1 bg-brand hover:bg-brand/80 text-brand-black'>
								<PlusCircle className='h-4 w-4' />
								Post Ad
							</Button>
						</Link>
					</div>
				</MaxWidthWrapper>
			</header>

			<MaxWidthWrapper className='py-6  md:py-8'>
				{/* Category Header */}
				<div className='mb-8'>
					<h1 className='text-3xl font-bold mb-2'>{categoryInfo.name}</h1>
					<p className='text-muted-foreground'>{categoryInfo.description}</p>
				</div>

				{/* Subcategories */}
				{categoryInfo.subcategories && categoryInfo.subcategories.length > 0 && (
					<div className='mb-8'>
						<h2 className='text-lg font-semibold mb-3'>Popular in {categoryInfo.name}</h2>
						<div className='flex flex-wrap gap-2'>
							{categoryInfo.subcategories.map((subcategory: string) => (
								<Link href={`#${subcategory.toLowerCase().replace(/\s+/g, "-")}`} key={subcategory}>
									<Badge
										variant='outline'
										className='px-3 py-1 hover:bg-emerald-50 hover:text-emerald-700 dark:hover:bg-emerald-950 dark:hover:text-emerald-300'
									>
										{subcategory}
									</Badge>
								</Link>
							))}
						</div>
					</div>
				)}

				<div className='flex items-center justify-between mb-6'>
					<h2 className='text-xl font-bold'>All {categoryInfo.name}</h2>
					<div className='flex items-center gap-2'>
						<Select value={sortOption} onValueChange={setSortOption}>
							<SelectTrigger className='w-[180px]'>
								<SelectValue placeholder='Sort by' />
							</SelectTrigger>
							<SelectContent>
								<SelectItem value='newest'>Newest First</SelectItem>
								<SelectItem value='oldest'>Oldest First</SelectItem>
								<SelectItem value='price_low'>Price: Low to High</SelectItem>
								<SelectItem value='price_high'>Price: High to Low</SelectItem>
							</SelectContent>
						</Select>

						{isMobile && (
							<Sheet open={isFilterOpen} onOpenChange={setIsFilterOpen}>
								<SheetTrigger asChild>
									<Button variant='outline' size='icon' className='md:hidden'>
										<SlidersHorizontal className='h-4 w-4' />
									</Button>
								</SheetTrigger>
								<SheetContent side='left' className='w-[300px] sm:w-[400px]'>
									<SheetHeader>
										<SheetTitle>Filters</SheetTitle>
										<SheetDescription>Narrow down your search with these filters</SheetDescription>
									</SheetHeader>
									<div className='py-4'>
										<FilterContent />
									</div>
									<SheetFooter>
										<SheetClose asChild>
											<Button className='w-full bg-emerald-600 hover:bg-emerald-700'>Apply Filters</Button>
										</SheetClose>
									</SheetFooter>
								</SheetContent>
							</Sheet>
						)}
					</div>
				</div>

				<div className='md:hidden mb-4'>
					<div className='relative w-full'>
						<Search className='absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground' />
						<Input
							type='search'
							placeholder={`Search in ${categoryInfo.name}...`}
							className='w-full bg-background pl-8'
							value={searchQuery}
							onChange={(e) => setSearchQuery(e.target.value)}
						/>
					</div>
				</div>

				<div className='flex flex-col md:flex-row gap-6'>
					{/* Filters - Desktop */}
					<div className='hidden md:block w-64 shrink-0'>
						<div className='sticky top-24 border rounded-lg p-4 bg-background'>
							<h2 className='font-semibold text-lg mb-4'>Filters</h2>
							<FilterContent />
						</div>
					</div>

					{/* Active filters */}
					<div className='flex-1'>
						{(selectedBrands.length > 0 ||
							selectedConditions.length > 0 ||
							priceRange[0] > 0 ||
							priceRange[1] < 1000 ||
							datePosted !== "any") && (
							<div className='mb-4 flex flex-wrap gap-2 items-center'>
								<span className='text-sm font-medium'>Active filters:</span>

								{selectedBrands.map((brand) => (
									<Badge key={brand} variant='secondary' className='flex items-center gap-1'>
										{brand}
										<X className='h-3 w-3 cursor-pointer' onClick={() => handleBrandChange(brand)} />
									</Badge>
								))}

								{selectedConditions.map((condition) => (
									<Badge key={condition} variant='secondary' className='flex items-center gap-1'>
										{condition}
										<X className='h-3 w-3 cursor-pointer' onClick={() => handleConditionChange(condition)} />
									</Badge>
								))}

								{(priceRange[0] > 0 || priceRange[1] < 1000) && (
									<Badge variant='secondary' className='flex items-center gap-1'>
										${priceRange[0]} - ${priceRange[1]}
										<X className='h-3 w-3 cursor-pointer' onClick={() => setPriceRange([0, 1000])} />
									</Badge>
								)}

								{datePosted !== "any" && (
									<Badge variant='secondary' className='flex items-center gap-1'>
										{datePosted === "1"
											? "Today"
											: datePosted === "3"
											? "Last 3 days"
											: datePosted === "7"
											? "Last week"
											: "Last month"}
										<X className='h-3 w-3 cursor-pointer' onClick={() => setDatePosted("any")} />
									</Badge>
								)}

								<Button variant='ghost' size='sm' className='h-7 text-xs' onClick={clearAllFilters}>
									Clear all
								</Button>
							</div>
						)}

						{/* Results count */}
						<div className='mb-4'>
							<p className='text-sm text-muted-foreground'>
								Showing {sortedAds.length} {sortedAds.length === 1 ? "result" : "results"} in {categoryInfo.name}
							</p>
						</div>

						{/* Ads grid */}
						{sortedAds.length > 0 ? (
							<div className='grid gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4'>
								{sortedAds.map((ad) => (
									<ProductCard key={ad?.id} product={ad} />
								))}
							</div>
						) : (
							<div className='flex flex-col items-center justify-center py-12 text-center'>
								<div className='rounded-full bg-muted p-6 mb-4'>
									<Search className='h-10 w-10 text-muted-foreground' />
								</div>
								<h3 className='text-xl font-semibold mb-2'>No results found</h3>
								<p className='text-muted-foreground mb-6 max-w-md'>
									We couldn&apos;t find any ads matching your search criteria. Try adjusting your filters or search
									term.
								</p>
								<Button onClick={clearAllFilters}>Clear all filters</Button>
							</div>
						)}

						{/* Pagination - simplified version */}
						{sortedAds.length > 0 && (
							<div className='flex justify-center mt-8'>
								<div className='flex items-center gap-1'>
									<Button variant='outline' size='icon' disabled>
										<ChevronDown className='h-4 w-4 rotate-90' />
									</Button>
									<Button variant='outline' size='sm' className='bg-brand text-brand-black hover:bg-brand/80'>
										1
									</Button>
									<Button variant='outline' size='sm'>
										2
									</Button>
									<Button variant='outline' size='sm'>
										2
									</Button>
									<Button variant='outline' size='sm'>
										3
									</Button>
									<Button variant='outline' size='sm' disabled>
										...
									</Button>
									<Button variant='outline' size='sm'>
										10
									</Button>
									<Button variant='outline' size='icon'>
										<ChevronDown className='h-4 w-4 -rotate-90' />
									</Button>
								</div>
							</div>
						)}
					</div>
				</div>
			</MaxWidthWrapper>
		</div>
	);
}
