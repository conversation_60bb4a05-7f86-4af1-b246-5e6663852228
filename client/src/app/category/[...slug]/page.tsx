import ProductListingPage from "@/components/products/product-listing-page";
import MaxWidthWrapper from "@/components/ui/max-width-wrapper";

const Page = async ({
	params,
	searchParams
}: {
	params: Promise<{ slug: string[] }>;
	searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) => {
	const resolvedParams = await params;
	const resolvedSearchParams = await searchParams;
	return (
		<MaxWidthWrapper>
			<ProductListingPage params={resolvedParams} searchParams={resolvedSearchParams} from='category' />;
		</MaxWidthWrapper>
	);
};

export default Page;
