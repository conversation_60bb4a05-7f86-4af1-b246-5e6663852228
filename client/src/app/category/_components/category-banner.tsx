import Image from "next/image";

interface CategoryBannerProps {
	name: string;
}

export default function CategoryBanner({ name }: CategoryBannerProps) {
	return (
		<div className='relative w-full h-48 md:h-64 rounded-lg overflow-hidden mb-6'>
			<Image src='/category-banner-phones-tablets.jpg' alt={name} fill className='object-cover' priority />
			<div className='absolute inset-0 bg-gradient-to-r from-black/50 to-transparent flex items-center'>
				<h1 className='text-3xl md:text-4xl font-bold text-white ml-8'>{name}</h1>
			</div>
		</div>
	);
}
