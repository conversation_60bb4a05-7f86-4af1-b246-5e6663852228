@tailwind base;
@tailwind components;
@tailwind utilities;

body {
	font-family: var(--font-geist-sans);
}

.font-sans {
	font-family: var(--font-geist-sans);
}

.font-mono {
	font-family: var(--font-geist-mono);
}

@layer base {
	:root {
		--background: 0 0% 100%;
		--foreground: 240 10% 3.9%;
		--card: 0 0% 100%;
		--card-foreground: 240 10% 3.9%;
		--popover: 0 0% 100%;
		--popover-foreground: 240 10% 3.9%;
		--primary: 240 5.9% 10%;
		--primary-foreground: 0 0% 98%;
		--secondary: 240 4.8% 95.9%;
		--secondary-foreground: 240 5.9% 10%;
		--muted: 240 4.8% 95.9%;
		--muted-foreground: 240 3.8% 46.1%;
		--accent: 240 4.8% 95.9%;
		--accent-foreground: 240 5.9% 10%;
		--destructive: 0 84.2% 60.2%;
		--destructive-foreground: 0 0% 98%;
		--border: 240 5.9% 90%;
		--input: 240 5.9% 90%;
		--ring: 240 10% 3.9%;
		--chart-1: 12 76% 61%;
		--chart-2: 173 58% 39%;
		--chart-3: 197 37% 24%;
		--chart-4: 43 74% 66%;
		--chart-5: 27 87% 67%;
		--radius: 0.5rem;
		--sidebar-background: 0 0% 98%;
		--sidebar-foreground: 240 5.3% 26.1%;
		--sidebar-primary: 240 5.9% 10%;
		--sidebar-primary-foreground: 0 0% 98%;
		--sidebar-accent: 240 4.8% 95.9%;
		--sidebar-accent-foreground: 240 5.9% 10%;
		--sidebar-border: 220 13% 91%;
		--sidebar-ring: 217.2 91.2% 59.8%;
	}
	.dark {
		--background: 240 10% 3.9%;
		--foreground: 0 0% 98%;
		--card: 240 10% 3.9%;
		--card-foreground: 0 0% 98%;
		--popover: 240 10% 3.9%;
		--popover-foreground: 0 0% 98%;
		--primary: 0 0% 98%;
		--primary-foreground: 240 5.9% 10%;
		--secondary: 240 3.7% 15.9%;
		--secondary-foreground: 0 0% 98%;
		--muted: 240 3.7% 15.9%;
		--muted-foreground: 240 5% 64.9%;
		--accent: 240 3.7% 15.9%;
		--accent-foreground: 0 0% 98%;
		--destructive: 0 62.8% 30.6%;
		--destructive-foreground: 0 0% 98%;
		--border: 240 3.7% 15.9%;
		--input: 240 3.7% 15.9%;
		--ring: 240 4.9% 83.9%;
		--chart-1: 220 70% 50%;
		--chart-2: 160 60% 45%;
		--chart-3: 30 80% 55%;
		--chart-4: 280 65% 60%;
		--chart-5: 340 75% 55%;
		--sidebar-background: 240 5.9% 10%;
		--sidebar-foreground: 240 4.8% 95.9%;
		--sidebar-primary: 224.3 76.3% 48%;
		--sidebar-primary-foreground: 0 0% 100%;
		--sidebar-accent: 240 3.7% 15.9%;
		--sidebar-accent-foreground: 240 4.8% 95.9%;
		--sidebar-border: 240 3.7% 15.9%;
		--sidebar-ring: 217.2 91.2% 59.8%;
	}
	* {
		@apply border-border;
	}
	body {
		@apply bg-background text-foreground;
	}
}

ul {
	@apply list-inside list-disc;
}

li {
	@apply mb-1;
}

ol {
	@apply list-inside list-decimal;
}

@layer base {
	* {
		@apply border-border outline-ring/50;
	}
	body {
		@apply bg-background text-foreground;
	}
}

.button-brand-small {
	@apply bg-brand hover:bg-brand/90 text-brand-black h-[20px] rounded-[2px] text-[12px] px-[8px];
}

.p-16-black {
	@apply text-brand-black text-[16px] leading-[16px] font-semibold;
}

.p-16-white {
	@apply text-white text-[16px] leading-[16px] font-semibold;
}

.p-15-black {
	@apply text-brand-black text-[15.008px] leading-[22.512px] font-medium;
}
.p-15-white {
	@apply text-white text-[15.008px] leading-[22.512px] font-medium;
}

.p-14-black {
	@apply text-brand-black text-[14px] leading-[21px]  font-normal;
}

.p-14-white {
	@apply text-white text-[14px] leading-[21px]  font-normal;
}

.p-13-black {
	@apply text-brand-black text-[13.008px] leading-[19.512px];
}
.p-13-white {
	@apply text-white text-[13.008px] leading-[19.512px]  font-normal;
}

.p-12-black {
	@apply text-brand-black text-[12px] leading-[18px]  font-normal;
}
.p-12-white {
	@apply text-white text-[12px] leading-[18px]  font-normal;
}

.h1-20-black {
	@apply text-brand-black text-[20px] leading-[30px] font-semibold;
}

.h1-24-black {
	@apply text-brand-black text-[20px] leading-[30px] font-semibold;
}

@keyframes pulse-subtle {
	0%,
	100% {
		transform: scale(1);
	}
	50% {
		transform: scale(1.05);
	}
}

.animate-pulse-subtle {
	animation: pulse-subtle 2s infinite;
}

/* Phone input styling */
.phone-input-wrapper {
	position: relative;
}

.phone-input-wrapper .flex {
	display: flex;
	align-items: stretch;
	height: 40px; /* Match input height */
}

.phone-prefix {
	display: flex;
	align-items: center;
	padding: 0 10px;
	background-color: hsl(var(--muted));
	border: 1px solid hsl(var(--input));
	border-right: none;
	border-top-left-radius: 0.375rem;
	border-bottom-left-radius: 0.375rem;
	font-size: 0.875rem;
	color: hsl(var(--foreground));
	height: 100%;
}

.phone-input {
	border-top-left-radius: 0 !important;
	border-bottom-left-radius: 0 !important;
	flex: 1;
}

/* Focus state styling */
.phone-input:focus-visible {
	outline: none;
	box-shadow: none;
	border-color: hsl(var(--ring));
}

.phone-input:focus-visible + .phone-prefix,
.phone-input:focus-visible {
	border-color: hsl(var(--ring));
	outline: 2px solid transparent;
	outline-offset: 2px;
	box-shadow: 0 0 0 2px hsl(var(--background)), 0 0 0 4px hsl(var(--ring));
}

/* Ensure consistent height */
.phone-input,
.phone-prefix {
	height: 40px;
}
