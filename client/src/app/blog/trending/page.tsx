import { ChevronR<PERSON>, Star } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

export default function TrendingPage() {
	// This would normally fetch data from an API or database
	const trendingDevices = [
		{
			id: 1,
			name: "iPhone 15 Pro",
			image: "/placeholder.svg?height=200&width=200&query=iphone 15 pro",
			rating: 4.7,
			price: "$999",
			category: "Smartphones"
		},
		{
			id: 2,
			name: "Sony WH-1000XM5",
			image: "/placeholder.svg?height=200&width=200&query=sony headphones",
			rating: 4.8,
			price: "$349",
			category: "Headphones"
		},
		{
			id: 3,
			name: "MacBook Pro M3",
			image: "/placeholder.svg?height=200&width=200&query=macbook pro",
			rating: 4.9,
			price: "$1,599",
			category: "Laptops"
		},
		{
			id: 4,
			name: "Samsung S95C OLED",
			image: "/placeholder.svg?height=200&width=200&query=samsung oled tv",
			rating: 4.6,
			price: "$1,899",
			category: "TVs"
		},
		{
			id: 5,
			name: "Canon EOS R6 Mark II",
			image: "/placeholder.svg?height=200&width=200&query=canon camera",
			rating: 4.5,
			price: "$2,499",
			category: "Cameras"
		},
		{
			id: 6,
			name: "Steam Deck OLED",
			image: "/placeholder.svg?height=200&width=200&query=steam deck",
			rating: 4.7,
			price: "$549",
			category: "Gaming"
		},
		{
			id: 7,
			name: "iPad Pro M2",
			image: "/placeholder.svg?height=200&width=200&query=ipad pro",
			rating: 4.6,
			price: "$799",
			category: "Tablets"
		},
		{
			id: 8,
			name: "Sonos Era 300",
			image: "/placeholder.svg?height=200&width=200&query=sonos speaker",
			rating: 4.4,
			price: "$449",
			category: "Speakers"
		},
		{
			id: 9,
			name: "DJI Air 3",
			image: "/placeholder.svg?height=200&width=200&query=dji drone",
			rating: 4.5,
			price: "$899",
			category: "Drones"
		},
		{
			id: 10,
			name: "Apple Watch Ultra 2",
			image: "/placeholder.svg?height=200&width=200&query=apple watch ultra",
			rating: 4.7,
			price: "$799",
			category: "Smartwatches"
		},
		{
			id: 11,
			name: "Bose QuietComfort Ultra",
			image: "/placeholder.svg?height=200&width=200&query=bose headphones",
			rating: 4.6,
			price: "$429",
			category: "Headphones"
		},
		{
			id: 12,
			name: "NVIDIA RTX 4080",
			image: "/placeholder.svg?height=200&width=200&query=nvidia graphics card",
			rating: 4.8,
			price: "$1,199",
			category: "PC Components"
		},
		{
			id: 13,
			name: "Samsung Galaxy S23 Ultra",
			image: "/placeholder.svg?height=200&width=200&query=samsung galaxy",
			rating: 4.5,
			price: "$1,199",
			category: "Smartphones"
		},
		{
			id: 14,
			name: "LG C3 OLED",
			image: "/placeholder.svg?height=200&width=200&query=lg oled tv",
			rating: 4.7,
			price: "$1,499",
			category: "TVs"
		},
		{
			id: 15,
			name: "Sony A7 IV",
			image: "/placeholder.svg?height=200&width=200&query=sony camera",
			rating: 4.6,
			price: "$2,399",
			category: "Cameras"
		},
		{
			id: 16,
			name: "Dell XPS 13",
			image: "/placeholder.svg?height=200&width=200&query=dell laptop",
			rating: 4.4,
			price: "$999",
			category: "Laptops"
		},
		{
			id: 17,
			name: "AirPods Pro 2",
			image: "/placeholder.svg?height=200&width=200&query=airpods pro",
			rating: 4.5,
			price: "$249",
			category: "Earbuds"
		},
		{
			id: 18,
			name: "PlayStation 5 Slim",
			image: "/placeholder.svg?height=200&width=200&query=playstation 5",
			rating: 4.7,
			price: "$499",
			category: "Gaming"
		},
		{
			id: 19,
			name: "GoPro Hero 12",
			image: "/placeholder.svg?height=200&width=200&query=gopro camera",
			rating: 4.3,
			price: "$399",
			category: "Action Cameras"
		},
		{
			id: 20,
			name: "Samsung Galaxy Tab S9",
			image: "/placeholder.svg?height=200&width=200&query=samsung tablet",
			rating: 4.4,
			price: "$799",
			category: "Tablets"
		},
		{
			id: 21,
			name: "Logitech MX Master 3S",
			image: "/placeholder.svg?height=200&width=200&query=logitech mouse",
			rating: 4.8,
			price: "$99",
			category: "Peripherals"
		},
		{
			id: 22,
			name: "Dyson V15 Detect",
			image: "/placeholder.svg?height=200&width=200&query=dyson vacuum",
			rating: 4.6,
			price: "$749",
			category: "Home Tech"
		},
		{
			id: 23,
			name: "Garmin Fenix 7",
			image: "/placeholder.svg?height=200&width=200&query=garmin watch",
			rating: 4.7,
			price: "$699",
			category: "Smartwatches"
		},
		{
			id: 24,
			name: "Keychron Q1 Pro",
			image: "/placeholder.svg?height=200&width=200&query=mechanical keyboard",
			rating: 4.5,
			price: "$199",
			category: "Peripherals"
		}
	];

	return (
		<div className='container mx-auto px-4 py-8'>
			<div className='mb-6'>
				<div className='flex items-center text-sm text-muted-foreground mb-4'>
					<Link href='/blog' className='hover:text-brand-dark-300'>
						Home
					</Link>
					<ChevronRight className='h-4 w-4 mx-2' />
					<span>Trending Devices</span>
				</div>
				<h1 className='text-3xl font-bold'>Trending Devices</h1>
				<p className='text-muted-foreground mt-2'>The most popular devices based on user interest and engagement</p>
			</div>

			<div className='grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4 md:gap-6'>
				{trendingDevices.map((device) => (
					<Link href={`/device-${device.id}`} key={device.id} className='group'>
						<div className='border rounded-lg p-4 transition-all hover:shadow-md group-hover:border-brand'>
							<div className='relative'>
								<div className='absolute top-0 left-0 bg-brand text-brand-black text-xs px-2 py-1 rounded-br-lg'>
									{device.category}
								</div>
							</div>
							<div className='aspect-square relative mb-3 pt-6'>
								<Image src={device.image || "/placeholder.svg"} alt={device.name} fill className='object-contain' />
							</div>
							<h3 className='text-sm font-medium text-center group-hover:text-brand-dark-300 transition-colors'>
								{device.name}
							</h3>
							<div className='flex items-center justify-center mt-2'>
								<div className='flex'>
									{[...Array(5)].map((_, i) => (
										<Star
											key={i}
											className={`h-3 w-3 ${
												i < Math.floor(Number(device.rating))
													? "fill-yellow-400 text-yellow-400"
													: "fill-muted text-muted-foreground"
											}`}
										/>
									))}
								</div>
								<span className='text-xs ml-1'>{device.rating}</span>
							</div>
							<div className='text-center mt-2 font-semibold text-brand-dark-300'>{device.price}</div>
						</div>
					</Link>
				))}
			</div>
		</div>
	);
}
