import { <PERSON>, <PERSON><PERSON>, <PERSON> } from "lucide-react";
import Link from "next/link";
import type React from "react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";

import MaxWidthWrapper from "@/components/ui/max-width-wrapper";
import { Suspense } from "react";

export const metadata = {
	title: "TechReview - Latest Smartphone Reviews and News",
	description: "Your source for the latest smartphone reviews, news, and comparisons",
	generator: "v0.dev"
};

export default function RootLayout({
	children
}: Readonly<{
	children: React.ReactNode;
}>) {
	return (
		<div>
			<header className='border-b '>
				<MaxWidthWrapper className=' py-3 flex items-center justify-between'>
					<div className='flex items-center'>
						<Sheet>
							<SheetTrigger asChild>
								<Button variant='ghost' size='icon' className='md:hidden'>
									<Menu className='h-5 w-5' />
									<span className='sr-only'>Toggle menu</span>
								</Button>
							</SheetTrigger>
							<SheetContent side='left' className='w-[300px] sm:w-[400px]'>
								<nav className='flex flex-col gap-4 mt-8'>
									<Link href='/blog' className='text-lg font-semibold px-2'>
										Home
									</Link>
									<Link href='/blog/reviews' className='text-lg font-semibold px-2'>
										Reviews
									</Link>
								</nav>
							</SheetContent>
						</Sheet>
						<Link href='/' className='flex items-center'>
							<span className='text-xl font-bold text-brand-dark-300 mr-1'>Tech</span>
							<span className='text-xl font-bold'>Review</span>
						</Link>
						<nav className='hidden md:flex items-center ml-8 space-x-6'>
							<Link href='/blog' className='text-sm font-medium hover:text-brand-dark-300 transition-colors'>
								Home
							</Link>
							<Link href='/blog/reviews' className='text-sm font-medium hover:text-brand-dark-300 transition-colors'>
								Reviews
							</Link>
						</nav>
					</div>
					<div className='flex items-center space-x-2'>
						<div className='hidden md:flex relative'>
							<Search className='absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground' />
							<Input type='search' placeholder='Search...' className='w-[200px] pl-8 rounded-full bg-muted' />
						</div>
						<Button variant='ghost' size='icon' className='hidden md:flex'>
							<Bell className='h-5 w-5' />
							<span className='sr-only'>Notifications</span>
						</Button>

						<Button variant='ghost' size='icon' className='md:hidden'>
							<Search className='h-5 w-5' />
							<span className='sr-only'>Search</span>
						</Button>
					</div>
				</MaxWidthWrapper>
			</header>
			<Suspense>
				<main>
					<MaxWidthWrapper>{children}</MaxWidthWrapper>
				</main>
			</Suspense>
		</div>
	);
}
