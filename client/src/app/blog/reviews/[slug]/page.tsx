import { Bookmark, Check, ChevronRight, Share2, Star, ThumbsDown, ThumbsUp, X } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Separator } from "@/components/ui/separator";

// Demo data for different products
const reviewData = {
	"sony-wh-1000xm5": {
		title: "Sony WH-1000XM5",
		subtitle: "The New King of Noise Cancellation",
		rating: 4.8,
		date: "May 5, 2025",
		author: "<PERSON>",
		authorImage: "/placeholder.svg?key=vomsv",
		heroImage: "/placeholder.svg?key=nftf8",
		price: "$349",
		category: "Headphones",
		pros: [
			"Best-in-class noise cancellation",
			"Exceptional sound quality",
			"Comfortable for all-day wear",
			"Improved call quality with 8 microphones",
			"30-hour battery life"
		],
		cons: [
			"More expensive than previous model",
			"No longer foldable for travel",
			"Touch controls can be finicky",
			"Limited color options"
		],
		specs: {
			driver: "30mm Carbon Fiber Composite",
			battery: "30 hours (ANC on), 40 hours (ANC off)",
			connectivity: "Bluetooth 5.2, 3.5mm, USB-C",
			weight: "250g",
			colors: "Black, Silver",
			features: "LDAC, DSEE Extreme, 360 Reality Audio"
		},
		content: `
      <h2>Introduction</h2>
      <p>Sony has been the leader in noise-cancelling headphones for years, and with the WH-1000XM5, they've completely redesigned their flagship to push the boundaries even further. With a sleeker look, improved noise cancellation, and better sound quality, Sony is clearly not resting on its laurels.</p>
      
      <h2>Design and Comfort</h2>
      <p>The WH-1000XM5 features a completely new design compared to its predecessor. Gone is the foldable design, replaced with a sleeker, more minimalist aesthetic that looks more premium. The headband is thinner with a step-less slider, and the ear cups have a more refined shape.</p>
      
      <p>Comfort is exceptional with these headphones. The ear cups use a softer synthetic leather with memory foam that creates a better seal while feeling less fatiguing during long listening sessions. At just 250 grams, they're also slightly lighter than the previous model, which helps for all-day wear.</p>
      
      <h2>Noise Cancellation</h2>
      <p>Sony has improved their already industry-leading noise cancellation with a new system that uses 8 microphones and two processors to analyze and filter out ambient noise. The results are impressive - these headphones can almost completely eliminate low-frequency rumble from planes and trains, while doing a better job with mid-range frequencies like voices than any competitor we've tested.</p>
      
      <h2>Sound Quality</h2>
      <p>The WH-1000XM5 features new 30mm carbon fiber composite drivers that deliver a more refined sound than their predecessors. The bass is tight and controlled rather than boomy, the midrange is detailed and natural, and the treble extends nicely without ever becoming harsh.</p>
      
      <p>Sony's DSEE Extreme upscaling technology helps restore detail to compressed music, and the headphones support LDAC for high-resolution wireless audio when paired with compatible devices. The default tuning is slightly warm but pleasantly balanced, and you can customize the EQ extensively through Sony's Headphones Connect app.</p>`,
		relatedProducts: [
			{
				name: "Bose QuietComfort Ultra",
				image: "/placeholder.svg?key=6n9pa",
				category: "Headphones",
				slug: "bose-quietcomfort-ultra"
			},
			{
				name: "Apple AirPods Max",
				image: "/placeholder.svg?key=3tiis",
				category: "Headphones",
				slug: "apple-airpods-max"
			},
			{
				name: "Sennheiser Momentum 4",
				image: "/placeholder.svg?key=orn2y",
				category: "Headphones",
				slug: "sennheiser-momentum-4"
			}
		]
	},
	"macbook-pro-m3-max": {
		title: "MacBook Pro M3 Max",
		subtitle: "The Ultimate Creator's Machine",
		rating: 4.9,
		date: "May 1, 2025",
		author: "Sarah Johnson",
		authorImage: "/placeholder.svg?key=vxy9s",
		heroImage: "/mac-book.webp",
		price: "$3,499",
		category: "Laptops",
		pros: [
			"Incredible performance for creative tasks",
			"Exceptional battery life despite the power",
			"Beautiful Mini-LED display",
			"Excellent keyboard and trackpad",
			"Comprehensive port selection"
		],
		cons: [
			"Very expensive",
			"RAM not user-upgradeable",
			"Notch in the display may bother some users",
			"No Face ID despite the notch"
		],
		specs: {
			processor: "Apple M3 Max (16-core CPU, 40-core GPU)",
			memory: "Up to 128GB unified memory",
			storage: "Up to 8TB SSD",
			display: "16.2-inch Liquid Retina XDR (3456 x 2234)",
			battery: "Up to 22 hours",
			ports: "3x Thunderbolt 4, HDMI, SD card, MagSafe, 3.5mm"
		},
		content: `
      <h2>Introduction</h2>
      <p>The MacBook Pro with M3 Max represents Apple's most powerful laptop to date, designed specifically for creative professionals who demand the absolute best performance. With a completely redesigned architecture that builds on the already impressive M1 Max, this laptop sets new standards for what's possible in a portable workstation.</p>
      
      <h2>Design and Build</h2>
      <p>The design remains largely unchanged from the 2021 redesign, which is a good thing. The all-aluminum chassis feels incredibly solid, with zero flex anywhere in the body or lid. At 4.7 pounds, it's not the lightest 16-inch laptop, but considering the power inside, it's remarkably portable.</p>
      
      <p>The keyboard continues to be excellent, with good key travel and a satisfying typing experience. The massive Force Touch trackpad remains the best in the business, with perfect palm rejection and precise tracking.</p>
      
      <h2>Display</h2>
      <p>The 16.2-inch Liquid Retina XDR display is simply stunning. With Mini-LED technology providing 1,000 nits of sustained brightness (1,600 nits peak for HDR content), it's bright enough to use outdoors and delivers incredible contrast that rivals OLED screens. The 120Hz ProMotion refresh rate makes everything from scrolling to video editing feel smooth and responsive.</p>
      
      <p>The notch at the top of the screen still feels like an odd design choice, especially since it doesn't house Face ID sensors, but you quickly get used to it in daily use.</p>
      
      <h2>Performance</h2>
      <p>Performance is where the M3 Max truly shines. In our testing, it handled everything from 8K video editing to complex 3D rendering with ease. The 16-core CPU and 40-core GPU combination delivered performance that often exceeded dedicated workstations, all while maintaining remarkable efficiency.</p>
      
      <p>For video editors, we saw 8K ProRes timelines play back without dropped frames in Final Cut Pro, and export times were up to 40% faster than the already impressive M1 Max. For 3D artists, Blender renders completed in nearly half the time compared to high-end Windows workstations with dedicated GPUs.</p>`,
		relatedProducts: [
			{
				name: "Dell XPS 17",
				image: "/placeholder.svg?key=v85s2",
				category: "Laptops",
				slug: "dell-xps-17"
			},
			{
				name: "ASUS ProArt StudioBook 16",
				image: "/placeholder.svg?key=p9xqb",
				category: "Laptops",
				slug: "asus-proart-studiobook-16"
			},
			{
				name: "Razer Blade 16",
				image: "/placeholder.svg?key=ds34l",
				category: "Laptops",
				slug: "razer-blade-16"
			}
		]
	},
	"samsung-qn90c-qled": {
		title: "Samsung QN90C QLED TV",
		subtitle: "Mini-LED Excellence for Bright Rooms",
		rating: 4.6,
		date: "April 28, 2025",
		author: "Michael Chen",
		authorImage: "/placeholder.svg?key=0x7mm",
		heroImage: "/placeholder.svg?key=ih8bb",
		price: "$1,799",
		category: "TVs",
		pros: [
			"Exceptional brightness for bright rooms",
			"Excellent anti-glare screen",
			"Great gaming features",
			"Impressive local dimming performance",
			"Sleek, thin design"
		],
		cons: [
			"Not quite as good black levels as OLED",
			"Some blooming around bright objects",
			"Expensive compared to budget options",
			"Tizen OS can be sluggish at times"
		],
		specs: {
			display: '65" Neo QLED 4K (3840 x 2160)',
			processor: "Neo Quantum Processor 4K",
			dimming: "Mini-LED with local dimming",
			refresh: "120Hz native refresh rate",
			hdr: "HDR10+, HLG",
			ports: "4x HDMI 2.1, 2x USB, Ethernet, optical"
		},
		content: `
      <h2>Introduction</h2>
      <p>Samsung's QN90C represents the latest evolution of their Neo QLED technology, combining Mini-LED backlighting with their quantum dot color technology. The result is a TV that delivers stunning brightness and color volume that makes it particularly well-suited for bright living rooms where traditional OLED TVs might struggle.</p>
      
      <h2>Design</h2>
      <p>The QN90C features Samsung's "Infinity One" design with an incredibly thin profile for an LED-backlit TV. The bezels are minimal, creating an almost edge-to-edge picture experience. The included center stand is sturdy and doesn't require a particularly wide media console, though wall-mounting with the optional slim-fit bracket creates an even more impressive aesthetic.</p>
      
      <p>The back of the TV includes cable management channels to keep your setup clean, and the One Connect box has been integrated into the TV itself rather than being a separate unit as on some previous premium Samsung models.</p>
      
      <h2>Picture Quality</h2>
      <p>The QN90C's picture quality is exceptional, particularly in bright environments. With peak brightness exceeding 2,000 nits in our measurements, it can overcome even direct sunlight while maintaining vibrant colors thanks to the quantum dot layer. The Mini-LED backlighting system provides excellent local dimming with minimal blooming around bright objects on dark backgrounds.</p>
      
      <p>Color accuracy out of the box is very good, though we recommend using the Filmmaker Mode for the most accurate representation of content. The wide color gamut covers nearly 100% of the DCI-P3 color space, making HDR content look particularly impressive.</p>
      
      <h2>Gaming Features</h2>
      <p>Gamers will appreciate the comprehensive gaming features, including four HDMI 2.1 ports that support 4K 120Hz, Variable Refresh Rate (VRR), Auto Low Latency Mode (ALLM), and FreeSync Premium Pro. Input lag in Game Mode is exceptionally low at around 10ms, making this TV an excellent choice for competitive gaming.</p>`,
		relatedProducts: [
			{
				name: "LG C3 OLED",
				image: "/placeholder.svg?height=64&width=64&query=lg oled tv",
				category: "TVs",
				slug: "lg-c3-oled"
			},
			{
				name: "Sony A95L QD-OLED",
				image: "/placeholder.svg?height=64&width=64&query=sony tv",
				category: "TVs",
				slug: "sony-a95l-qd-oled"
			},
			{
				name: "TCL 6-Series Mini-LED",
				image: "/placeholder.svg?height=64&width=64&query=tcl tv",
				category: "TVs",
				slug: "tcl-6-series"
			}
		]
	},
	"canon-eos-r6-mark-ii": {
		title: "Canon EOS R6 Mark II",
		subtitle: "The Perfect Hybrid Shooter",
		rating: 4.7,
		date: "April 15, 2025",
		author: "Jessica Lee",
		authorImage: "/placeholder.svg?height=50&width=50&query=female photographer",
		heroImage: "/placeholder.svg?height=600&width=1200&query=canon camera",
		price: "$2,499",
		category: "Cameras",
		pros: [
			"Excellent autofocus with reliable subject tracking",
			"Impressive 40fps electronic shutter",
			"No recording time limits for video",
			"Improved battery life over predecessor",
			"Effective in-body image stabilization"
		],
		cons: [
			"24MP resolution may be limiting for some",
			"Rolling shutter can be noticeable in electronic shutter mode",
			"Menu system can be complex",
			"Expensive compared to some competitors"
		],
		specs: {
			sensor: "24.2MP Full-Frame CMOS",
			processor: "DIGIC X",
			autofocus: "Dual Pixel CMOS AF II, 1,053 AF points",
			burst: "Up to 40fps (electronic), 12fps (mechanical)",
			video: "6K RAW external, 4K60p internal",
			stabilization: "5-axis IBIS, up to 8 stops"
		},
		content: `
      <h2>Introduction</h2>
      <p>The Canon EOS R6 Mark II builds upon the success of its predecessor with meaningful improvements to autofocus, burst shooting, and video capabilities. It's positioned as Canon's versatile workhorse for photographers and videographers who need reliable performance across a wide range of scenarios.</p>
      
      <h2>Design and Handling</h2>
      <p>The R6 Mark II maintains a similar design to the original R6, with a comfortable grip and well-thought-out control layout. The body is weather-sealed against dust and moisture, making it suitable for professional use in challenging conditions. At 680g with the battery and card, it strikes a good balance between robustness and portability.</p>
      
      <p>The fully articulating touchscreen is bright and responsive, making it easy to shoot from creative angles or when filming yourself. The electronic viewfinder offers a clear 3.69-million dot resolution with a comfortable magnification of 0.76x.</p>
      
      <h2>Image Quality</h2>
      <p>The new 24.2MP sensor delivers excellent image quality with improved dynamic range over its predecessor. While some competitors offer higher resolution, the R6 Mark II prioritizes low-light performance and speed. Images show excellent color rendition that's characteristic of Canon, with pleasing skin tones straight out of camera.</p>
      
      <p>High ISO performance is impressive, with usable results up to ISO 12,800 and even ISO 25,600 in a pinch. The in-body image stabilization system works effectively to allow handheld shooting at slow shutter speeds, which further enhances its low-light capabilities.</p>
      
      <h2>Autofocus</h2>
      <p>Autofocus is where the R6 Mark II truly shines. The updated Dual Pixel CMOS AF II system now offers improved subject detection and tracking for people, animals, vehicles, and even specific types of animals like birds. In our testing, the eye-detection autofocus was remarkably tenacious, maintaining focus on subjects even when they turned away momentarily.</p>`,
		relatedProducts: [
			{
				name: "Sony A7 IV",
				image: "/placeholder.svg?key=5uvqy",
				category: "Cameras",
				slug: "sony-a7-iv"
			},
			{
				name: "Nikon Z6 II",
				image: "/placeholder.svg?height=64&width=64&query=nikon camera",
				category: "Cameras",
				slug: "nikon-z6-ii"
			},
			{
				name: "Fujifilm X-T5",
				image: "/placeholder.svg?height=64&width=64&query=fujifilm camera",
				category: "Cameras",
				slug: "fujifilm-x-t5"
			}
		]
	}
};

export default async function ReviewPage({ params }: { params: Promise<{ slug: string }> }) {
	// Await the params object to get the actual values
	const resolvedParams = await params;

	// Now you can use resolvedParams.slug safely
	const review = reviewData[resolvedParams.slug as keyof typeof reviewData] || reviewData["sony-wh-1000xm5"];

	return (
		<div className='container mx-auto px-4 py-8'>
			<div className='mb-6'>
				<div className='flex items-center text-sm text-muted-foreground mb-4'>
					<Link href='/blog' className='hover:text-brand-dark-300'>
						Home
					</Link>
					<ChevronRight className='h-4 w-4 mx-2' />
					<Link href='/blog/reviews' className='hover:text-brand-dark-300'>
						Reviews
					</Link>
					<ChevronRight className='h-4 w-4 mx-2' />
					<Link href={`/category/${review.category.toLowerCase()}`} className='hover:text-brand-dark-300'>
						{review.category}
					</Link>
					<ChevronRight className='h-4 w-4 mx-2' />
					<span>{review.title}</span>
				</div>

				<div className='flex flex-col md:flex-row md:items-center justify-between gap-4 mb-6'>
					<div>
						<Badge variant='outline' className='mb-2'>
							{review.category}
						</Badge>
						<h1 className='text-3xl md:text-4xl font-bold'>{review.title}</h1>
						<p className='text-xl text-muted-foreground mt-1'>{review.subtitle}</p>
					</div>
					<div className='flex items-center gap-3'>
						<Button variant='outline' size='sm'>
							<Share2 className='h-4 w-4 mr-2' />
							Share
						</Button>
						<Button variant='outline' size='sm'>
							<Bookmark className='h-4 w-4 mr-2' />
							Save
						</Button>
					</div>
				</div>
			</div>

			{/* Hero Image and Rating */}
			<div className='mb-8'>
				<div className='relative rounded-xl overflow-hidden mb-6'>
					<Image
						src={review.heroImage || "/mac-book.webp"}
						alt={review.title}
						width={1200}
						height={600}
						className='w-full aspect-[21/9] object-cover' // Changed aspect ratio to make image less tall
					/>
				</div>

				<div className='flex flex-col md:flex-row md:items-center justify-between gap-4 bg-muted/30 p-4 rounded-lg'>
					<div className='flex items-center'>
						<Avatar className='h-10 w-10 mr-3'>
							<AvatarImage src={review.authorImage || "/placeholder.svg"} alt={review.author} />
							<AvatarFallback>JS</AvatarFallback>
						</Avatar>
						<div>
							<div className='font-medium'>{review.author}</div>
							<div className='text-sm text-muted-foreground'>{review.date}</div>
						</div>
					</div>
					<div className='flex items-center gap-4'>
						<div className='flex items-center'>
							<div className='flex mr-2'>
								{[...Array(5)].map((_, i) => (
									<Star
										key={i}
										className={`h-5 w-5 ${
											i < Math.floor(review.rating)
												? "fill-yellow-400 text-yellow-400"
												: "fill-muted text-muted-foreground"
										}`}
									/>
								))}
							</div>
							<span className='font-medium'>{review.rating.toFixed(1)}/5.0</span>
						</div>
						<div className='text-xl font-bold text-brand-dark-300'>{review.price}</div>
					</div>
				</div>
			</div>

			{/* Key Specifications - Redesigned */}
			<div className='mb-8'>
				<h3 className='font-semibold text-xl mb-6'>Key Specifications</h3>
				<div className='grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4'>
					{Object.entries(review.specs).map(([key, value]) => (
						<div key={key} className='bg-muted/30 rounded-lg p-4 hover:bg-muted/50 transition-colors'>
							<div className='text-muted-foreground text-sm uppercase tracking-wider mb-1 font-medium'>{key}</div>
							<div className='font-semibold text-foreground'>{value}</div>
						</div>
					))}
				</div>
				{/* <div className="mt-6 flex justify-center">
          <Button className="bg-red-600 hover:bg-red-700 px-8">Check Best Price</Button>
        </div> */}
			</div>

			<div className='grid grid-cols-1 lg:grid-cols-3 gap-8'>
				{/* Main Content */}
				<div className='lg:col-span-2'>
					<div
						className='prose prose-headings:font-bold prose-headings:text-2xl prose-h2:text-xl prose-h2:mt-8 prose-h2:mb-4 prose-p:text-base prose-p:leading-relaxed prose-p:mb-4 max-w-none'
						dangerouslySetInnerHTML={{ __html: review.content }}
					/>

					<div className='grid grid-cols-1 md:grid-cols-2 gap-6 my-8'>
						<div className='border border-green-200 bg-green-50 rounded-lg p-4'>
							<h3 className='font-semibold text-green-700 mb-3'>Pros</h3>
							<ul className='space-y-2'>
								{review.pros.map((pro, index) => (
									<li key={index} className='flex items-start'>
										<Check className='h-5 w-5 text-green-600 mr-2 flex-shrink-0 mt-0.5' />
										<span>{pro}</span>
									</li>
								))}
							</ul>
						</div>
						<div className='border border-red-200 bg-red-50 rounded-lg p-4'>
							<h3 className='font-semibold text-brand-dark-300 mb-3'>Cons</h3>
							<ul className='space-y-2'>
								{review.cons.map((con, index) => (
									<li key={index} className='flex items-start'>
										<X className='h-5 w-5 text-brand-dark-300 mr-2 flex-shrink-0 mt-0.5' />
										<span>{con}</span>
									</li>
								))}
							</ul>
						</div>
					</div>

					<div className='border rounded-lg p-6 mb-8'>
						<h3 className='font-semibold text-xl mb-4'>Final Verdict</h3>
						<p className='mb-4'>
							The {review.title} {review.category === "Headphones" || review.category === "TVs" ? "" : "is"} an
							excellent choice for anyone looking for a premium {review.category.toLowerCase().slice(0, -1)} with
							outstanding performance. Despite a few minor drawbacks, it delivers exceptional value for the price and
							earns our strong recommendation.
						</p>
						<div className='flex items-center justify-between'>
							<div className='flex items-center'>
								<div className='flex mr-2'>
									{[...Array(5)].map((_, i) => (
										<Star
											key={i}
											className={`h-6 w-6 ${
												i < Math.floor(review.rating)
													? "fill-yellow-400 text-yellow-400"
													: "fill-muted text-muted-foreground"
											}`}
										/>
									))}
								</div>
								<span className='font-bold text-xl'>{review.rating.toFixed(1)}/5.0</span>
							</div>
							<Button className='bg-brand hover:bg-brand-dark-200 text-brand-black'>Check Best Price</Button>
						</div>
					</div>

					<Separator className='my-8' />

					<div>
						<h3 className='text-xl font-bold mb-6'>Comments (24)</h3>
						<div className='space-y-6'>
							{[1, 2, 3].map((i) => (
								<div key={i} className='border rounded-lg p-4'>
									<div className='flex items-start'>
										<Avatar className='h-10 w-10 mr-3'>
											<AvatarImage src={`/placeholder.svg?height=40&width=40&query=person ${i}`} alt={`User ${i}`} />
											<AvatarFallback>U{i}</AvatarFallback>
										</Avatar>
										<div className='flex-1'>
											<div className='flex items-center justify-between'>
												<div className='font-medium'>User{i}</div>
												<div className='text-sm text-muted-foreground'>
													{i} day{i > 1 ? "s" : ""} ago
												</div>
											</div>
											<p className='mt-2 text-sm'>
												{i === 1
													? `I've been using this ${review.category
															.toLowerCase()
															.slice(
																0,
																-1
															)} for a month now and I'm really impressed with the performance. Definitely worth the price!`
													: i === 2
													? `Great review! I was on the fence about buying this, but your detailed analysis convinced me to go for it.`
													: `I agree with most points, but I think the ${review.cons[0].toLowerCase()} is a bigger issue than you mentioned. Otherwise, spot on review!`}
											</p>
											<div className='flex items-center mt-3 space-x-4'>
												<button className='flex items-center text-sm text-muted-foreground hover:text-foreground'>
													<ThumbsUp className='h-4 w-4 mr-1' />
													{i * 5}
												</button>
												<button className='flex items-center text-sm text-muted-foreground hover:text-foreground'>
													<ThumbsDown className='h-4 w-4 mr-1' />
													{i}
												</button>
											</div>
										</div>
									</div>
								</div>
							))}
							<Button variant='outline' className='w-full'>
								Load More Comments
							</Button>
						</div>
					</div>
				</div>

				{/* Sidebar */}
				<div className='lg:col-span-1'>
					<div className='lg:sticky lg:top-24 space-y-6'>
						<div className='border rounded-xl p-6 bg-background'>
							<h3 className='font-semibold text-lg mb-4'>Related Reviews</h3>
							<div className='space-y-4'>
								{review.relatedProducts.map((item, i) => (
									<Link href={`/reviews/${item.slug}`} key={i} className='flex items-center gap-3 group'>
										<div className='w-16 h-16 relative flex-shrink-0'>
											<Image
												src={item.image || "/placeholder.svg"}
												alt={item.name}
												fill
												className='object-cover rounded-md'
											/>
										</div>
										<div>
											<h4 className='font-medium group-hover:text-brand-dark-300 transition-colors'>{item.name}</h4>
											<div className='flex items-center mt-1'>
												<div className='flex'>
													{[...Array(5)].map((_, j) => (
														<Star
															key={j}
															className={`h-3 w-3 ${
																j < 4 ? "fill-yellow-400 text-yellow-400" : "fill-muted text-muted-foreground"
															}`}
														/>
													))}
												</div>
												<span className='text-xs ml-1'>4.0</span>
											</div>
											<span className='text-xs text-muted-foreground'>{item.category}</span>
										</div>
									</Link>
								))}
							</div>
						</div>

						<div className='border rounded-xl p-6 bg-background'>
							<h3 className='font-semibold text-lg mb-4'>Latest News</h3>
							<div className='space-y-4'>
								{[
									`New ${review.title} model rumored for next year`,
									`How the ${review.title} compares to competitors`,
									`Top accessories for your ${review.title}`
								].map((title, i) => (
									<Link href='#' key={i} className='block group'>
										<h4 className='font-medium group-hover:text-brand-dark-300 transition-colors line-clamp-2'>
											{title}
										</h4>
										<p className='text-sm text-muted-foreground mt-1'>
											{i} day{i > 1 ? "s" : ""} ago
										</p>
									</Link>
								))}
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
	);
}
