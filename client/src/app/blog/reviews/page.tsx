import { <PERSON><PERSON><PERSON><PERSON><PERSON>, Filter, <PERSON> } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";

export default function ReviewsPage() {
	// Demo data for all reviews
	const reviews = [
		{
			id: 1,
			title: "Sony WH-1000XM5",
			image: "/modern-smartphone.png",
			rating: 4.8,
			price: "$349",
			category: "Headphones",
			slug: "sony-wh-1000xm5",
			date: "May 5, 2025"
		},
		{
			id: 2,
			title: "MacBook Pro M3 Max",
			image: "/modern-smartphone.png",
			rating: 4.9,
			price: "$3,499",
			category: "Laptops",
			slug: "macbook-pro-m3-max",
			date: "May 1, 2025"
		},
		{
			id: 3,
			title: "Samsung QN90C QLED TV",
			image: "/modern-smartphone.png",
			rating: 4.6,
			price: "$1,799",
			category: "TVs",
			slug: "samsung-qn90c-qled",
			date: "April 28, 2025"
		},
		{
			id: 4,
			title: "Canon EOS R6 Mark II",
			image: "/modern-smartphone.png",
			rating: 4.7,
			price: "$2,499",
			category: "Cameras",
			slug: "canon-eos-r6-mark-ii",
			date: "April 15, 2025"
		},
		{
			id: 5,
			title: "LG C3 OLED TV",
			image: "/modern-smartphone.png",
			rating: 4.7,
			price: "$1,499",
			category: "TVs",
			slug: "lg-c3-oled",
			date: "April 10, 2025"
		},
		{
			id: 6,
			title: "Steam Deck OLED",
			image: "/modern-smartphone.png",
			rating: 4.9,
			price: "$549",
			category: "Gaming",
			slug: "steam-deck-oled",
			date: "April 5, 2025"
		},
		{
			id: 7,
			title: "iPhone 15 Pro",
			image: "/modern-smartphone.png",
			rating: 4.7,
			price: "$999",
			category: "Smartphones",
			slug: "iphone-15-pro",
			date: "March 30, 2025"
		},
		{
			id: 8,
			title: "Bose QuietComfort Ultra",
			image: "/modern-smartphone.png",
			rating: 4.6,
			price: "$429",
			category: "Headphones",
			slug: "bose-quietcomfort-ultra",
			date: "March 25, 2025"
		},
		{
			id: 9,
			title: "Dell XPS 13",
			image: "/modern-smartphone.png",
			rating: 4.4,
			price: "$999",
			category: "Laptops",
			slug: "dell-xps-13",
			date: "March 20, 2025"
		},
		{
			id: 10,
			title: "Sony A7 IV",
			image: "/modern-smartphone.png",
			rating: 4.6,
			price: "$2,399",
			category: "Cameras",
			slug: "sony-a7-iv",
			date: "March 15, 2025"
		},
		{
			id: 11,
			title: "Samsung Galaxy S23 Ultra",
			image: "/modern-smartphone.png",
			rating: 4.5,
			price: "$1,199",
			category: "Smartphones",
			slug: "samsung-galaxy-s23-ultra",
			date: "March 10, 2025"
		},
		{
			id: 12,
			title: "Apple AirPods Max",
			image: "/modern-smartphone.png",
			rating: 4.5,
			price: "$549",
			category: "Headphones",
			slug: "apple-airpods-max",
			date: "March 5, 2025"
		}
	];

	return (
		<div className='container mx-auto px-4 py-8'>
			<div className='mb-6'>
				<div className='flex items-center text-sm text-muted-foreground mb-4'>
					<Link href='/blog' className='hover:text-brand-dark-300'>
						Home
					</Link>
					<ChevronRight className='h-4 w-4 mx-2' />
					<span>All Reviews</span>
				</div>
				<h1 className='text-3xl font-bold'>All Product Reviews</h1>
				<p className='text-muted-foreground mt-2'>Browse our comprehensive collection of in-depth product reviews</p>
			</div>

			<div className='flex flex-col md:flex-row gap-6 mb-8'>
				<div className='md:w-1/4 space-y-6'>
					<div className='border rounded-lg p-4'>
						<h3 className='font-semibold mb-4 flex items-center'>
							<Filter className='h-4 w-4 mr-2' />
							Filter Reviews
						</h3>

						<div className='space-y-4'>
							<div>
								<label className='text-sm font-medium mb-1 block'>Category</label>
								<Select>
									<SelectTrigger>
										<SelectValue placeholder='All Categories' />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value='all'>All Categories</SelectItem>
										<SelectItem value='smartphones'>Smartphones</SelectItem>
										<SelectItem value='laptops'>Laptops</SelectItem>
										<SelectItem value='headphones'>Headphones</SelectItem>
										<SelectItem value='tvs'>TVs</SelectItem>
										<SelectItem value='cameras'>Cameras</SelectItem>
										<SelectItem value='gaming'>Gaming</SelectItem>
									</SelectContent>
								</Select>
							</div>

							<div>
								<label className='text-sm font-medium mb-1 block'>Rating</label>
								<Select>
									<SelectTrigger>
										<SelectValue placeholder='Any Rating' />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value='any'>Any Rating</SelectItem>
										<SelectItem value='4.5'>4.5+ Stars</SelectItem>
										<SelectItem value='4'>4+ Stars</SelectItem>
										<SelectItem value='3.5'>3.5+ Stars</SelectItem>
									</SelectContent>
								</Select>
							</div>

							<div>
								<label className='text-sm font-medium mb-1 block'>Price Range</label>
								<Select>
									<SelectTrigger>
										<SelectValue placeholder='Any Price' />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value='any'>Any Price</SelectItem>
										<SelectItem value='budget'>Budget (Under $500)</SelectItem>
										<SelectItem value='mid'>Mid-range ($500-$1000)</SelectItem>
										<SelectItem value='premium'>Premium ($1000-$2000)</SelectItem>
										<SelectItem value='luxury'>Luxury ($2000+)</SelectItem>
									</SelectContent>
								</Select>
							</div>

							<Button className='w-full'>Apply Filters</Button>
						</div>
					</div>

					<div className='border rounded-lg p-4'>
						<h3 className='font-semibold mb-4'>Popular Categories</h3>
						<div className='space-y-2'>
							{["Smartphones", "Laptops", "Headphones", "TVs", "Cameras", "Gaming"].map((category) => (
								<Link
									key={category}
									href={`/category/${category.toLowerCase()}`}
									className='block py-2 px-3 rounded-md hover:bg-muted transition-colors hover:text-brand-dark-200'
								>
									{category}
								</Link>
							))}
						</div>
					</div>
				</div>

				<div className='md:w-3/4'>
					<Tabs defaultValue='all' className='mb-6'>
						<TabsList>
							<TabsTrigger value='all'>All Reviews</TabsTrigger>
							<TabsTrigger value='latest'>Latest</TabsTrigger>
							<TabsTrigger value='popular'>Most Popular</TabsTrigger>
							<TabsTrigger value='highest'>Highest Rated</TabsTrigger>
						</TabsList>

						<div className='flex justify-between items-center mt-6 mb-4'>
							<p className='text-sm text-muted-foreground'>Showing {reviews.length} reviews</p>
							<Select defaultValue='newest'>
								<SelectTrigger className='w-[180px]'>
									<SelectValue placeholder='Sort by' />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value='newest'>Newest First</SelectItem>
									<SelectItem value='oldest'>Oldest First</SelectItem>
									<SelectItem value='rating'>Highest Rating</SelectItem>
									<SelectItem value='price-high'>Price: High to Low</SelectItem>
									<SelectItem value='price-low'>Price: Low to High</SelectItem>
								</SelectContent>
							</Select>
						</div>

						<TabsContent value='all' className='mt-0'>
							<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
								{reviews.map((review) => (
									<Link href={`/reviews/${review.slug}`} key={review.id} className='group'>
										<div className='border rounded-lg overflow-hidden transition-all hover:shadow-md'>
											<div className='aspect-square relative'>
												<div className='absolute top-3 left-3 z-10 bg-brand text-brand-black px-2 py-1 rounded text-xs font-medium'>
													{review.category}
												</div>
												<Image
													src={review.image || "/placeholder.svg"}
													alt={review.title}
													fill
													className='object-contain p-6 group-hover:scale-105 transition-transform duration-300'
												/>
											</div>
											<div className='p-4 border-t'>
												<h3 className='font-semibold group-hover:text-brand-dark-300 transition-colors'>
													{review.title}
												</h3>
												<div className='flex items-center mt-2'>
													<div className='flex'>
														{[...Array(5)].map((_, i) => (
															<Star
																key={i}
																className={`h-4 w-4 ${
																	i < Math.floor(review.rating)
																		? "fill-yellow-400 text-yellow-400"
																		: "fill-muted text-muted-foreground"
																}`}
															/>
														))}
													</div>
													<span className='text-sm ml-1'>{review.rating.toFixed(1)}</span>
												</div>
												<div className='flex items-center justify-between mt-2'>
													<span className='font-semibold text-brand-dark-300'>{review.price}</span>
													<span className='text-xs text-muted-foreground'>{review.date}</span>
												</div>
											</div>
										</div>
									</Link>
								))}
							</div>
						</TabsContent>

						<TabsContent value='latest' className='mt-0'>
							<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
								{reviews.slice(0, 6).map((review) => (
									<Link href={`/reviews/${review.slug}`} key={review.id} className='group'>
										<div className='border rounded-lg overflow-hidden transition-all hover:shadow-md'>
											<div className='aspect-square relative'>
												<div className='absolute top-3 left-3 z-10 bg-red-600 text-white px-2 py-1 rounded text-xs font-medium'>
													{review.category}
												</div>
												<Image
													src={review.image || "/placeholder.svg"}
													alt={review.title}
													fill
													className='object-contain p-6 group-hover:scale-105 transition-transform duration-300'
												/>
											</div>
											<div className='p-4 border-t'>
												<h3 className='font-semibold group-hover:text-red-600 transition-colors'>{review.title}</h3>
												<div className='flex items-center mt-2'>
													<div className='flex'>
														{[...Array(5)].map((_, i) => (
															<Star
																key={i}
																className={`h-4 w-4 ${
																	i < Math.floor(review.rating)
																		? "fill-yellow-400 text-yellow-400"
																		: "fill-muted text-muted-foreground"
																}`}
															/>
														))}
													</div>
													<span className='text-sm ml-1'>{review.rating.toFixed(1)}</span>
												</div>
												<div className='flex items-center justify-between mt-2'>
													<span className='font-semibold text-red-600'>{review.price}</span>
													<span className='text-xs text-muted-foreground'>{review.date}</span>
												</div>
											</div>
										</div>
									</Link>
								))}
							</div>
						</TabsContent>

						<TabsContent value='popular' className='mt-0'>
							<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
								{[...reviews]
									.sort((a, b) => b.rating - a.rating)
									.slice(0, 6)
									.map((review) => (
										<Link href={`/reviews/${review.slug}`} key={review.id} className='group'>
											<div className='border rounded-lg overflow-hidden transition-all hover:shadow-md'>
												<div className='aspect-square relative'>
													<div className='absolute top-3 left-3 z-10 bg-red-600 text-white px-2 py-1 rounded text-xs font-medium'>
														{review.category}
													</div>
													<Image
														src={review.image || "/placeholder.svg"}
														alt={review.title}
														fill
														className='object-contain p-6 group-hover:scale-105 transition-transform duration-300'
													/>
												</div>
												<div className='p-4 border-t'>
													<h3 className='font-semibold group-hover:text-red-600 transition-colors'>{review.title}</h3>
													<div className='flex items-center mt-2'>
														<div className='flex'>
															{[...Array(5)].map((_, i) => (
																<Star
																	key={i}
																	className={`h-4 w-4 ${
																		i < Math.floor(review.rating)
																			? "fill-yellow-400 text-yellow-400"
																			: "fill-muted text-muted-foreground"
																	}`}
																/>
															))}
														</div>
														<span className='text-sm ml-1'>{review.rating.toFixed(1)}</span>
													</div>
													<div className='flex items-center justify-between mt-2'>
														<span className='font-semibold text-red-600'>{review.price}</span>
														<span className='text-xs text-muted-foreground'>{review.date}</span>
													</div>
												</div>
											</div>
										</Link>
									))}
							</div>
						</TabsContent>

						<TabsContent value='highest' className='mt-0'>
							<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
								{[...reviews]
									.sort((a, b) => b.rating - a.rating)
									.slice(0, 6)
									.map((review) => (
										<Link href={`/reviews/${review.slug}`} key={review.id} className='group'>
											<div className='border rounded-lg overflow-hidden transition-all hover:shadow-md'>
												<div className='aspect-square relative'>
													<div className='absolute top-3 left-3 z-10 bg-red-600 text-white px-2 py-1 rounded text-xs font-medium'>
														{review.category}
													</div>
													<Image
														src={review.image || "/placeholder.svg"}
														alt={review.title}
														fill
														className='object-contain p-6 group-hover:scale-105 transition-transform duration-300'
													/>
												</div>
												<div className='p-4 border-t'>
													<h3 className='font-semibold group-hover:text-red-600 transition-colors'>{review.title}</h3>
													<div className='flex items-center mt-2'>
														<div className='flex'>
															{[...Array(5)].map((_, i) => (
																<Star
																	key={i}
																	className={`h-4 w-4 ${
																		i < Math.floor(review.rating)
																			? "fill-yellow-400 text-yellow-400"
																			: "fill-muted text-muted-foreground"
																	}`}
																/>
															))}
														</div>
														<span className='text-sm ml-1'>{review.rating.toFixed(1)}</span>
													</div>
													<div className='flex items-center justify-between mt-2'>
														<span className='font-semibold text-red-600'>{review.price}</span>
														<span className='text-xs text-muted-foreground'>{review.date}</span>
													</div>
												</div>
											</div>
										</Link>
									))}
							</div>
						</TabsContent>
					</Tabs>

					<div className='flex justify-center mt-8'>
						<Button variant='outline' className='mx-1'>
							1
						</Button>
						<Button variant='outline' className='mx-1'>
							2
						</Button>
						<Button variant='outline' className='mx-1'>
							3
						</Button>
						<Button variant='outline' className='mx-1'>
							...
						</Button>
						<Button variant='outline' className='mx-1'>
							Next
						</Button>
					</div>
				</div>
			</div>
		</div>
	);
}
