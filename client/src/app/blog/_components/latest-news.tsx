import { Clock } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

export default function LatestNews() {
	const news = [
		{
			id: 1,
			title: "Apple's M3 MacBook Pro: A Leap Forward for Creative Professionals",
			excerpt:
				"<p>The new M3 chip brings significant performance improvements, especially for video editing and 3D rendering tasks. Our tests show up to 40% faster exports compared to M2.</p>",
			image: "/mac-book.webp",
			date: "2 hours ago",
			slug: "apple-m3-macbook-pro-review"
		},
		{
			id: 2,
			title: "Sony Announces the A7R V with Revolutionary AI Autofocus",
			excerpt:
				"<p>Sony's latest flagship mirrorless camera features a new AI processing unit dedicated to subject recognition and tracking, making it the most reliable autofocus system we've tested.</p>",
			image: "/modern-smartphone.png",
			date: "5 hours ago",
			slug: "sony-a7r-v-announcement"
		},
		{
			id: 3,
			title: "Samsung's New QD-OLED TVs Redefine Home Theater Experience",
			excerpt:
				"<p>The second generation of Quantum Dot OLED technology delivers even better brightness and color volume, challenging the best OLED TVs from LG while maintaining perfect blacks.</p>",
			image: "/tv.jpeg",
			date: "Yesterday",
			slug: "samsung-qd-oled-tvs"
		},
		{
			id: 4,
			title: "Bose QuietComfort Ultra Headphones: The New King of ANC?",
			excerpt:
				"<p>Bose's latest flagship headphones introduce spatial audio and improved noise cancellation that might finally dethrone Sony's WH-1000XM5 as our top recommendation.</p>",
			image: "/camera.jpeg",
			date: "Yesterday",
			slug: "bose-quietcomfort-ultra-review"
		},
		{
			id: 5,
			title: "NVIDIA RTX 5090: What We Know About the Next-Gen GPU",
			excerpt:
				"<p>Leaks suggest NVIDIA's next flagship graphics card will use the new Ada Lovelace architecture and could offer up to 70% better ray tracing performance compared to the RTX 4090.</p>",
			image: "/headphone.jpg",
			date: "2 days ago",
			slug: "nvidia-rtx-5090-leaks"
		}
	];

	return (
		<div className='space-y-6'>
			<div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
				<div className='relative rounded-xl overflow-hidden group'>
					<div className='absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent z-10' />
					<Image
						src={news[0].image || "/placeholder.svg"}
						alt={news[0].title}
						width={600}
						height={400}
						className='w-full aspect-video object-cover group-hover:scale-105 transition-transform duration-300'
					/>
					<div className='absolute bottom-0 left-0 right-0 p-6 z-20'>
						<Link href={`/news/${news[0].slug}`}>
							<h3 className='text-xl font-bold text-white mb-2 group-hover:text-brand-dark-100 transition-colors'>
								{news[0].title}
							</h3>
						</Link>
						<div
							className='text-white/80 text-sm mb-3 line-clamp-2'
							dangerouslySetInnerHTML={{ __html: news[0].excerpt }}
						/>
						<div className='flex items-center text-white/70 text-xs'>
							<Clock className='h-3 w-3 mr-1' />
							{news[0].date}
						</div>
					</div>
				</div>
				<div className='relative rounded-xl overflow-hidden group'>
					<div className='absolute inset-0 bg-gradient-to-t from-black/80 via-black/40 to-transparent z-10' />
					<Image
						src={news[1].image || "/placeholder.svg"}
						alt={news[1].title}
						width={600}
						height={400}
						className='w-full aspect-video object-cover group-hover:scale-105 transition-transform duration-300'
					/>
					<div className='absolute bottom-0 left-0 right-0 p-6 z-20'>
						<Link href={`/news/${news[1].slug}`}>
							<h3 className='text-xl font-bold text-white mb-2 group-hover:text-brand-dark-100 transition-colors'>
								{news[1].title}
							</h3>
						</Link>
						<div
							className='text-white/80 text-sm mb-3 line-clamp-2'
							dangerouslySetInnerHTML={{ __html: news[1].excerpt }}
						/>
						<div className='flex items-center text-white/70 text-xs'>
							<Clock className='h-3 w-3 mr-1' />
							{news[1].date}
						</div>
					</div>
				</div>
			</div>
			<div className='grid grid-cols-1 md:grid-cols-3 gap-6'>
				{news.slice(2).map((item) => (
					<div key={item.id} className='border rounded-xl overflow-hidden group hover:shadow-md'>
						<div className='relative'>
							<Image
								src={item.image || "/placeholder.svg"}
								alt={item.title}
								width={400}
								height={240}
								className='w-full aspect-video object-cover group-hover:scale-105 transition-transform duration-300'
							/>
						</div>
						<div className='p-4'>
							<Link href={`/news/${item.slug}`}>
								<h3 className='font-semibold mb-2 line-clamp-2 group-hover:text-brand-dark-300 transition-colors'>
									{item.title}
								</h3>
							</Link>
							<div
								className='text-sm text-muted-foreground mb-3 line-clamp-2'
								dangerouslySetInnerHTML={{ __html: item.excerpt }}
							/>
							<div className='flex items-center text-xs text-muted-foreground'>
								<Clock className='h-3 w-3 mr-1' />
								{item.date}
							</div>
						</div>
					</div>
				))}
			</div>
		</div>
	);
}
