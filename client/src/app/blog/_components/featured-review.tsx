import { Star } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

interface FeaturedReviewProps {
	title: string;
	image: string;
	rating: number;
	category: string;
	slug: string;
}

export default function FeaturedReview({ title, image, rating, category, slug }: FeaturedReviewProps) {
	return (
		<Link href={`/reviews/${slug}`} className='group'>
			<div className='border rounded-xl overflow-hidden transition-all hover:shadow-lg'>
				<div className='relative'>
					<div className='absolute top-3 left-3 z-10 bg-brand text-brand-black px-2 py-1 rounded text-xs font-medium'>
						{category}
					</div>
					<Image
						src={image || "/placeholder.svg"}
						alt={title}
						width={400}
						height={300}
						className='w-full aspect-[4/3] object-cover group-hover:scale-105 transition-transform duration-300'
					/>
				</div>
				<div className='p-4'>
					<h3 className='font-semibold text-lg mb-2 group-hover:text-brand-dark-300 transition-colors'>
						{title} Review
					</h3>
					<div className='flex items-center justify-between'>
						<div className='flex items-center'>
							{[...Array(5)].map((_, i) => (
								<Star
									key={i}
									className={`h-4 w-4 ${
										i < Math.floor(rating) ? "fill-yellow-400 text-yellow-400" : "fill-muted text-muted-foreground"
									}`}
								/>
							))}
							<span className='ml-2 text-sm font-medium'>{rating.toFixed(1)}</span>
						</div>
						<span className='text-sm text-muted-foreground'>3 days ago</span>
					</div>
				</div>
			</div>
		</Link>
	);
}
