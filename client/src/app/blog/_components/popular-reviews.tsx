import { <PERSON>, MessageSquare, Star } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

export default function PopularReviews() {
	const reviews = [
		{
			id: 1,
			title: "Sony WH-1000XM5",
			excerpt:
				"<p>Sony's flagship noise-cancelling headphones set a new standard with improved ANC, natural sound quality, and exceptional comfort for long listening sessions.</p>",
			image: "/modern-smartphone.png",
			rating: 4.8,
			comments: 156,
			views: "45K",
			slug: "sony-wh-1000xm5",
			category: "Headphones"
		},
		{
			id: 2,
			title: "tv.jpeg",
			excerpt:
				"<p>LG's mid-tier OLED delivers perfect blacks, vibrant colors, and gaming features like 4K/120Hz, VRR, and ALLM that make it ideal for both movies and next-gen consoles.</p>",
			image: "/headphone.jpg",
			rating: 4.7,
			comments: 203,
			views: "62K",
			slug: "lg-c3-oled",
			category: "TVs"
		},
		{
			id: 3,
			title: "camera.jpeg",
			excerpt:
				"<p>Canon's updated full-frame mirrorless camera addresses the overheating issues of its predecessor while improving autofocus and adding 6K RAW video recording.</p>",
			image: "/modern-smartphone.png",
			rating: 4.6,
			comments: 124,
			views: "38K",
			slug: "canon-eos-r6-mark-ii",
			category: "Cameras"
		},
		{
			id: 4,
			title: "Steam Deck OLED",
			excerpt:
				"<p>Valve's refreshed handheld gaming PC features a gorgeous OLED display with better battery life and improved thermals, making it the ultimate portable gaming device.</p>",
			image: "/modern-smartphone.png",
			rating: 4.9,
			comments: 98,
			views: "29K",
			slug: "steam-deck-oled",
			category: "Gaming"
		}
	];

	return (
		<div className='space-y-6'>
			{reviews.map((review) => (
				<div
					key={review.id}
					className='flex flex-col md:flex-row border rounded-xl overflow-hidden hover:shadow-md group'
				>
					<div className='md:w-1/4 relative'>
						<Image
							src={review.image || "/placeholder.svg"}
							alt={review.title}
							width={200}
							height={200}
							className='w-full aspect-square object-cover group-hover:scale-105 transition-transform duration-300'
						/>
					</div>
					<div className='p-6 md:w-3/4'>
						<div className='flex items-center gap-2 mb-2'>
							<span className='text-xs font-medium px-2 py-1 bg-brand-light-100 text-brand-dark-700 rounded-full'>
								{review.category}
							</span>
						</div>
						<Link href={`/reviews/${review.slug}`}>
							<h3 className='text-xl font-bold mb-2 group-hover:text-brand-dark-300 transition-colors'>
								{review.title} Review
							</h3>
						</Link>
						<div className='text-muted-foreground mb-4' dangerouslySetInnerHTML={{ __html: review.excerpt }} />
						<div className='flex flex-wrap items-center gap-4'>
							<div className='flex items-center'>
								<div className='flex'>
									{[...Array(5)].map((_, i) => (
										<Star
											key={i}
											className={`h-4 w-4 ${
												i < Math.floor(review.rating)
													? "fill-yellow-400 text-yellow-400"
													: "fill-muted text-muted-foreground"
											}`}
										/>
									))}
								</div>
								<span className='ml-2 text-sm font-medium'>{review.rating.toFixed(1)}</span>
							</div>
							<div className='flex items-center text-sm text-muted-foreground'>
								<MessageSquare className='h-4 w-4 mr-1' />
								{review.comments} comments
							</div>
							<div className='flex items-center text-sm text-muted-foreground'>
								<Eye className='h-4 w-4 mr-1' />
								{review.views} views
							</div>
						</div>
					</div>
				</div>
			))}
		</div>
	);
}
