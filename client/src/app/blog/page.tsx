import { ChevronR<PERSON>, TrendingUp } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import FeaturedReview from "./_components/featured-review";
import LatestNews from "./_components/latest-news";
import PopularReviews from "./_components/popular-reviews";

export default function Home() {
	return (
		<div className='container mx-auto px-4 py-8'>
			{/* Hero Section */}
			<section className='mb-12'>
				<div className='relative rounded-xl overflow-hidden'>
					<div className='absolute inset-0 bg-gradient-to-r from-black/70 to-black/30 z-10' />
					<Image
						src='/mac-book.webp'
						alt='Latest Technology'
						width={1200}
						height={500}
						className='w-full h-[500px] object-cover'
					/>
					<div className='absolute inset-0 z-20 flex flex-col justify-end p-8'>
						<span className='bg-brand text-brand-black px-3 py-1 rounded-md text-sm font-medium mb-3 w-fit'>
							FEATURED
						</span>
						<h1 className='text-3xl md:text-4xl lg:text-5xl font-bold text-white mb-4'>
							MacBook Pro M3 Max Review: The Ultimate Creator's Machine
						</h1>
						<p className='text-white/90 text-lg mb-6 max-w-2xl'>
							We test Apple's most powerful laptop to see if it lives up to the hype for creative professionals
						</p>
						<Link href='/blog/reviews/macbook-pro-m3-max'>
							<Button className='w-fit bg-brand hover:bg-brand-dark-200 text-brand-black'>Read Full Review</Button>
						</Link>
					</div>
				</div>
			</section>

			{/* Featured Reviews Grid */}
			<section className='mb-12'>
				<div className='flex items-center justify-between mb-6'>
					<h2 className='text-2xl font-bold'>Featured Reviews</h2>
					<Link href='/blog/reviews' className='text-brand-dark-300 hover:text-brand-dark-200 flex items-center'>
						View All <ChevronRight className='h-4 w-4 ml-1' />
					</Link>
				</div>
				<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
					<FeaturedReview
						title='Sony WH-1000XM5'
						image='/modern-smartphone.png'
						rating={4.8}
						category='Headphones'
						slug='sony-wh-1000xm5'
					/>
					<FeaturedReview
						title='Samsung QN90C QLED TV'
						image='/modern-smartphone.png'
						rating={4.6}
						category='TVs'
						slug='samsung-qn90c-qled'
					/>
					<FeaturedReview
						title='Canon EOS R6 Mark II'
						image='/modern-smartphone.png'
						rating={4.7}
						category='Cameras'
						slug='canon-eos-r6-mark-ii'
					/>
				</div>
			</section>

			{/* Categories Section */}
			<section className='mb-12'>
				<div className='flex items-center justify-between mb-6'>
					<h2 className='text-2xl font-bold'>Browse Categories</h2>
				</div>
				<div className='grid grid-cols-2 md:grid-cols-4 gap-4'>
					<Link href='/blog/category/smartphones' className='group'>
						<div className='border rounded-lg overflow-hidden transition-all hover:shadow-md'>
							<div className='aspect-video relative'>
								<Image
									src='/modern-smartphone.png'
									alt='Smartphones'
									fill
									className='object-cover group-hover:scale-105 transition-transform duration-300'
								/>
								<div className='absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex items-end'>
									<h3 className='text-white font-semibold p-4'>Smartphones</h3>
								</div>
							</div>
						</div>
					</Link>
					<Link href='/blog/category/laptops' className='group'>
						<div className='border rounded-lg overflow-hidden transition-all hover:shadow-md'>
							<div className='aspect-video relative'>
								<Image
									src='/mac-book.webp'
									alt='Laptops'
									fill
									className='object-cover group-hover:scale-105 transition-transform duration-300'
								/>
								<div className='absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex items-end'>
									<h3 className='text-white font-semibold p-4'>Laptops</h3>
								</div>
							</div>
						</div>
					</Link>
					<Link href='/blog/category/headphones' className='group'>
						<div className='border rounded-lg overflow-hidden transition-all hover:shadow-md'>
							<div className='aspect-video relative'>
								<Image
									src='/headphone.jpg'
									alt='Headphones'
									fill
									className='object-cover group-hover:scale-105 transition-transform duration-300'
								/>
								<div className='absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex items-end'>
									<h3 className='text-white font-semibold p-4'>Headphones</h3>
								</div>
							</div>
						</div>
					</Link>
					<Link href='/blog/category/cameras' className='group'>
						<div className='border rounded-lg overflow-hidden transition-all hover:shadow-md'>
							<div className='aspect-video relative'>
								<Image
									src='/camera.jpeg'
									alt='Cameras'
									fill
									className='object-cover group-hover:scale-105 transition-transform duration-300'
								/>
								<div className='absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex items-end'>
									<h3 className='text-white font-semibold p-4'>Cameras</h3>
								</div>
							</div>
						</div>
					</Link>
				</div>
				<div className='grid grid-cols-2 md:grid-cols-4 gap-4 mt-4'>
					<Link href='/blog/category/tvs' className='group'>
						<div className='border rounded-lg overflow-hidden transition-all hover:shadow-md'>
							<div className='aspect-video relative'>
								<Image
									src='/tv.jpeg'
									alt='TVs'
									fill
									className='object-cover group-hover:scale-105 transition-transform duration-300'
								/>
								<div className='absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex items-end'>
									<h3 className='text-white font-semibold p-4'>TVs</h3>
								</div>
							</div>
						</div>
					</Link>
					<Link href='/blog/category/smartwatches' className='group'>
						<div className='border rounded-lg overflow-hidden transition-all hover:shadow-md'>
							<div className='aspect-video relative'>
								<Image
									src='/watch.jpeg'
									alt='Smartwatches'
									fill
									className='object-cover group-hover:scale-105 transition-transform duration-300'
								/>
								<div className='absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex items-end'>
									<h3 className='text-white font-semibold p-4'>Smartwatches</h3>
								</div>
							</div>
						</div>
					</Link>
					<Link href='/blog/category/gaming' className='group'>
						<div className='border rounded-lg overflow-hidden transition-all hover:shadow-md'>
							<div className='aspect-video relative'>
								<Image
									src='/gaming.jpeg'
									alt='Gaming'
									fill
									className='object-cover group-hover:scale-105 transition-transform duration-300'
								/>
								<div className='absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex items-end'>
									<h3 className='text-white font-semibold p-4'>Gaming</h3>
								</div>
							</div>
						</div>
					</Link>
					<Link href='/blog/category/audio' className='group'>
						<div className='border rounded-lg overflow-hidden transition-all hover:shadow-md'>
							<div className='aspect-video relative'>
								<Image
									src='/audio.jpeg'
									alt='Audio'
									fill
									className='object-cover group-hover:scale-105 transition-transform duration-300'
								/>
								<div className='absolute inset-0 bg-gradient-to-t from-black/70 to-transparent flex items-end'>
									<h3 className='text-white font-semibold p-4'>Audio</h3>
								</div>
							</div>
						</div>
					</Link>
				</div>
			</section>

			{/* Main Content Tabs */}
			<section className='mb-12'>
				<Tabs defaultValue='latest' className='w-full'>
					<TabsList className='grid w-full grid-cols-3 mb-8'>
						<TabsTrigger value='latest'>Latest News</TabsTrigger>
						<TabsTrigger value='popular'>Popular Reviews</TabsTrigger>
						<TabsTrigger value='deals'>Hot Deals</TabsTrigger>
					</TabsList>
					<TabsContent value='latest'>
						<LatestNews />
					</TabsContent>
					<TabsContent value='popular'>
						<PopularReviews />
					</TabsContent>
					<TabsContent value='deals'>
						<div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
							{[
								{
									name: "Sony WH-1000XM5 Headphones",
									originalPrice: "$399.99",
									salePrice: "$299.99",
									image: "/modern-smartphone.png",
									slug: "sony-wh-1000xm5"
								},
								{
									name: "MacBook Air M2 8GB RAM 256GB",
									originalPrice: "$999.99",
									salePrice: "$849.99",
									image: "/modern-smartphone.png",
									slug: "macbook-air-m2"
								},
								{
									name: 'Samsung 65" QN90C QLED 4K TV',
									originalPrice: "$1,999.99",
									salePrice: "$1,599.99",
									image: "/modern-smartphone.png",
									slug: "samsung-qn90c-qled"
								},
								{
									name: "Bose QuietComfort Earbuds II",
									originalPrice: "$299.99",
									salePrice: "$199.99",
									image: "/modern-smartphone.png",
									slug: "bose-quietcomfort-earbuds-ii"
								}
							].map((deal, i) => (
								<Card key={i} className='overflow-hidden'>
									<CardContent className='p-0'>
										<div className='flex items-center p-4 border-b'>
											<div className='w-20 h-20 relative mr-4 flex-shrink-0'>
												<Image src={deal.image || "/placeholder.svg"} alt={deal.name} fill className='object-contain' />
											</div>
											<div>
												<h3 className='font-semibold'>{deal.name}</h3>
												<div className='flex items-center mt-1'>
													<span className='text-muted-foreground line-through mr-2'>{deal.originalPrice}</span>
													<span className='text-brand-dark-300 font-bold'>{deal.salePrice}</span>
												</div>
											</div>
										</div>
									</CardContent>
									<CardFooter className='p-4 pt-2'>
										<Link href={`/reviews/${deal.slug}`} className='w-full'>
											<Button variant='outline' size='sm' className='w-full'>
												View Deal
											</Button>
										</Link>
									</CardFooter>
								</Card>
							))}
						</div>
					</TabsContent>
				</Tabs>
			</section>

			{/* Trending Section */}
			<section className='mb-12'>
				<div className='flex items-center justify-between mb-6'>
					<h2 className='text-2xl font-bold flex items-center'>
						<TrendingUp className='mr-2 h-5 w-5 text-brand-dark-200' />
						Trending Devices
					</h2>
					<Link href='/blog/trending' className=' text-brand-dark-300 flex items-center'>
						View All <ChevronRight className='h-4 w-4 ml-1' />
					</Link>
				</div>
				<div className='grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4'>
					{[
						{
							name: "iPhone 15 Pro",
							image: "/modern-smartphone.png",
							category: "Smartphone",
							slug: "iphone-15-pro"
						},
						{
							name: "Sony A7 IV",
							image: "/modern-smartphone.png",
							category: "Camera",
							slug: "sony-a7-iv"
						},
						{
							name: "MacBook Pro M3",
							image: "/modern-smartphone.png",
							category: "Laptop",
							slug: "macbook-pro-m3-max"
						},
						{
							name: "Samsung S95C OLED",
							image: "/modern-smartphone.png",
							category: "TV",
							slug: "samsung-s95c-oled"
						},
						{
							name: "AirPods Pro 2",
							image: "/modern-smartphone.png",
							category: "Audio",
							slug: "airpods-pro-2"
						},
						{
							name: "PS5 Slim",
							image: "/modern-smartphone.png",
							category: "Gaming",
							slug: "playstation-5-slim"
						}
					].map((device, i) => (
						<Link href={`/reviews/${device.slug}`} key={i} className='group'>
							<div className='border rounded-lg p-4 transition-all hover:shadow-md group-hover:border-red-200'>
								<div className='aspect-square relative mb-3'>
									<Image src={device.image || "/placeholder.svg"} alt={device.name} fill className='object-contain' />
								</div>
								<h3 className='text-sm font-medium text-center group-hover:text-brand-dark-300 transition-colors'>
									{device.name}
								</h3>
								<p className='text-xs text-center text-muted-foreground mt-1'>{device.category}</p>
							</div>
						</Link>
					))}
				</div>
			</section>
		</div>
	);
}
