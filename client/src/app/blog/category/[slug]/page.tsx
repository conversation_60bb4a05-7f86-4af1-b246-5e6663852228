import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "lucide-react";
import Image from "next/image";
import Link from "next/link";

import { Ta<PERSON>, <PERSON><PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";

// Mock data for different categories
const categoryData = {
	smartphones: {
		title: "Smartphones",
		description: "Browse the latest smartphones with cutting-edge features, powerful cameras, and all-day battery life",
		devices: [
			{
				id: 1,
				name: "iPhone 15 Pro",
				image: "/placeholder.svg?height=200&width=200&query=iphone 15 pro",
				rating: 4.7,
				price: "$999",
				releaseDate: "2024 Q3"
			},
			{
				id: 2,
				name: "Samsung Galaxy S23 Ultra",
				image: "/placeholder.svg?height=200&width=200&query=samsung galaxy",
				rating: 4.5,
				price: "$1,199",
				releaseDate: "2024 Q1"
			},
			{
				id: 3,
				name: "Google Pixel 8 Pro",
				image: "/placeholder.svg?height=200&width=200&query=google pixel",
				rating: 4.6,
				price: "$899",
				releaseDate: "2024 Q4"
			},
			{
				id: 4,
				name: "OnePlus 12",
				image: "/placeholder.svg?height=200&width=200&query=oneplus phone",
				rating: 4.4,
				price: "$799",
				releaseDate: "2024 Q1"
			},
			{
				id: 5,
				name: "Xiaomi 14 Ultra",
				image: "/placeholder.svg?height=200&width=200&query=xiaomi phone",
				rating: 4.3,
				price: "$899",
				releaseDate: "2024 Q2"
			},
			{
				id: 6,
				name: "Nothing Phone 2",
				image: "/placeholder.svg?height=200&width=200&query=nothing phone",
				rating: 4.2,
				price: "$599",
				releaseDate: "2024 Q2"
			},
			{
				id: 7,
				name: "Motorola Edge 40 Pro",
				image: "/placeholder.svg?height=200&width=200&query=motorola phone",
				rating: 4.1,
				price: "$699",
				releaseDate: "2024 Q1"
			},
			{
				id: 8,
				name: "Sony Xperia 1 V",
				image: "/placeholder.svg?height=200&width=200&query=sony xperia",
				rating: 4.3,
				price: "$1,299",
				releaseDate: "2024 Q2"
			}
		]
	},
	laptops: {
		title: "Laptops",
		description: "Find powerful laptops for work, gaming, content creation, and everyday use from top brands",
		devices: [
			{
				id: 1,
				name: "MacBook Pro M3",
				image: "/placeholder.svg?height=200&width=200&query=macbook pro",
				rating: 4.9,
				price: "$1,599",
				releaseDate: "2024 Q4"
			},
			{
				id: 2,
				name: "Dell XPS 13",
				image: "/placeholder.svg?height=200&width=200&query=dell laptop",
				rating: 4.4,
				price: "$999",
				releaseDate: "2024 Q1"
			},
			{
				id: 3,
				name: "Lenovo ThinkPad X1 Carbon",
				image: "/placeholder.svg?height=200&width=200&query=lenovo thinkpad",
				rating: 4.5,
				price: "$1,299",
				releaseDate: "2024 Q2"
			},
			{
				id: 4,
				name: "HP Spectre x360",
				image: "/placeholder.svg?height=200&width=200&query=hp spectre",
				rating: 4.3,
				price: "$1,099",
				releaseDate: "2024 Q1"
			},
			{
				id: 5,
				name: "ASUS ROG Zephyrus G14",
				image: "/placeholder.svg?height=200&width=200&query=asus gaming laptop",
				rating: 4.7,
				price: "$1,499",
				releaseDate: "2024 Q2"
			},
			{
				id: 6,
				name: "Microsoft Surface Laptop 5",
				image: "/placeholder.svg?height=200&width=200&query=surface laptop",
				rating: 4.2,
				price: "$999",
				releaseDate: "2024 Q3"
			},
			{
				id: 7,
				name: "Razer Blade 15",
				image: "/placeholder.svg?height=200&width=200&query=razer laptop",
				rating: 4.6,
				price: "$1,799",
				releaseDate: "2024 Q1"
			},
			{
				id: 8,
				name: "Acer Swift 14",
				image: "/placeholder.svg?height=200&width=200&query=acer laptop",
				rating: 4.1,
				price: "$799",
				releaseDate: "2024 Q2"
			}
		]
	},
	headphones: {
		title: "Headphones",
		description: "Discover premium headphones with noise cancellation, high-fidelity sound, and comfortable designs",
		devices: [
			{
				id: 1,
				name: "Sony WH-1000XM5",
				image: "/placeholder.svg?height=200&width=200&query=sony headphones",
				rating: 4.8,
				price: "$349",
				releaseDate: "2024 Q1"
			},
			{
				id: 2,
				name: "Bose QuietComfort Ultra",
				image: "/placeholder.svg?height=200&width=200&query=bose headphones",
				rating: 4.6,
				price: "$429",
				releaseDate: "2024 Q1"
			},
			{
				id: 3,
				name: "Apple AirPods Max",
				image: "/placeholder.svg?height=200&width=200&query=airpods max",
				rating: 4.5,
				price: "$549",
				releaseDate: "2023 Q4"
			},
			{
				id: 4,
				name: "Sennheiser Momentum 4",
				image: "/placeholder.svg?height=200&width=200&query=sennheiser headphones",
				rating: 4.7,
				price: "$349",
				releaseDate: "2024 Q2"
			},
			{
				id: 5,
				name: "Beyerdynamic DT 900 Pro X",
				image: "/placeholder.svg?height=200&width=200&query=beyerdynamic headphones",
				rating: 4.4,
				price: "$299",
				releaseDate: "2024 Q1"
			},
			{
				id: 6,
				name: "Audio-Technica ATH-M50xBT2",
				image: "/placeholder.svg?height=200&width=200&query=audio technica headphones",
				rating: 4.3,
				price: "$199",
				releaseDate: "2023 Q4"
			},
			{
				id: 7,
				name: "Shure AONIC 50",
				image: "/placeholder.svg?height=200&width=200&query=shure headphones",
				rating: 4.2,
				price: "$299",
				releaseDate: "2024 Q2"
			},
			{
				id: 8,
				name: "Master & Dynamic MW75",
				image: "/placeholder.svg?height=200&width=200&query=master dynamic headphones",
				rating: 4.5,
				price: "$599",
				releaseDate: "2024 Q1"
			}
		]
	},
	cameras: {
		title: "Cameras",
		description: "Explore professional and enthusiast cameras for photography and videography from leading brands",
		devices: [
			{
				id: 1,
				name: "Sony A7 IV",
				image: "/placeholder.svg?height=200&width=200&query=sony camera",
				rating: 4.6,
				price: "$2,399",
				releaseDate: "2024 Q1"
			},
			{
				id: 2,
				name: "Canon EOS R6 Mark II",
				image: "/placeholder.svg?height=200&width=200&query=canon camera",
				rating: 4.5,
				price: "$2,499",
				releaseDate: "2024 Q2"
			},
			{
				id: 3,
				name: "Nikon Z8",
				image: "/placeholder.svg?height=200&width=200&query=nikon camera",
				rating: 4.7,
				price: "$3,999",
				releaseDate: "2024 Q1"
			},
			{
				id: 4,
				name: "Fujifilm X-T5",
				image: "/placeholder.svg?height=200&width=200&query=fujifilm camera",
				rating: 4.8,
				price: "$1,699",
				releaseDate: "2023 Q4"
			},
			{
				id: 5,
				name: "Panasonic Lumix S5 II",
				image: "/placeholder.svg?height=200&width=200&query=panasonic camera",
				rating: 4.4,
				price: "$1,999",
				releaseDate: "2024 Q2"
			},
			{
				id: 6,
				name: "Olympus OM-1",
				image: "/placeholder.svg?height=200&width=200&query=olympus camera",
				rating: 4.3,
				price: "$2,199",
				releaseDate: "2024 Q1"
			},
			{
				id: 7,
				name: "Leica Q3",
				image: "/placeholder.svg?height=200&width=200&query=leica camera",
				rating: 4.6,
				price: "$5,995",
				releaseDate: "2024 Q2"
			},
			{
				id: 8,
				name: "GoPro Hero 12",
				image: "/placeholder.svg?height=200&width=200&query=gopro camera",
				rating: 4.3,
				price: "$399",
				releaseDate: "2024 Q1"
			}
		]
	},
	tvs: {
		title: "TVs",
		description: "Find the perfect TV with stunning picture quality, smart features, and immersive sound",
		devices: [
			{
				id: 1,
				name: "Samsung S95C OLED",
				image: "/placeholder.svg?height=200&width=200&query=samsung oled tv",
				rating: 4.6,
				price: "$1,899",
				releaseDate: "2024 Q1"
			},
			{
				id: 2,
				name: "LG C3 OLED",
				image: "/placeholder.svg?height=200&width=200&query=lg oled tv",
				rating: 4.7,
				price: "$1,499",
				releaseDate: "2024 Q1"
			},
			{
				id: 3,
				name: "Sony A95L QD-OLED",
				image: "/placeholder.svg?height=200&width=200&query=sony oled tv",
				rating: 4.8,
				price: "$2,799",
				releaseDate: "2024 Q2"
			},
			{
				id: 4,
				name: "TCL 6-Series Mini-LED",
				image: "/placeholder.svg?height=200&width=200&query=tcl tv",
				rating: 4.4,
				price: "$999",
				releaseDate: "2024 Q1"
			},
			{
				id: 5,
				name: "Hisense U8K",
				image: "/placeholder.svg?height=200&width=200&query=hisense tv",
				rating: 4.3,
				price: "$899",
				releaseDate: "2024 Q2"
			},
			{
				id: 6,
				name: "Panasonic MZ2000",
				image: "/placeholder.svg?height=200&width=200&query=panasonic tv",
				rating: 4.5,
				price: "$2,499",
				releaseDate: "2024 Q1"
			},
			{
				id: 7,
				name: "Philips OLED+908",
				image: "/placeholder.svg?height=200&width=200&query=philips tv",
				rating: 4.4,
				price: "$1,999",
				releaseDate: "2024 Q2"
			},
			{
				id: 8,
				name: "Samsung QN90C Neo QLED",
				image: "/placeholder.svg?height=200&width=200&query=samsung qled tv",
				rating: 4.5,
				price: "$1,599",
				releaseDate: "2024 Q1"
			}
		]
	},
	gaming: {
		title: "Gaming",
		description: "Discover the latest gaming consoles, handhelds, and accessories for the ultimate gaming experience",
		devices: [
			{
				id: 1,
				name: "PlayStation 5 Slim",
				image: "/placeholder.svg?height=200&width=200&query=playstation 5",
				rating: 4.7,
				price: "$499",
				releaseDate: "2023 Q4"
			},
			{
				id: 2,
				name: "Xbox Series X",
				image: "/placeholder.svg?height=200&width=200&query=xbox series x",
				rating: 4.6,
				price: "$499",
				releaseDate: "2023 Q4"
			},
			{
				id: 3,
				name: "Nintendo Switch OLED",
				image: "/placeholder.svg?height=200&width=200&query=nintendo switch",
				rating: 4.5,
				price: "$349",
				releaseDate: "2023 Q3"
			},
			{
				id: 4,
				name: "Steam Deck OLED",
				image: "/placeholder.svg?height=200&width=200&query=steam deck",
				rating: 4.7,
				price: "$549",
				releaseDate: "2024 Q1"
			},
			{
				id: 5,
				name: "ASUS ROG Ally",
				image: "/placeholder.svg?height=200&width=200&query=asus rog ally",
				rating: 4.3,
				price: "$699",
				releaseDate: "2023 Q4"
			},
			{
				id: 6,
				name: "Meta Quest 3",
				image: "/placeholder.svg?height=200&width=200&query=meta quest",
				rating: 4.4,
				price: "$499",
				releaseDate: "2023 Q4"
			},
			{
				id: 7,
				name: "PlayStation VR2",
				image: "/placeholder.svg?height=200&width=200&query=playstation vr",
				rating: 4.2,
				price: "$549",
				releaseDate: "2023 Q4"
			},
			{
				id: 8,
				name: "Xbox Elite Controller Series 2",
				image: "/placeholder.svg?height=200&width=200&query=xbox controller",
				rating: 4.6,
				price: "$179",
				releaseDate: "2023 Q3"
			}
		]
	}
};

export default async function CategoryPage({ params }: { params: Promise<{ slug: string }> }) {
	// Await the params object to get the actual values
	const resolvedParams = await params;

	// Now you can use resolvedParams.slug safely
	const category = categoryData[resolvedParams.slug as keyof typeof categoryData] || categoryData.smartphones;

	return (
		<div className='container mx-auto px-4 py-8'>
			<div className='mb-6'>
				<div className='flex items-center text-sm text-muted-foreground mb-4'>
					<Link href='/blog' className='hover:text-brand-dark-300'>
						Home
					</Link>
					<ChevronRight className='h-4 w-4 mx-2' />
					<span>{category.title}</span>
				</div>
				<h1 className='text-3xl font-bold'>{category.title}</h1>
				<p className='text-muted-foreground mt-2'>{category.description}</p>
			</div>

			<Tabs defaultValue='all' className='mb-8'>
				<TabsList>
					<TabsTrigger value='all'>All</TabsTrigger>
					<TabsTrigger value='latest'>Latest</TabsTrigger>
					<TabsTrigger value='popular'>Popular</TabsTrigger>
					<TabsTrigger value='upcoming'>Upcoming</TabsTrigger>
				</TabsList>
				<TabsContent value='all' className='mt-6'>
					<div className='grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6'>
						{category.devices.map((device) => (
							<Link href={`/device-${device.id}`} key={device.id} className='group'>
								<div className='border rounded-lg overflow-hidden transition-all hover:shadow-md'>
									<div className='aspect-square relative p-4'>
										<Image
											src={device.image || "/placeholder.svg"}
											alt={device.name}
											fill
											className='object-contain p-4'
										/>
									</div>
									<div className='p-4 border-t'>
										<h3 className='font-medium group-hover:text-brand-dark-300 transition-colors'>{device.name}</h3>
										<div className='flex items-center mt-2'>
											<div className='flex'>
												{[...Array(5)].map((_, i) => (
													<Star
														key={i}
														className={`h-4 w-4 ${
															i < Math.floor(Number(device.rating))
																? "fill-yellow-400 text-yellow-400"
																: "fill-muted text-muted-foreground"
														}`}
													/>
												))}
											</div>
											<span className='text-sm ml-1'>{device.rating}</span>
										</div>
										<div className='flex items-center justify-between mt-2'>
											<span className='font-semibold text-brand-dark-300'>{device.price}</span>
											<span className='text-xs text-muted-foreground'>{device.releaseDate}</span>
										</div>
									</div>
								</div>
							</Link>
						))}
					</div>
				</TabsContent>
				<TabsContent value='latest' className='mt-6'>
					<div className='grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6'>
						{category.devices.slice(0, 8).map((device) => (
							<Link href={`/device-${device.id}`} key={device.id} className='group'>
								<div className='border rounded-lg overflow-hidden transition-all hover:shadow-md'>
									<div className='aspect-square relative p-4'>
										<Image
											src={device.image || "/placeholder.svg"}
											alt={device.name}
											fill
											className='object-contain p-4'
										/>
									</div>
									<div className='p-4 border-t'>
										<h3 className='font-medium group-hover:text-brand-dark-300 transition-colors'>{device.name}</h3>
										<div className='flex items-center mt-2'>
											<div className='flex'>
												{[...Array(5)].map((_, i) => (
													<Star
														key={i}
														className={`h-4 w-4 ${
															i < Math.floor(Number(device.rating))
																? "fill-yellow-400 text-yellow-400"
																: "fill-muted text-muted-foreground"
														}`}
													/>
												))}
											</div>
											<span className='text-sm ml-1'>{device.rating}</span>
										</div>
										<div className='flex items-center justify-between mt-2'>
											<span className='font-semibold text-brand-dark-300'>{device.price}</span>
											<span className='text-xs text-muted-foreground'>{device.releaseDate}</span>
										</div>
									</div>
								</div>
							</Link>
						))}
					</div>
				</TabsContent>
				<TabsContent value='popular' className='mt-6'>
					<div className='grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6'>
						{[...category.devices]
							.sort((a, b) => b.rating - a.rating)
							.slice(0, 8)
							.map((device) => (
								<Link href={`/device-${device.id}`} key={device.id} className='group'>
									<div className='border rounded-lg overflow-hidden transition-all hover:shadow-md'>
										<div className='aspect-square relative p-4'>
											<Image
												src={device.image || "/placeholder.svg"}
												alt={device.name}
												fill
												className='object-contain p-4'
											/>
										</div>
										<div className='p-4 border-t'>
											<h3 className='font-medium group-hover:text-brand-dark-300 transition-colors'>{device.name}</h3>
											<div className='flex items-center mt-2'>
												<div className='flex'>
													{[...Array(5)].map((_, i) => (
														<Star
															key={i}
															className={`h-4 w-4 ${
																i < Math.floor(Number(device.rating))
																	? "fill-yellow-400 text-yellow-400"
																	: "fill-muted text-muted-foreground"
															}`}
														/>
													))}
												</div>
												<span className='text-sm ml-1'>{device.rating}</span>
											</div>
											<div className='flex items-center justify-between mt-2'>
												<span className='font-semibold text-brand-dark-300'>{device.price}</span>
												<span className='text-xs text-muted-foreground'>{device.releaseDate}</span>
											</div>
										</div>
									</div>
								</Link>
							))}
					</div>
				</TabsContent>
				<TabsContent value='upcoming' className='mt-6'>
					<div className='grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6'>
						{category.devices
							.filter((device) => device.releaseDate.includes("2024"))
							.slice(0, 8)
							.map((device) => (
								<Link href={`/device-${device.id}`} key={device.id} className='group'>
									<div className='border rounded-lg overflow-hidden transition-all hover:shadow-md'>
										<div className='aspect-square relative p-4'>
											<Image
												src={device.image || "/placeholder.svg"}
												alt={device.name}
												fill
												className='object-contain p-4'
											/>
										</div>
										<div className='p-4 border-t'>
											<h3 className='font-medium group-hover:text-brand-dark-300 transition-colors'>{device.name}</h3>
											<div className='flex items-center mt-2'>
												<div className='flex'>
													{[...Array(5)].map((_, i) => (
														<Star
															key={i}
															className={`h-4 w-4 ${
																i < Math.floor(Number(device.rating))
																	? "fill-yellow-400 text-yellow-400"
																	: "fill-muted text-muted-foreground"
															}`}
														/>
													))}
												</div>
												<span className='text-sm ml-1'>{device.rating}</span>
											</div>
											<div className='flex items-center justify-between mt-2'>
												<span className='font-semibold text-brand-dark-300'>{device.price}</span>
												<span className='text-xs text-muted-foreground'>Coming {device.releaseDate}</span>
											</div>
										</div>
									</div>
								</Link>
							))}
					</div>
				</TabsContent>
			</Tabs>
		</div>
	);
}
