import ProductListingPage from "@/components/products/product-listing-page";
import MaxWidthWrapper from "@/components/ui/max-width-wrapper";
import { fetchFromServer } from "@/utils/fetchFromServer";

export async function generateMetadata({ params }: { params: Promise<{ slug: string }> }) {
	const resolvedParams = await params;
	const res = await fetchFromServer(`/brands/slug/${resolvedParams.slug}`);
	return {
		title: `Buy ${res?.data?.name} Products Online in Bangladesh | Exchanger BD`,
		description: `Shop for the latest ${res?.data?.name} products at Exchanger BD. Find the best deals and offers on ${res?.data?.name} products in Bangladesh.`
	};
}

const Page = async ({
	params,
	searchParams
}: {
	params: Promise<{ slug: string }>;
	searchParams: Promise<{ [key: string]: string | string[] | undefined }>;
}) => {
	const resolvedParams = await params;
	const resolvedSearchParams = await searchParams;
	return (
		<MaxWidthWrapper>
			<ProductListingPage params={resolvedParams} searchParams={resolvedSearchParams} from='brand' />
		</MaxWidthWrapper>
	);
};

export default Page;
