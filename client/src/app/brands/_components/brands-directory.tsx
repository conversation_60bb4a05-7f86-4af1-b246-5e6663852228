"use client";

import type React from "react";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import type { I<PERSON>rand } from "@/interfaces";
import { cn } from "@/lib/utils";
import { Search } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useEffect, useState } from "react";

type SortOption = "alphabetical" | "popularity" | "products";

export default function BrandsDirectory({ res }: any) {
	const [searchQuery, setSearchQuery] = useState("");
	const [sortOption, setSortOption] = useState<SortOption>("alphabetical");
	const [activeLetterFilters, setActiveLetterFilters] = useState<string[]>([]);
	const [filteredBrands, setFilteredBrands] = useState(res.data);
	const brandData = res.data;

	// Alphabet array for letter navigation
	const alphabet = "ABCDEFGHIJKLMNOPQRSTUVWXYZ".split("");

	// Get all letters that have brands from the original data
	const availableLettersFromOriginal = brandData.reduce((letters: Set<string>, brand: IBrand) => {
		letters.add(brand.name.charAt(0).toUpperCase());
		return letters;
	}, new Set<string>());

	// Apply filters and sorting
	useEffect(() => {
		let result = [...brandData];

		// Apply search filter
		if (searchQuery) {
			const query = searchQuery.toLowerCase();
			result = result.filter((brand) => brand.name.toLowerCase().includes(query));
		}

		// Apply letter filters (multiple)
		if (activeLetterFilters.length > 0) {
			result = result.filter((brand) => activeLetterFilters.includes(brand.name.charAt(0).toUpperCase()));
		}

		// Apply sorting
		switch (sortOption) {
			case "alphabetical":
				result.sort((a, b) => a.name.localeCompare(b.name));
				break;
			case "popularity":
				result.sort((a, b) => (b.popularity || 0) - (a.popularity || 0));
				break;
			case "products":
				result.sort((a, b) => (b.productCount || 0) - (a.productCount || 0));
				break;
		}

		setFilteredBrands(result);
	}, [searchQuery, sortOption, activeLetterFilters, brandData]);

	// Group brands by first letter
	const groupedBrands = filteredBrands.reduce((acc: any, brand: IBrand) => {
		const firstLetter = brand.name.charAt(0).toUpperCase();
		if (!acc[firstLetter]) {
			acc[firstLetter] = [];
		}
		acc[firstLetter].push(brand);
		return acc;
	}, {} as Record<string, typeof brandData>);

	// Get all letters that have brands in the filtered results (for display)
	const availableLetters = Object.keys(groupedBrands).sort();

	const handleLetterClick = (letter: string) => {
		setActiveLetterFilters((prev) => {
			// If letter is already selected, remove it
			if (prev.includes(letter)) {
				return prev.filter((l) => l !== letter);
			}
			// Otherwise add it to the selection
			return [...prev, letter];
		});
	};

	const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		setSearchQuery(e.target.value);
		setActiveLetterFilters([]); // Reset letter filters when searching
	};

	const handleSortChange = (value: string) => {
		setSortOption(value as SortOption);
	};

	const clearFilters = () => {
		setSearchQuery("");
		setActiveLetterFilters([]);
		setSortOption("alphabetical");
	};

	return (
		<div>
			{/* Filters and Search */}
			<div className='mb-8 flex flex-col md:flex-row gap-4'>
				<div className='relative flex-1'>
					<Input
						type='search'
						placeholder='Search brands...'
						value={searchQuery}
						onChange={handleSearchChange}
						className='pl-10'
					/>
					<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400' />
				</div>
				<div className='flex gap-2'>
					<Select value={sortOption} onValueChange={handleSortChange}>
						<SelectTrigger className='w-[180px]'>
							<SelectValue placeholder='Sort by' />
						</SelectTrigger>
						<SelectContent>
							<SelectItem value='alphabetical'>Alphabetical</SelectItem>
							<SelectItem value='popularity'>Popularity</SelectItem>
							{/* <SelectItem value='products'>Product Count</SelectItem> */}
						</SelectContent>
					</Select>
					<Button variant='outline' onClick={clearFilters}>
						Clear
					</Button>
				</div>
			</div>

			{/* Alphabet Navigation */}
			<div className='mb-8 border-b border-t py-4'>
				<div className='flex flex-wrap justify-center gap-1 md:gap-2'>
					{alphabet.map((letter) => {
						const hasProducts = availableLettersFromOriginal.has(letter);
						return (
							<Button
								key={letter}
								variant='ghost'
								size='sm'
								className={cn(
									"min-w-8 h-8 px-2 font-medium",
									activeLetterFilters.includes(letter) && "bg-brand text-brand-black",
									!hasProducts && "opacity-40 cursor-not-allowed"
								)}
								disabled={!hasProducts}
								onClick={() => handleLetterClick(letter)}
							>
								{letter}
							</Button>
						);
					})}
				</div>
			</div>

			{/* Results Summary */}
			<div className='mb-4 text-sm text-gray-500'>
				{filteredBrands.length} brands found
				{activeLetterFilters.length > 0 &&
					` starting with ${
						activeLetterFilters.length === 1
							? `'${activeLetterFilters[0]}'`
							: `'${activeLetterFilters.join("', '")}' (${activeLetterFilters.length} selected)`
					}`}
				{searchQuery && ` matching '${searchQuery}'`}
			</div>

			{/* Brands Directory */}
			{filteredBrands.length === 0 ? (
				<div className='text-center py-12'>
					<p className='text-lg text-gray-500'>No brands found matching your criteria.</p>
					<Button variant='link' onClick={clearFilters} className='mt-2'>
						Clear filters
					</Button>
				</div>
			) : (
				<div className='space-y-12'>
					{availableLetters.map((letter) => (
						<section key={letter} id={`section-${letter}`} className='scroll-mt-20'>
							<h2 className='text-3xl font-bold mb-4 border-b pb-2'>{letter}</h2>
							<div className='grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-6 gap-4'>
								{groupedBrands[letter].map((brand: IBrand) => (
									<Link
										key={brand._id}
										href={`/brands/${brand.slug}`}
										className='flex flex-col items-center p-3 border rounded-lg hover:shadow-md transition-shadow'
									>
										<div className='relative h-12 w-24 mb-2'>
											<Image
												src={brand.image || `/placeholder.svg?height=48&width=96&query=${brand.name}`}
												alt={brand.name}
												fill
												className='object-contain'
											/>
										</div>
										<span className='text-sm text-center font-medium'>{brand.name}</span>
									</Link>
								))}
							</div>
						</section>
					))}
				</div>
			)}
		</div>
	);
}
