import ShopByBrands from "@/components/Home/all-brand";
import CompanyOverview from "@/components/Home/company-overview";
import FeaturedCategories from "@/components/Home/featured-categories";
import FeatureNavigation from "@/components/Home/featured-navigation";
import FeaturedProducts from "@/components/Home/featured-products";
import HeroSection from "@/components/Home/HeroSection";
import NewArrival from "@/components/Home/new-arrival";
import ProductBanner from "@/components/Home/product-banner-section";
import ProductSlider from "@/components/Home/product-slider";
import TopBrandProducts from "@/components/Home/top-brand-products";
import { fetchFromServer } from "@/utils/fetchFromServer";

// Add revalidation to enable ISR
export const revalidate = 3600; // Revalidate every hour

const Page = async () => {
	try {
		// Fetch data with proper error handling
		const [productsResponse, bannersResponse] = await Promise.allSettled([
			fetchFromServer(`products/minimal`),
			fetchFromServer(`banners`)
		]);

		// Handle the responses safely
		const products = productsResponse.status === "fulfilled" ? productsResponse.value?.data || [] : [];
		const banners = bannersResponse.status === "fulfilled" ? bannersResponse.value?.data || [] : [];

		const staticBanners = banners.filter((banner: any) => ["static-1", "static-2"].includes(banner.type)) || [];

		return (
			<div>
				<HeroSection />
				<FeatureNavigation />
				<FeaturedCategories />
				<ProductSlider products={products} />
				<FeaturedProducts products={products} />
				<ProductBanner banners={staticBanners} />
				<NewArrival products={products} />
				<TopBrandProducts />
				<ShopByBrands />
				<CompanyOverview />
			</div>
		);
	} catch (error) {
		console.error("Error rendering home page:", error);
		// Provide a fallback UI to prevent build failures
		return (
			<div>
				<HeroSection />
				<FeatureNavigation />
				<FeaturedCategories />
				<CompanyOverview />
			</div>
		);
	}
};

export default Page;
