"use client";

import { useEffect, useState } from "react";
import { useSearchParams } from "next/navigation";
import Link from "next/link";
import { Button } from "@/components/ui/button";
import { CheckCircle2 } from "lucide-react";

export default function OrderSuccessPage() {
	const searchParams = useSearchParams();
	const orderNumber = searchParams.get("order");
	const [countdown, setCountdown] = useState(5);

	// Countdown effect to redirect after 5 seconds
	useEffect(() => {
		if (countdown > 0) {
			const timer = setTimeout(() => setCountdown(countdown - 1), 1000);
			return () => clearTimeout(timer);
		} else {
			window.location.href = "/";
		}
	}, [countdown]);

	return (
		<div className='min-h-screen bg-gray-50 flex flex-col items-center justify-center p-4'>
			<div className='bg-white p-8 rounded-lg shadow-sm max-w-md w-full text-center'>
				<div className='mb-6'>
					<CheckCircle2 className='h-16 w-16 text-green-500 mx-auto' />
				</div>

				<h1 className='text-2xl font-bold text-brand-black mb-2'>Order Placed Successfully!</h1>

				<p className='text-gray-600 mb-4'>
					Thank you for your order. We have received your order and will process it soon.
				</p>

				{orderNumber && (
					<div className='bg-brand/5 p-4 rounded-lg mb-6'>
						<p className='text-sm text-gray-500 mb-1'>Your Order Number</p>
						<p className='text-lg font-bold text-brand'>{orderNumber}</p>
					</div>
				)}

				<p className='text-sm text-gray-500 mb-6'>
					You will receive an order confirmation email with details of your order.
				</p>

				<div className='space-y-3'>
					<Link href='/'>
						<Button className='w-full bg-brand hover:bg-brand-darker'>Continue Shopping</Button>
					</Link>

					<p className='text-xs text-gray-400'>Redirecting to home page in {countdown} seconds...</p>
				</div>
			</div>
		</div>
	);
}
