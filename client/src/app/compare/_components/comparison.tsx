"use client";

import { IProduct } from "@/interfaces";
import { useGetProductBySlugQuery } from "@/redux/apis/productApis";
import { useSearchParams } from "next/navigation";
import { useEffect, useState } from "react";
import ProductFeatures from "./product-features";
import ProductSearch from "./product-search";

export default function ProductComparison() {
	const [selectedProducts, setSelectedProducts] = useState<Array<IProduct | null>>([null, null, null]);

	const searchParams = useSearchParams();
	const deviceSlug = searchParams.get("device");

	const { data: fetchedProduct, isSuccess, isLoading } = useGetProductBySlugQuery(deviceSlug!, { skip: !deviceSlug });

	// If device param exists and product is fetched, insert it in the first slot
	useEffect(() => {
		if (isSuccess && fetchedProduct?.data?._id) {
			setSelectedProducts((prev) => [fetchedProduct.data, prev[1], prev[2]]);
		}
	}, [isSuccess, fetchedProduct]);

	const handleProductSelect = (product: IProduct, index: number) => {
		const updated = [...selectedProducts];
		updated[index] = product;
		setSelectedProducts(updated);
	};

	return (
		<div>
			<div className='mb-8'>
				<h1 className='h1-24-black mb-2'>Product Comparison</h1>
				<p className='p-14-black text-gray-600'>
					Find and select products to see the differences and similarities between them
				</p>
			</div>

			<div className='grid grid-cols-1 md:grid-cols-3 gap-4'>
				{selectedProducts.map((_, index) => (
					<ProductSearch
						key={index}
						selectedProduct={selectedProducts[index]}
						onSelect={(product) => handleProductSelect(product, index)}
					/>
				))}
			</div>

			{selectedProducts.some((p) => p !== null) && (
				<div className='mt-8'>
					<ProductFeatures selectedProducts={selectedProducts} />
				</div>
			)}
		</div>
	);
}
