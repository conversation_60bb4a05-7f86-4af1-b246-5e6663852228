"use client";

import type React from "react";

import { useGetProductsQuery } from "@/redux/apis/productApis";
import { Search, X } from "lucide-react";
import Image from "next/image";
import { useEffect, useRef, useState } from "react";

interface ProductSearchProps {
	selectedProduct: any;
	onSelect: (product: any) => void;
}

export default function ProductSearch({ selectedProduct, onSelect }: ProductSearchProps) {
	const [searchQuery, setSearchQuery] = useState("");
	const { data } = useGetProductsQuery([{ name: "search", value: searchQuery }]);
	const [isFocused, setIsFocused] = useState(false);
	const searchRef = useRef<HTMLDivElement>(null);

	useEffect(() => {
		if (selectedProduct) {
			setSearchQuery(selectedProduct.name);
		}
	}, [selectedProduct]);

	useEffect(() => {
		const handleClickOutside = (event: MouseEvent) => {
			if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
				setIsFocused(false);
			}
		};

		document.addEventListener("mousedown", handleClickOutside);
		return () => {
			document.removeEventListener("mousedown", handleClickOutside);
		};
	}, []);

	const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		setSearchQuery(e.target.value);
		if (!isFocused) {
			setIsFocused(true);
		}
		if (e.target.value === "" && selectedProduct) {
			onSelect(null);
		}
	};

	const handleProductSelect = (product) => {
		onSelect(product);
		setIsFocused(false);
	};

	const handleClearSearch = () => {
		setSearchQuery("");
		onSelect(null);
	};
	return (
		<div ref={searchRef} className='relative'>
			<div className='relative'>
				<input
					type='text'
					placeholder='Search and Select Product'
					value={searchQuery}
					onChange={handleSearchChange}
					onFocus={() => setIsFocused(true)}
					className='w-full border border-gray-300 rounded-md py-2 pl-10 pr-10 focus:outline-none focus:ring-2 focus:ring-brand focus:border-transparent'
				/>
				<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-5 w-5' />
				{searchQuery && (
					<button
						onClick={handleClearSearch}
						className='absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600'
					>
						<X className='h-5 w-5' />
					</button>
				)}
			</div>

			{isFocused && data?.data?.length > 0 && (
				<div className='absolute z-10 w-full mt-1 bg-white border border-gray-200 rounded-md shadow-lg max-h-60 overflow-y-auto'>
					{data?.data?.map((product) => (
						<div
							key={product._id}
							className='flex items-center p-3 hover:bg-gray-100 cursor-pointer'
							onClick={() => handleProductSelect(product)}
						>
							<div className='w-10 h-10 relative mr-3 flex-shrink-0'>
								<Image
									src={product.images[0] || `/placeholder.svg?height=40&width=40&query=${product.name}`}
									alt={product.name}
									fill
									className='object-contain'
								/>
							</div>
							<div>
								<div className='font-medium text-sm'>{product.name}</div>
								<div className='text-xs text-gray-500'>{product?.brand?.name || "N/A"}</div>
							</div>
						</div>
					))}
				</div>
			)}

			{selectedProduct && (
				<div className='mt-2 p-3 border border-gray-200 rounded-md bg-gray-50'>
					<div className='flex items-center'>
						<div className='w-12 h-12 relative mr-3 flex-shrink-0'>
							<Image
								src={selectedProduct.images[0] || `/placeholder.svg?height=48&width=48&query=${selectedProduct.name}`}
								alt={selectedProduct.name}
								fill
								className='object-contain'
							/>
						</div>
						<div>
							<div className='font-medium'>{selectedProduct.name}</div>
							<div className='text-sm text-gray-500'>{selectedProduct?.brand?.name}</div>
						</div>
					</div>
				</div>
			)}
		</div>
	);
}
