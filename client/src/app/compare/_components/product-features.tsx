// import { IProduct } from "@/interfaces";
// import Image from "next/image";

// interface ProductFeaturesProps {
// 	selectedProducts: Array<IProduct | null>;
// }

// export default function ProductFeatures({ selectedProducts }: ProductFeaturesProps) {
// 	// Get all unique feature keys from selected products
// 	const allFeatureKeys = new Set<string>();
// 	selectedProducts.forEach((product) => {
// 		if (product) {
// 			product.additionalInfo.forEach((info) => allFeatureKeys.add(info.key));
// 		}
// 	});

// 	const featureKeys = Array.from(allFeatureKeys);
// 	console.log({ selectedProducts });

// 	return (
// 		<div className='border rounded-lg overflow-hidden'>
// 			<div className='bg-gray-50 p-4 border-b'>
// 				<h2 className='h1-20-black'>Product Features Comparison</h2>
// 			</div>

// 			<div className='grid grid-cols-4'>
// 				{/* Feature names column */}
// 				<div className='col-span-1 border-r'>
// 					<div className='p-4 bg-gray-50 border-b h-24 flex items-center justify-center'>
// 						<span className='p-14-black font-medium'>Features</span>
// 					</div>
// 					{featureKeys.map((key) => (
// 						<div key={key} className='p-4 border-b'>
// 							<span className='p-14-black capitalize'>{key}</span>
// 						</div>
// 					))}
// 				</div>

// 				{/* Product columns */}
// 				{selectedProducts.map((product, index) => (
// 					<div key={index} className={`col-span-1 ${index < selectedProducts.length - 1 ? "border-r" : ""}`}>
// 						{product ? (
// 							<>
// 								<div className='p-4 border-b h-24'>
// 									<div className='flex flex-col items-center justify-center h-full'>
// 										<div className='w-12 h-12 relative mb-1'>
// 											<Image
// 												src={product.images[0] || `/placeholder.svg?height=48&width=48&query=${product.name}`}
// 												alt={product.name}
// 												fill
// 												className='object-contain'
// 											/>
// 										</div>
// 										<div className='text-center'>
// 											<div className='p-13-black font-medium truncate max-w-full'>{product.name}</div>
// 											<div className='text-xs text-gray-500'>{product.brand?.name}</div>
// 										</div>
// 									</div>
// 								</div>
// 								{featureKeys.map((key) => (
// 									<div key={key} className={`p-4 border-b ${key === "price" ? "font-semibold text-brand" : ""}`}>
// 										<span className='p-13-black'>
// 											{product.additionalInfo.find((info) => info.key === key)?.value || "-"}
// 										</span>
// 									</div>
// 								))}
// 							</>
// 						) : (
// 							<>
// 								<div className='p-4 border-b h-24 flex items-center justify-center'>
// 									<span className='text-gray-400'>No product selected</span>
// 								</div>
// 								{featureKeys.map((key) => (
// 									<div key={key} className='p-4 border-b'>
// 										<span className='text-gray-400'>-</span>
// 									</div>
// 								))}
// 							</>
// 						)}
// 					</div>
// 				))}
// 			</div>
// 		</div>
// 	);
// }

import { IProduct } from "@/interfaces";
import Image from "next/image";
import { useMemo } from "react";
interface ProductFeaturesProps {
	selectedProducts: Array<IProduct | null>;
}

export default function ProductFeatures({ selectedProducts }: ProductFeaturesProps) {
	// Memoize unique feature keys
	const featureKeys = useMemo(() => {
		const keys = new Set<string>();
		for (const product of selectedProducts) {
			if (product) {
				for (const info of product.additionalInfo) {
					keys.add(info.key);
				}
			}
		}
		return Array.from(keys);
	}, [selectedProducts]);

	return (
		<div className='border rounded-lg overflow-hidden'>
			<div className='bg-gray-50 p-4 border-b'>
				<h2 className='h1-20-black'>Product Features Comparison</h2>
			</div>

			<div className='grid grid-cols-4'>
				{/* Feature name column */}
				<div className='col-span-1 border-r'>
					<div className='p-4 bg-gray-50 border-b h-24 flex items-center justify-center'>
						<span className='p-14-black font-medium'>Features</span>
					</div>
					{featureKeys.map((key) => (
						<div key={key} className='p-4 border-b'>
							<span className='p-14-black capitalize'>{key}</span>
						</div>
					))}
				</div>

				{/* Product comparison columns */}
				{selectedProducts.map((product, index) => {
					const isLast = index === selectedProducts.length - 1;

					return (
						<div key={index} className={`col-span-1 ${!isLast ? "border-r" : ""}`}>
							{product ? (
								<>
									{/* Header */}
									<div className='p-4 border-b h-24'>
										<div className='flex flex-col items-center justify-center h-full'>
											<div className='w-12 h-12 relative mb-1'>
												<Image
													src={product.images[0] || `/placeholder.svg?height=48&width=48&query=${product.name}`}
													alt={product.name}
													fill
													className='object-contain'
												/>
											</div>
											<div className='text-center'>
												<div className='p-13-black font-medium truncate'>{product.name}</div>
												<div className='text-xs text-gray-500'>{product.brand?.name}</div>
											</div>
										</div>
									</div>

									{/* Features */}
									{featureKeys.map((key) => {
										const value = product.additionalInfo.find((info) => info.key === key)?.value || "-";
										return (
											<div key={key} className={`p-4 border-b ${key === "price" ? "font-semibold text-brand" : ""}`}>
												<span className='p-13-black'>{value}</span>
											</div>
										);
									})}
								</>
							) : (
								<>
									<div className='p-4 border-b h-24 flex items-center justify-center text-gray-400'>
										No product selected
									</div>
									{featureKeys.map((key) => (
										<div key={key} className='p-4 border-b text-gray-400'>
											-
										</div>
									))}
								</>
							)}
						</div>
					);
				})}
			</div>
		</div>
	);
}
