"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import MaxWidthWrapper from "@/components/ui/max-width-wrapper";
import { cn } from "@/lib/utils";
import { CheckCircle2, CircleDashed, Clock, Home, Package, Search, ShoppingBag, Truck } from "lucide-react";
import { useState } from "react";

export default function OrderTrackingPage() {
	const [orderId, setOrderId] = useState("184984");
	const [isLoading, setIsLoading] = useState(false);
	const [isTracking, setIsTracking] = useState(true);

	// Mock order details
	const orderDetails = {
		id: "184984",
		date: "April 18, 2025",
		items: 3,
		total: "$245.00",
		estimatedDelivery: "April 23, 2025",
		carrier: "Express Logistics",
		trackingNumber: "EL7391428650"
	};

	// Status steps with more detailed information
	const statuses = [
		{
			id: "order-placed",
			name: "Order Placed",
			description: "Your order has been received and is being processed",
			icon: ShoppingBag,
			completed: true,
			date: "April 18, 2025",
			time: "10:23 AM",
			details: "Payment confirmed via Credit Card"
		},
		{
			id: "order-confirmed",
			name: "Order Confirmed",
			description: "Your order has been confirmed and is being prepared",
			icon: CheckCircle2,
			completed: true,
			date: "April 19, 2025",
			time: "09:45 AM",
			details: "All items are in stock and ready to ship"
		},
		{
			id: "shipping",
			name: "Shipping in Progress",
			description: "Your package is on its way to you",
			icon: Truck,
			completed: true,
			date: "April 20, 2025",
			time: "02:30 PM",
			details: "Package departed from New York distribution center"
		},
		{
			id: "delivered",
			name: "Delivered",
			description: "Your package has been delivered",
			icon: Home,
			completed: false,
			date: "Expected April 23, 2025",
			time: "By end of day",
			details: "Signature may be required upon delivery"
		}
	];

	// Find the current active step
	const currentStep =
		statuses.findIndex((status) => status.completed) === statuses.length - 1
			? statuses.length - 1
			: statuses.findIndex((status) => !status.completed) - 1;

	const handleTrackOrder = () => {
		setIsLoading(true);
		// Simulate API call
		setTimeout(() => {
			setIsLoading(false);
			setIsTracking(true);
		}, 1000);
	};

	return (
		<div className='min-h-screen bg-gradient-to-b from-slate-50 to-slate-100 py-8 md:py-12'>
			<MaxWidthWrapper className=' px-4 max-w-5xl'>
				{/* Header Section */}
				<div className='mb-8 md:mb-12 text-center'>
					<h1 className='text-3xl md:text-4xl font-bold text-brand-black mb-3 tracking-tight'>Order Tracking</h1>
					<p className='text-slate-500 max-w-2xl mx-auto'>Track your order status and estimated delivery time</p>
				</div>

				{/* Order Lookup Section */}
				<div className='bg-white rounded-xl shadow-sm border border-slate-100 p-6 mb-8 transition-all duration-300'>
					<div className='max-w-xl mx-auto'>
						<h2 className='text-lg font-medium text-brand-black mb-4 flex items-center'>
							<Package className='h-5 w-5 mr-2 text-brand-dark-500' />
							Track Your Order
						</h2>

						<div className='flex flex-col sm:flex-row gap-3'>
							<div className='relative flex-grow'>
								<Input
									type='text'
									value={orderId}
									onChange={(e) => setOrderId(e.target.value)}
									className='bg-slate-50 border-slate-200 h-12 pl-4 focus-visible:ring-brand'
									placeholder='Enter your order ID'
								/>
							</div>
							<Button
								className='bg-brand text-brand-black hover:bg-brand/80 h-12 px-5 transition-all duration-300'
								onClick={handleTrackOrder}
								disabled={isLoading}
							>
								{isLoading ? (
									<div className='flex items-center'>
										<div className='h-4 w-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2'></div>
										Tracking...
									</div>
								) : (
									<>
										<Search className='h-5 w-5 mr-2' />
										Track Order
									</>
								)}
							</Button>
						</div>
					</div>
				</div>

				{isTracking && (
					<>
						{/* Order Summary Card */}
						<div className='bg-white rounded-xl shadow-sm border border-slate-100 p-6 mb-8'>
							<div className='flex flex-col md:flex-row justify-between gap-6'>
								<div>
									<h2 className='text-xl font-semibold text-brand-black mb-4'>Order #{orderDetails.id}</h2>
									<div className='grid grid-cols-1 sm:grid-cols-2 gap-x-12 gap-y-4'>
										<div>
											<p className='text-sm text-slate-500'>Order Date</p>
											<p className='font-medium'>{orderDetails.date}</p>
										</div>
										<div>
											<p className='text-sm text-slate-500'>Items</p>
											<p className='font-medium'>{orderDetails.items} products</p>
										</div>
										<div>
											<p className='text-sm text-slate-500'>Total</p>
											<p className='font-medium'>{orderDetails.total}</p>
										</div>
										<div>
											<p className='text-sm text-slate-500'>Estimated Delivery</p>
											<p className='font-medium'>{orderDetails.estimatedDelivery}</p>
										</div>
									</div>
								</div>
								<div className='bg-slate-50 p-4 rounded-lg self-start min-w-[240px]'>
									<p className='text-sm text-slate-500 mb-1'>Shipping Via</p>
									<p className='font-medium'>{orderDetails.carrier}</p>
									<p className='text-sm text-slate-500 mt-3 mb-1'>Tracking Number</p>
									<p className='font-medium text-brand-dark-600'>{orderDetails.trackingNumber}</p>
								</div>
							</div>
						</div>

						{/* Status Tracking - Vertical Timeline */}
						<div className='bg-white rounded-xl shadow-sm border border-slate-100 p-6 mb-8'>
							<h2 className='text-xl font-semibold text-brand-black mb-6'>Shipment Status</h2>

							<div className='relative'>
								{statuses.map((status, index) => {
									const isActive = index === currentStep;
									const isPast = index < currentStep;
									const isFuture = index > currentStep;

									return (
										<div key={status.id} className='relative'>
											<div className='flex items-start gap-4 mb-8'>
												{/* Status Icon */}
												<div
													className={cn(
														"relative flex items-center justify-center w-12 h-12 rounded-full shrink-0",
														isActive
															? "bg-brand-light-200 text-brand-dark-600"
															: isPast
															? "bg-brand text-brand-dark-600"
															: "bg-slate-100 text-slate-400"
													)}
												>
													{status.completed ? (
														<status.icon className='h-6 w-6' />
													) : (
														<CircleDashed className='h-6 w-6' />
													)}

													{/* Connector Line */}
													{index < statuses.length - 1 && (
														<div
															className={cn(
																"absolute top-12 left-1/2 w-0.5 h-8 -translate-x-1/2",
																isPast ? "bg-brand" : "bg-slate-200"
															)}
														></div>
													)}
												</div>

												{/* Status Content */}
												<div
													className={cn(
														"flex-1 transition-all duration-300",
														isActive ? "opacity-100" : isPast ? "opacity-90" : "opacity-60"
													)}
												>
													<div className='flex flex-col sm:flex-row sm:items-center justify-between mb-1'>
														<h3
															className={cn(
																"font-semibold",
																isActive ? "text-brand-dark-700" : isPast ? "text-slate-800" : "text-slate-500"
															)}
														>
															{status.name}
														</h3>
														<div className='text-sm text-slate-500'>
															{status.date} • {status.time}
														</div>
													</div>
													<p className='text-slate-600 mb-2'>{status.description}</p>
													<p className='text-sm text-slate-500'>{status.details}</p>
												</div>
											</div>
										</div>
									);
								})}
							</div>
						</div>

						{/* Estimated Delivery Card */}
						<div className='bg-gradient-to-r from-brand-dark-800 to-brand-dark-900 rounded-xl shadow-md p-6 mb-8 text-brand-light-100'>
							<div className='flex flex-col md:flex-row items-center justify-between'>
								<div>
									<h3 className='text-lg font-medium mb-2'>Estimated Delivery Date</h3>
									<p className='text-2xl font-bold'>{orderDetails.estimatedDelivery}</p>
									<p className='text-emerald-100 mt-1'>Your package is on schedule</p>
								</div>
								<div className='mt-4 md:mt-0'>
									<div className='bg-white/20 backdrop-blur-sm rounded-lg p-4'>
										<Clock className='h-8 w-8 mb-2' />
										<p className='text-sm'>Current Status:</p>
										<p className='font-medium'>{statuses[currentStep + 1]?.name || "Processing"}</p>
									</div>
								</div>
							</div>
						</div>
					</>
				)}

				{/* Help Section */}
				<div className='text-center mt-10'>
					<p className='text-slate-500 text-sm'>
						Need help with your order?{" "}
						<a href='#' className='text-brand-dark-600 hover:text-brand-dark-700 font-medium'>
							Contact our support team
						</a>
					</p>
				</div>
			</MaxWidthWrapper>
		</div>
	);
}
