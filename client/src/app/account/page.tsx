"use client";

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON><PERSON>rigger } from "@/components/ui/tabs";
import { useEffect, useState } from "react";

import MaxWidthWrapper from "@/components/ui/max-width-wrapper";
import { useToast } from "@/hooks/use-toast";
import { useChangePasswordMutation, useGetMeQuery, useUpdateMeMutation } from "@/redux/apis/authApis";
import { logout, selectCurrentToken } from "@/redux/authSlice";
import { useAppDispatch } from "@/redux/hooks";
import { store } from "@/redux/store";
import { useRouter } from "next/navigation";
import { AddressesTab } from "./_components/addresses-tab";
import { OrdersTab } from "./_components/orders-tab";
import { PasswordTab } from "./_components/password-tab";
import { PersonalInfoTab } from "./_components/personal-info-tab";
import { ProfileSidebar } from "./_components/profile-sidebar";

// Define types
export type Address = {
	_id?: string;
	district: string;
	detailsAddress?: string;
	isDefault: boolean;
};

export type UserData = {
	name: string;
	phone: string;
	addresses: Address[];
	currentPassword: string;
	newPassword: string;
	confirmPassword: string;
};

const menuItems = [
	{ id: "personal-info", label: "Personal Info", icon: "user" },
	{ id: "orders", label: "My Orders", icon: "shopping-bag" },
	{ id: "address", label: "Address", icon: "map-pin" },
	{ id: "password", label: "Change Password", icon: "key-round" }
];

export default function AccountProfile() {
	const { toast } = useToast();
	const [activeTab, setActiveTab] = useState("personal-info");
	const { data, refetch } = useGetMeQuery(undefined);
	const userData = data?.data || {};
	const [updateMe, { isLoading: isUpdating }] = useUpdateMeMutation();
	const [changePassword, { isLoading: isChangingPassword }] = useChangePasswordMutation();
	console.log({ data, userData });
	// Handle update password
	const handleUpdatePassword = async (currentPassword: string, newPassword: string, confirmPassword: string) => {
		if (newPassword !== confirmPassword) {
			toast({
				title: "Passwords don't match",
				description: "New password and confirm password must match.",
				variant: "destructive"
			});
			return;
		}
		try {
			// Call the mutation with form data
			const response = await changePassword({ oldPassword: currentPassword, newPassword }).unwrap();

			if (response.success) {
				toast({
					title: "Password changed successfully!",
					description: "Your password changed successfully"
				});
			}
		} catch (error) {
			console.error("Failed to changed password:", error);
			toast({
				title: error.data.message || "Failed to changed password",
				description: "There was an error changing password. Please try again.",
				variant: "destructive"
			});
		}
	};

	const dispatch = useAppDispatch();
	const navigate = useRouter();

	const handleLogout = () => {
		toast({
			title: "Logging out",
			description: "You are being logged out of your account."
		});
		dispatch(logout());
		navigate.push("/");
	};

	const onUpdateMe = async (payload: Partial<UserData>) => {
		try {
			// Call the mutation with form data
			const response = await updateMe(payload).unwrap();

			if (response.success) {
				toast({
					title: "User updated successfully!",
					description: "Your profile has been updated successfully"
				});
				refetch();
			}
		} catch (error) {
			console.error("Failed to update user:", error);
			toast({
				title: "Failed to update user",
				description: "There was an error updating user. Please try again.",
				variant: "destructive"
			});
		}
	};

	useEffect(() => {
		if (!selectCurrentToken(store.getState())) {
			navigate.push("/");
		}
	}, [selectCurrentToken(store.getState())]);

	return (
		<MaxWidthWrapper className=' py-8 px-4 md:px-6 min-h-screen'>
			<div className='max-w-7xl mx-auto'>
				<div className='mb-8'>
					<h1 className='text-3xl font-bold tracking-tight text-brand-black'>Account Dashboard</h1>
					<p className='text-muted-foreground mt-1 text-brand-black'>Manage your account settings and preferences</p>
				</div>

				<div className='grid grid-cols-1 md:grid-cols-12 gap-8'>
					{/* Sidebar */}
					<div className='md:col-span-3'>
						<ProfileSidebar
							activeTab={activeTab}
							setActiveTab={setActiveTab}
							menuItems={menuItems}
							userData={userData}
							onLogout={handleLogout}
						/>
					</div>

					{/* Main Content */}
					<div className='md:col-span-9'>
						<Tabs value={activeTab} onValueChange={setActiveTab} className='w-full'>
							<TabsList className='hidden'>
								{menuItems.map((item) => (
									<TabsTrigger key={item.id} value={item.id}>
										{item.label}
									</TabsTrigger>
								))}
							</TabsList>

							{/* Personal Info Tab */}
							<TabsContent value='personal-info'>
								<PersonalInfoTab userData={userData} onUpdateMe={onUpdateMe} isUpdating={isUpdating} />
							</TabsContent>

							{/* Orders Tab */}
							<TabsContent value='orders'>
								<OrdersTab />
							</TabsContent>

							{/* Address Tab */}
							<TabsContent value='address'>
								<AddressesTab addresses={userData?.addresses} onUpdateMe={onUpdateMe} isUpdating={isUpdating} />
							</TabsContent>

							{/* Change Password Tab */}
							<TabsContent value='password'>
								<PasswordTab isChangingPassword={isChangingPassword} onUpdatePassword={handleUpdatePassword} />
							</TabsContent>
						</Tabs>
					</div>
				</div>
			</div>
		</MaxWidthWrapper>
	);
}
