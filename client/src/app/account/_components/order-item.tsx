import { Package } from "lucide-react";

interface OrderItemProps {
	item: {
		_id: string;
		productId: {
			name: string;
		};
		quantity: number;
		price: number;
		image: string;
	};
}

export function OrderItem({ item }: OrderItemProps) {
	return (
		<div className='flex items-center justify-between'>
			<div className='flex items-center space-x-3'>
				<div className='w-12 h-12 bg-gray-200 rounded-md flex items-center justify-center'>
					<Package className='h-6 w-6 text-gray-500' />
				</div>
				<div>
					<p className='font-medium'>{item.productId.name}</p>
					<p className='text-sm text-gray-500'>Qty: {item.quantity}</p>
				</div>
			</div>
			<p className='font-medium'>${item.price.toFixed(2)}</p>
		</div>
	);
}
