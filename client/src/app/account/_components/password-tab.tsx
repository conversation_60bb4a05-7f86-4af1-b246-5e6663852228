"use client";

import type React from "react";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { KeyRound } from "lucide-react";
import { useState } from "react";
import { TabHeader } from "./tab-header";

interface PasswordTabProps {
	isChangingPassword: boolean;
	onUpdatePassword: (currentPassword: string, newPassword: string, confirmPassword: string) => void;
}

export function PasswordTab({ isChangingPassword, onUpdatePassword }: PasswordTabProps) {
	const [formData, setFormData] = useState({
		currentPassword: "",
		newPassword: "",
		confirmPassword: ""
	});

	const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const { name, value } = e.target;
		setFormData({
			...formData,
			[name]: value
		});
	};

	const handleSubmit = (e: React.FormEvent) => {
		e.preventDefault();
		onUpdatePassword(formData.currentPassword, formData.newPassword, formData.confirmPassword);
	};

	return (
		<Card className='border-none shadow-lg'>
			<TabHeader
				icon={<KeyRound className='h-5 w-5 text-brand-dark-300 mr-2' />}
				title='Change Password'
				description='Update your password to keep your account secure.'
			/>
			<form onSubmit={handleSubmit}>
				<CardContent className='space-y-6 pt-6'>
					<div className='space-y-2'>
						<Label htmlFor='currentPassword' className='text-sm font-medium'>
							Current Password
						</Label>
						<Input
							id='currentPassword'
							name='currentPassword'
							type='password'
							value={formData.currentPassword}
							onChange={handleInputChange}
							className='border-gray-300 focus:border-brand-light-500 focus:ring-brand-light-500'
							placeholder='Current password'
						/>
					</div>
					<div className='space-y-2'>
						<Label htmlFor='newPassword' className='text-sm font-medium'>
							New Password
						</Label>
						<Input
							id='newPassword'
							name='newPassword'
							type='password'
							value={formData.newPassword}
							onChange={handleInputChange}
							className='border-gray-300 focus:border-brand-light-500 focus:ring-brand-light-500'
							placeholder='New password'
						/>
					</div>
					<div className='space-y-2'>
						<Label htmlFor='confirmPassword' className='text-sm font-medium'>
							Confirm New Password
						</Label>
						<Input
							id='confirmPassword'
							name='confirmPassword'
							type='password'
							value={formData.confirmPassword}
							onChange={handleInputChange}
							className='border-gray-300 focus:border-brand-light-500 focus:ring-brand-light-500'
							placeholder='Confirm new password'
						/>
					</div>
				</CardContent>
				<CardFooter className='flex justify-end bg-gray-50 border-t pt-4'>
					<Button
						disabled={isChangingPassword}
						type='submit'
						className='bg-brand hover:bg-brand/80 text-brand-black shadow-md'
					>
						{isChangingPassword ? "Updating" : "Update Password"}
					</Button>
				</CardFooter>
			</form>
		</Card>
	);
}
