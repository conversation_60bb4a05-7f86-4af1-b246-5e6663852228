"use client";

import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { useGetUserOrdersQuery } from "@/redux/apis/orderApis";
import { Calendar, CreditCard, MapPin, ShoppingBag } from "lucide-react";
import { OrderItem } from "./order-item";
import { TabHeader } from "./tab-header";

export function OrdersTab() {
	const { data: ordersData, isLoading, error } = useGetUserOrdersQuery(undefined);

	// Map API orders to frontend format

	// Get status badge styling
	const getStatusBadge = (status: string) => {
		switch (status) {
			case "delivered":
				return { bg: "bg-green-100 text-green-800 hover:bg-green-100", text: "Delivered" };
			case "shipped":
				return { bg: "bg-blue-100 text-blue-800 hover:bg-blue-100", text: "Shipped" };
			case "processing":
				return { bg: "bg-amber-100 text-amber-800 hover:bg-amber-100", text: "Processing" };
			case "cancelled":
				return { bg: "bg-red-100 text-red-800 hover:bg-red-100", text: "Cancelled" };
			default:
				return { bg: "bg-gray-100 text-gray-800 hover:bg-gray-100", text: status };
		}
	};

	// Loading skeleton
	if (isLoading) {
		return (
			<Card className='border-none shadow-lg'>
				<TabHeader
					icon={<ShoppingBag className='h-5 w-5 text-brand-dark-300 mr-2' />}
					title='My Orders'
					description='View and track all your orders here.'
				/>
				<CardContent className='p-6 space-y-6'>
					{[1, 2, 3].map((item) => (
						<div key={item} className='space-y-4'>
							<div className='flex justify-between'>
								<Skeleton className='h-6 w-[100px]' />
								<Skeleton className='h-6 w-[120px]' />
							</div>
							<Skeleton className='h-[180px] w-full rounded-lg' />
							<div className='flex justify-end space-x-3'>
								<Skeleton className='h-9 w-[100px]' />
								<Skeleton className='h-9 w-[100px]' />
							</div>
						</div>
					))}
				</CardContent>
			</Card>
		);
	}

	// Error state
	if (error) {
		return (
			<Card className='border-none shadow-lg'>
				<TabHeader
					icon={<ShoppingBag className='h-5 w-5 text-brand-dark-300 mr-2' />}
					title='My Orders'
					description='View and track all your orders here.'
				/>
				<CardContent className='p-8 text-center'>
					<ShoppingBag className='h-12 w-12 text-red-300 mx-auto mb-3' />
					<h3 className='text-lg font-medium mb-1'>Unable to load orders</h3>
					<p className='text-gray-500'>There was a problem loading your orders. Please try again later.</p>
				</CardContent>
			</Card>
		);
	}

	return (
		<Card className='border-none shadow-lg'>
			<TabHeader
				icon={<ShoppingBag className='h-5 w-5 text-brand-dark-300 mr-2' />}
				title='My Orders'
				description='View and track all your orders here.'
			/>
			<CardContent className='p-0'>
				{!ordersData?.data || ordersData?.data?.length === 0 ? (
					<div className='p-8 text-center'>
						<ShoppingBag className='h-12 w-12 text-gray-300 mx-auto mb-3' />
						<h3 className='text-lg font-medium mb-1'>No orders found</h3>
						<p className='text-gray-500'>You haven&apos;t placed any orders yet.</p>
					</div>
				) : (
					ordersData?.data?.map((order, index) => (
						<div key={order._id} className={`border-b last:border-b-0 hover:bg-gray-50 transition-colors`}>
							<div className='p-6'>
								<div className='flex flex-col space-y-4'>
									{/* Order header */}
									<div className='flex flex-wrap items-center justify-between gap-4'>
										<div className='flex items-center space-x-2'>
											<h3 className='font-semibold text-lg'>{order.orderNumber}</h3>
											<Badge className={getStatusBadge(order.status).bg}>{getStatusBadge(order.status).text}</Badge>
										</div>
										<div className='text-sm text-gray-500 flex items-center'>
											<Calendar className='h-4 w-4 mr-1' />
											{new Date(order.date).toLocaleDateString("en-US", {
												year: "numeric",
												month: "short",
												day: "numeric"
											})}
										</div>
									</div>

									{/* Order details */}
									<div className='bg-gray-50 rounded-lg p-4'>
										{/* Order items */}
										<div className='space-y-3'>
											{order.items.map((item) => (
												<OrderItem key={item._id} item={item} />
											))}
										</div>

										{/* Order summary */}
										<div className='mt-4 pt-4 border-t border-gray-200'>
											<div className='flex justify-between items-center'>
												<div className='text-sm text-gray-500'>
													<div className='flex items-center mb-1'>
														<MapPin className='h-4 w-4 mr-1' />
														<span>Shipping to:</span>
													</div>
													<p className='ml-5'>{order.shippingAddress}</p>
												</div>
												<div className='text-right'>
													<div className='flex items-center justify-end text-sm text-gray-500 mb-1'>
														<CreditCard className='h-4 w-4 mr-1' />
														<span>{order.paymentMethod}</span>
													</div>
													<p className='font-semibold text-lg'>Total: ${order.totalAmount.toFixed(2)}</p>
												</div>
											</div>
										</div>
									</div>

									{/* Order actions */}
									<div className='flex justify-end space-x-3'>
										<Button variant='outline' size='sm' className='border-gray-300'>
											Track Order
										</Button>
										<Button
											variant='outline'
											size='sm'
											className='border-brand bg-brand-light-200 text-brand-black hover:bg-brand-light-100 hover:text-brand-black'
										>
											View Details
										</Button>
									</div>
								</div>
							</div>
						</div>
					))
				)}
			</CardContent>
		</Card>
	);
}
