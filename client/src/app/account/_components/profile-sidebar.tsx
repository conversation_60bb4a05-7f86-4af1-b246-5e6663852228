"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { ChevronRight, KeyRound, LogOut, MapPin, ShoppingBag, User } from "lucide-react";
import { UserData } from "../page";

interface ProfileSidebarProps {
	activeTab: string;
	setActiveTab: (tab: string) => void;
	menuItems: { id: string; label: string; icon: string }[];
	userData: UserData;
	onLogout: () => void;
}

export function ProfileSidebar({ activeTab, setActiveTab, menuItems, userData, onLogout }: ProfileSidebarProps) {
	// Function to render the appropriate icon
	const renderIcon = (iconName: string, isActive: boolean) => {
		const className = `h-5 w-5 ${isActive ? "text-brand-dark-300" : "text-gray-500"}`;

		switch (iconName) {
			case "user":
				return <User className={className} />;
			case "shopping-bag":
				return <ShoppingBag className={className} />;
			case "map-pin":
				return <MapPin className={className} />;
			case "key-round":
				return <KeyRound className={className} />;
			default:
				return <User className={className} />;
		}
	};

	return (
		<Card className='overflow-hidden border-none shadow-lg'>
			<CardHeader className='bg-white border-b'>
				<div className='space-y-1'>
					<CardTitle className='text-xl font-semibold text-brand-black'>{userData.name}</CardTitle>
					<CardDescription>
						<Badge variant='secondary' className='bg-brand-light-200 text-brand-black  hover:bg-brand-ligt-100'>
							Premium Member
						</Badge>
					</CardDescription>
				</div>
			</CardHeader>
			<CardContent className='p-0'>
				<nav className='flex flex-col'>
					{menuItems.map((item) => (
						<Button
							key={item.id}
							variant={activeTab === item.id ? "secondary" : "ghost"}
							className={`justify-between h-14 px-4 rounded-none border-l-4 ${
								activeTab === item.id ? "border-l-brand bg-orange-50 text-brand-dark-700" : "border-l-transparent"
							}`}
							onClick={() => setActiveTab(item.id)}
						>
							<div className='flex items-center'>
								<span className='mr-3'>{renderIcon(item.icon, activeTab === item.id)}</span>
								{item.label}
							</div>
							<ChevronRight className={`h-4 w-4 ${activeTab === item.id ? "text-brand-dark-300" : "text-gray-300"}`} />
						</Button>
					))}
					<Separator />
					<Button
						variant='ghost'
						className='justify-start h-14 px-4 text-red-500 hover:text-red-600 hover:bg-red-50 rounded-none'
						onClick={onLogout}
					>
						<span className='mr-3'>
							<LogOut className='h-5 w-5' />
						</span>
						Logout
					</Button>
				</nav>
			</CardContent>
		</Card>
	);
}
