"use client";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Trash2 } from "lucide-react";
import { Address } from "../page";

interface AddressCardProps {
	address: Address;
	onEdit: () => void;
	onDelete: (id: string) => Promise<void>;
	onSetDefault: (id: string) => Promise<void>;
}

export function AddressCard({ address, onEdit, onDelete, onSetDefault }: AddressCardProps) {
	return (
		<div className='p-6 hover:bg-gray-50 transition-colors'>
			<div className='flex flex-col md:flex-row md:items-start md:justify-between gap-4'>
				<div className='space-y-1 flex-grow'>
					<div className='flex items-center gap-2'>
						{address.isDefault && (
							<Badge variant='outline' className='bg-brand-light-200 text-brand-black border-brand-light-500'>
								Default
							</Badge>
						)}
					</div>
					<p className='text-sm'>{address.district}</p>
					<p className='text-sm'>{address.detailsAddress}</p>
				</div>
				<div className='flex items-center gap-2 self-end md:self-start'>
					{!address.isDefault && (
						<Button variant='outline' size='sm' onClick={() => onSetDefault(address?._id as string)}>
							Set as Default
						</Button>
					)}
					{/* <Button variant='outline' size='icon' onClick={onEdit}>
						<Pencil className='h-4 w-4' />
					</Button> */}
					<Button
						variant='outline'
						size='icon'
						className='text-red-500 hover:text-red-600 hover:bg-red-50'
						onClick={() => onDelete(address?._id as string)}
					>
						<Trash2 className='h-4 w-4' />
					</Button>
				</div>
			</div>
		</div>
	);
}
