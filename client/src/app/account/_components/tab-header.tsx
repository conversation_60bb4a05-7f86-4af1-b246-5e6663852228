import type { ReactNode } from "react"
import { CardDescription, CardHeader, CardTitle } from "@/components/ui/card"

interface TabHeaderProps {
  icon: ReactNode
  title: string
  description: string
  action?: ReactNode
}

export function TabHeader({ icon, title, description, action }: TabHeaderProps) {
  return (
    <CardHeader className="border-b bg-gray-50">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          {icon}
          <CardTitle>{title}</CardTitle>
        </div>
        {action}
      </div>
      <CardDescription>{description}</CardDescription>
    </CardHeader>
  )
}
