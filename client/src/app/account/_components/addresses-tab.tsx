"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { MapPin, Plus } from "lucide-react";
import { useState } from "react";

import { Dialog, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { Address, UserData } from "../page";
import { AddressCard } from "./address-card";
import { AddressForm } from "./address-form";
import { TabHeader } from "./tab-header";

interface AddressesTabProps {
	addresses: Address[];
	onUpdateMe: (payload: Partial<UserData>) => Promise<void>;
	isUpdating: boolean;
}

export function AddressesTab({ addresses, onUpdateMe, isUpdating }: AddressesTabProps) {
	const [isAddressModalOpen, setIsAddressModalOpen] = useState(false);
	const [editingAddressId, setEditingAddressId] = useState<string | null>(null);
	const deleteAddress = async (id: string) => {
		const newAddresses = addresses?.filter((add) => add._id != id);
		await onUpdateMe({ addresses: newAddresses });
	};
	const setDefaultAddress = async (id: string) => {
		if (!addresses) return;

		// Mark the selected address as default and others as not default
		const updatedAddresses = addresses.map((add) => ({
			...add,
			isDefault: add._id === id
		}));

		await onUpdateMe({ addresses: updatedAddresses });
	};
	return (
		<Card className='border-none shadow-lg'>
			<TabHeader
				icon={<MapPin className='h-5 w-5 text-brand-dark-300 mr-2' />}
				title='My Addresses'
				description='Manage your shipping and billing addresses.'
				action={
					<Dialog open={isAddressModalOpen} onOpenChange={setIsAddressModalOpen}>
						<DialogTrigger asChild>
							<Button
								className='bg-brand hover:bg-brand/80 text-brand-black'
								onClick={() => {
									setEditingAddressId(null);
									setIsAddressModalOpen(true);
								}}
							>
								<Plus className='h-4 w-4 mr-1' /> Add New Address
							</Button>
						</DialogTrigger>
						<DialogContent className='sm:max-w-[600px] max-h-[90vh] overflow-y-auto'>
							<AddressForm addresses={addresses} onUpdateMe={onUpdateMe} isUpdating={isUpdating} />
						</DialogContent>
					</Dialog>
				}
			/>
			<CardContent className='p-0'>
				{addresses.length === 0 ? (
					<div className='p-8 text-center'>
						<MapPin className='h-12 w-12 text-gray-300 mx-auto mb-3' />
						<h3 className='text-lg font-medium mb-1'>No addresses found</h3>
						<p className='text-gray-500 mb-4'>You haven&apos;t added any addresses yet.</p>
						<Button
							className='bg-brand hover:bg-brand/80 text-brand-black'
							onClick={() => {
								setEditingAddressId(null);
								setIsAddressModalOpen(true);
							}}
						>
							<Plus className='h-4 w-4 mr-1' /> Add New Address
						</Button>
					</div>
				) : (
					<div className='divide-y'>
						{addresses.map((address) => (
							<AddressCard
								key={address._id}
								address={address}
								onEdit={() => console.log("on save")}
								onDelete={deleteAddress}
								onSetDefault={setDefaultAddress}
							/>
						))}
					</div>
				)}
			</CardContent>
		</Card>
	);
}
