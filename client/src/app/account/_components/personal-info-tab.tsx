"use client";

import type React from "react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Edit, User } from "lucide-react";
import { useEffect, useState } from "react";
import { UserData } from "../page";
import { TabHeader } from "./tab-header";

interface PersonalInfoTabProps {
	userData: UserData;
	onUpdateMe: (payload: Partial<UserData>) => Promise<void>;
	isUpdating: boolean;
}

export function PersonalInfoTab({ userData, onUpdateMe, isUpdating }: PersonalInfoTabProps) {
	const [formData, setFormData] = useState<Partial<UserData>>({ name: userData.name, phone: userData.phone });
	const [isEditMode, setIsEditMode] = useState<boolean>(false);

	const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const { name, value } = e.target;
		setFormData({
			...formData,
			[name]: value
		});
	};

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		await onUpdateMe({ name: formData.name });
		setIsEditMode(true);
	};
	useEffect(() => {
		setFormData({ name: userData.name, phone: userData.phone });
	}, [userData]);

	return (
		<Card className='border-none shadow-lg'>
			<TabHeader
				icon={<User className='h-5 w-5 text-brand-dark-300 mr-2' />}
				title='Personal Information'
				description='Update your personal details here. Your information is securely stored.'
				action={<Edit onClick={() => setIsEditMode(true)} className='h-5 w-5 text-brand-dark-300 mr-2' />}
			/>
			<form onSubmit={handleSubmit}>
				<CardContent className='space-y-6 pt-6'>
					<div className='grid grid-cols-1 gap-6'>
						<div className='space-y-2'>
							<Label htmlFor='name' className='text-sm font-medium'>
								Name
							</Label>
							<Input
								id='name'
								name='name'
								value={formData.name}
								readOnly={!isEditMode}
								onChange={handleInputChange}
								className='border-gray-300 focus:border-brand-light-500 focus:ring-brand-light-500 focus:border-0'
							/>
						</div>
					</div>
					<div className='space-y-2'>
						<Label htmlFor='phone' className='text-sm font-medium'>
							Mobile Number
						</Label>
						<Input
							id='phone'
							name='phone'
							value={formData.phone}
							disabled
							onChange={handleInputChange}
							className='border-gray-300 focus:border-brand-light-500 focus:ring-brand-light-500 focus:border-0'
						/>
					</div>
				</CardContent>
				{isEditMode && (
					<CardFooter className='flex justify-end bg-gray-50 border-t'>
						<Button
							disabled={isUpdating}
							type='submit'
							className='bg-brand hover:bg-brand/80 text-brand-black shadow-md mt-6'
						>
							{isUpdating ? "Updating" : "Update Profile"}
						</Button>
					</CardFooter>
				)}
			</form>
		</Card>
	);
}
