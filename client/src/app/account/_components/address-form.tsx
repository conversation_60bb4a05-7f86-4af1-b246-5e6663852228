"use client";

import type React from "react";

import { Button } from "@/components/ui/button";
import { DialogClose, DialogDescription, Di<PERSON><PERSON>ooter, Di<PERSON><PERSON>eader, DialogTitle } from "@/components/ui/dialog";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { districts } from "@/constants";
import { useState } from "react";
import { Address } from "../page";

interface AddressFormProps {
	addresses: any;
	isEditMode?: boolean;
	onUpdateMe: any;
	isUpdating: boolean;
}

export function AddressForm({ addresses, isEditMode = false, onUpdateMe, isUpdating }: AddressFormProps) {
	// Address form state
	const [addressFormData, setAddressFormData] = useState<Omit<Address, "id" | "isDefault">>({
		district: "",
		detailsAddress: ""
	});

	// Form validation errors
	const [addressFormErrors, setAddressFormErrors] = useState<Record<string, string>>({});

	// Handle address form input change
	const handleAddressInputChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
		const { name, value } = e.target;
		setAddressFormData({
			...addressFormData,
			[name]: value
		});

		// Clear error for this field if it exists
		if (addressFormErrors[name]) {
			setAddressFormErrors({
				...addressFormErrors,
				[name]: ""
			});
		}
	};

	// Handle select input change
	const handleSelectChange = (name: string, value: string) => {
		setAddressFormData({
			...addressFormData,
			[name]: value
		});

		// Clear error for this field if it exists
		if (addressFormErrors[name]) {
			setAddressFormErrors({
				...addressFormErrors,
				[name]: ""
			});
		}
	};

	// Validate address form
	const validateAddressForm = (): boolean => {
		const errors: Record<string, string> = {};
		// const requiredFields = ["firstName", "lastName", "phoneNumber", "division", "city", "address1"];
		const requiredFields = ["district", "detailsAddress"];

		requiredFields.forEach((field) => {
			if (!addressFormData[field as keyof typeof addressFormData]) {
				errors[field] = "This field is required";
			}
		});

		setAddressFormErrors(errors);
		return Object.keys(errors).length === 0;
	};

	// Handle form submission
	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();

		if (!validateAddressForm()) {
			return;
		}
		await onUpdateMe({ addresses: [...addresses, addressFormData] });
	};

	return (
		<>
			<DialogHeader>
				<DialogTitle>{isEditMode ? "Edit Address" : "Add New Address"}</DialogTitle>
				<DialogDescription>
					{isEditMode
						? "Update your address details below."
						: "Fill in the details to add a new address to your account."}
				</DialogDescription>
			</DialogHeader>
			<form onSubmit={handleSubmit}>
				<div className='grid gap-4 py-4'>
					<div>
						<div className='space-y-2 w-full'>
							<Label htmlFor='district'>District*</Label>
							<Select value={addressFormData.district} onValueChange={(value) => handleSelectChange("district", value)}>
								<SelectTrigger id='district' className={addressFormErrors.district ? "border-red-500" : ""}>
									<SelectValue placeholder='Select district' />
								</SelectTrigger>
								<SelectContent>
									{districts.map((dis, index) => (
										<SelectItem key={index} value={dis.name}>
											{dis.name}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
							{addressFormErrors.division && <p className='text-red-500 text-xs mt-1'>{addressFormErrors.division}</p>}
						</div>
					</div>
					<div className='space-y-2'>
						<Label htmlFor='detailsAddress'>Address*</Label>
						<Textarea
							id='detailsAddress'
							name='detailsAddress'
							placeholder='Enter details address'
							value={addressFormData.detailsAddress}
							onChange={handleAddressInputChange}
							className={addressFormErrors.detailsAddress ? "border-red-500" : ""}
						/>
						{addressFormErrors.detailsAddress && (
							<p className='text-red-500 text-xs mt-1'>{addressFormErrors.detailsAddress}</p>
						)}
					</div>
				</div>
				<DialogFooter>
					<DialogClose>
						<Button variant='outline' type='button'>
							Cancel
						</Button>
					</DialogClose>
					<DialogClose>
						<Button className='bg-brand hover:bg-brand/80 text-brand-black' type='submit'>
							{isEditMode ? "Update Address" : "Add Address"}
						</Button>
					</DialogClose>
				</DialogFooter>
			</form>
		</>
	);
}
