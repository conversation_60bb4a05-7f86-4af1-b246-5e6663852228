"use client";

import type React from "react";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import MaxWidthWrapper from "@/components/ui/max-width-wrapper";
import { Separator } from "@/components/ui/separator";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { useCart } from "@/hooks/useCart";
import { useGetMeQuery } from "@/redux/apis/authApis";
import { useSendOtpMutation, useVerifyOtpMutation } from "@/redux/apis/otpApis";
import { useGetMinimalProductsQuery } from "@/redux/apis/productApis";
import { ArrowLeft, Check, CreditCard, InfoIcon, ShoppingBag, Truck } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import { type FormEvent, useEffect, useMemo, useRef, useState } from "react";
type TCartItem = {
	id: string;
	quantity: number;
	variantId?: string;
};

export default function CheckoutPage() {
	const { cartItems, updateItemQuantity, emptyCart } = useCart();
	const [sendOtp, { isLoading: isSendingOtp }] = useSendOtpMutation();
	const { data: ud, refetch } = useGetMeQuery(undefined);
	const userData = ud?.data || {};
	const [paymentMethod, setPaymentMethod] = useState<"cash" | "online">("cash");
	const [buyNowItem, setBuyNowItem] = useState<TCartItem | null>(null);
	const [couponCode, setCouponCode] = useState("");
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [isOtpModalOpen, setIsOtpModalOpen] = useState(false);
	const [isPhoneVerified, setIsPhoneVerified] = useState(false);
	const { toast } = useToast();
	const router = useRouter();
	const searchParams = useSearchParams();
	const buyNowId = searchParams.get("buyNow");
	const buyNowQuantity = searchParams.get("quantity") || 1;
	const buyNowVariant = searchParams.get("variantId");
	const itemsToCheckout = useMemo(() => {
		return buyNowItem ? [buyNowItem] : cartItems;
	}, [buyNowItem, cartItems]);

	const productIds = itemsToCheckout?.map((item) => item.id).join(",");
	useEffect(() => {
		if (buyNowId) {
			const item: TCartItem = {
				id: buyNowId,
				quantity: Number(buyNowQuantity) || 1
			};

			if (buyNowVariant) {
				item.variantId = buyNowVariant;
			}
			setBuyNowItem(item);
		}
	}, [buyNowId, buyNowQuantity, buyNowVariant]);

	// Fetch product details based on those IDs
	const { data, isFetching } = useGetMinimalProductsQuery([{ name: "_ids", value: productIds }], {
		skip: itemsToCheckout.length === 0
	});

	// Form state
	const [formData, setFormData] = useState({
		fullName: "",
		phoneNumber: "",
		address: "",
		termsAccepted: false
	});

	// Form errors state
	const [errors, setErrors] = useState({
		fullName: "",
		phoneNumber: "",
		address: "",
		termsAccepted: "",
		phoneVerify: ""
	});

	// Handle input change
	const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
		const { id, value } = e.target;
		setFormData((prev) => ({
			...prev,
			[id]: value
		}));

		// Clear error when user types
		if (errors[id as keyof typeof errors]) {
			setErrors((prev) => ({
				...prev,
				[id]: ""
			}));
		}
	};

	// Handle checkbox change
	const handleCheckboxChange = (checked: boolean) => {
		setFormData((prev) => ({
			...prev,
			termsAccepted: checked
		}));

		if (checked) {
			setErrors((prev) => ({
				...prev,
				termsAccepted: ""
			}));
		}
	};

	// Redirect to cart if no items
	if (itemsToCheckout.length === 0) {
		return (
			<div className='min-h-screen bg-gray-50 flex flex-col items-center justify-center p-4'>
				<Card className='w-full max-w-md text-center'>
					<CardHeader>
						<div className='mx-auto bg-gray-100 w-16 h-16 rounded-full flex items-center justify-center mb-2'>
							<ShoppingBag className='h-8 w-8 text-gray-500' />
						</div>
						<CardTitle className='text-xl'>Your cart is empty</CardTitle>
						<CardDescription>Please add some items to your cart before checkout</CardDescription>
					</CardHeader>
					<CardFooter className='flex justify-center'>
						<Link href='/'>
							<Button className='bg-brand hover:bg-brand-darker'>
								<ArrowLeft className='mr-2 h-4 w-4' />
								Continue Shopping
							</Button>
						</Link>
					</CardFooter>
				</Card>
			</div>
		);
	}

	// Calculate values
	const deliveryCharge = 0; // Will be calculated later
	// const discount = 0;
	// const subtotal = 0;
	// const total = subtotal + deliveryCharge - discount;
	const products = data?.data || [];

	const totals = products.reduce(
		(acc, product) => {
			const item = itemsToCheckout.find((ci) => ci.id === product._id);
			if (!item) return acc;

			const qty = item.quantity || 1;
			acc.totalPrice += product.price * qty;
			acc.totalActualPrice += product.actualPrice * qty;

			return acc;
		},
		{ totalPrice: 0, totalActualPrice: 0 }
	);
	const enrichedCartItems = products.map((product) => {
		const matchingCartItem = itemsToCheckout.find((ci) => ci.id === product._id);
		return {
			...product,
			quantity: matchingCartItem?.quantity || 1,
			total: product.price * (matchingCartItem?.quantity || 1),
			...(matchingCartItem?.variantId && { variantId: matchingCartItem.variantId })
		};
	});

	const discount = totals.totalActualPrice - totals.totalPrice;
	const subtotal = totals.totalActualPrice;
	const total = totals.totalPrice;

	// Validate form
	const validateForm = () => {
		let isValid = true;
		const newErrors = {
			fullName: "",
			phoneNumber: "",
			address: "",
			termsAccepted: "",
			phoneVerify: ""
		};

		// Validate phone
		if (!isPhoneVerified) {
			newErrors.phoneVerify = "Please verify your phone first!";
			isValid = false;
		}

		// Validate name
		if (!formData.fullName || formData.fullName.length < 3) {
			newErrors.fullName = "Name must be at least 3 characters";
			isValid = false;
		}

		// Validate phone number (Bangladeshi format)
		const phoneRegex = /^01[3-9]\d{8}$/;
		if (!formData.phoneNumber || !phoneRegex.test(formData.phoneNumber)) {
			newErrors.phoneNumber = "Please enter a valid Bangladeshi phone number";
			isValid = false;
		}

		// Validate address
		if (!formData.address || formData.address.length < 10) {
			newErrors.address = "Please provide a complete address with at least 10 characters";
			isValid = false;
		}

		// Validate terms acceptance
		if (!formData.termsAccepted) {
			newErrors.termsAccepted = "You must accept the terms and conditions";
			isValid = false;
		}

		setErrors(newErrors);
		return isValid;
	};

	// Handle form submission
	const handleSubmit = async (e: FormEvent) => {
		e.preventDefault();

		// Validate form
		if (!validateForm()) {
			toast({
				title: "Form Validation Error",
				description: "Please check the form for errors.",
				variant: "destructive"
			});
			return;
		}

		// For online payment, we'll implement later
		if (paymentMethod === "online") {
			toast({
				title: "Online Payment",
				description: "Online payment is not available yet.",
				variant: "destructive"
			});
			return;
		}

		try {
			setIsSubmitting(true);

			// Prepare order data
			const orderData = {
				customerInfo: {
					name: formData.fullName,
					phone: formData.phoneNumber,
					address: formData.address
				},
				totalAmount: total,
				items: enrichedCartItems.map((item) => ({
					productId: item._id,
					quantity: item.quantity,
					price: item.price,
					...(item?.variantId && { variantId: item.variantId })
				}))
			};

			console.log("Submitting order data:", orderData);

			// Get the API URL - use a sensible default to prevent undefined URLs
			const apiBaseUrl = process.env.NEXT_PUBLIC_API_BASE_URL || "http://localhost:5000";

			// Using the correct endpoint based on the server routes
			const endpoint = "/orders/create";

			const url = `${apiBaseUrl}${endpoint}`;
			console.log("API URL:", url);

			try {
				// Create order using native fetch
				const response = await fetch(url, {
					method: "POST",
					headers: {
						"Content-Type": "application/json",
						Accept: "application/json"
					},
					body: JSON.stringify(orderData)
				});

				console.log("Response status:", response.status);

				if (!response.ok) {
					const errorText = await response.text();
					console.error("Server response error:", errorText);
					throw new Error(`Server Error: ${response.status} ${response.statusText}`);
				}

				const responseData = await response.json();
				console.log("Response data:", responseData);

				// Handle success
				toast({
					title: "Order Placed Successfully!",
					description: `Your order has been placed successfully.`,
					variant: "default"
				});

				// Clear cart and redirect to success page
				emptyCart();

				// Use the order number from the response if available
				const orderNumber = responseData.data?.orderNumber || "unknown";
				router.push(`/order-success?order=${orderNumber}`);
			} catch (fetchError) {
				console.error("Fetch error:", fetchError);
				throw fetchError;
			}
		} catch (error) {
			console.error("Error creating order:", error);
			toast({
				title: "Order Failed",
				description: "There was an error processing your order. Please try again.",
				variant: "destructive"
			});
		} finally {
			setIsSubmitting(false);
		}
	};
	const handleVerifyPhone = async (phone: string) => {
		try {
			// Call signup API again to resend OTP
			const response = await sendOtp({ phone }).unwrap();

			if (response.success) {
				toast({
					title: "OTP sent successfully!",
					description: "Please check your phone!"
				});
				setIsOtpModalOpen(true);
			}
		} catch (error: any) {
			// Handle error
			const errorMessage = error.response?.data?.message || "Failed to resend OTP. Please try again.";
			toast({
				title: errorMessage,
				variant: "destructive"
			});
		}
	};
	return (
		<div className='bg-gray-50 min-h-screen'>
			<MaxWidthWrapper className='py-8'>
				{/* Alert Banner */}
				<div className='bg-brand/5 border border-brand/20 p-4 mb-8 rounded-lg text-center shadow-sm'>
					<p className='text-brand-black font-medium'>
						অর্ডার সংক্রান্ত যেকোনো প্রয়োজনে কথা বলুন কাস্টমার সার্ভিস অফিসারদের সাথে - 09678148148
					</p>
				</div>

				<div className='grid grid-cols-1 lg:grid-cols-12 gap-4 sm:gap-8'>
					<div className='lg:col-span-8 space-y-6'>
						{/* Payment Method Selection */}
						<Card className='shadow-sm border-0'>
							<CardHeader className='pb-3'>
								<CardTitle>Payment Method</CardTitle>
								<CardDescription>Select how you want to pay for your order</CardDescription>
							</CardHeader>
							<CardContent>
								<div className='grid grid-cols-1 sm:grid-cols-2 gap-3'>
									<button
										className={`p-3 rounded-lg border-2 transition-all duration-200 flex items-center gap-2 ${
											paymentMethod === "cash"
												? "border-brand bg-brand/5 ring-1 ring-brand"
												: "border-gray-200 hover:border-brand/50"
										}`}
										onClick={() => setPaymentMethod("cash")}
										type='button'
									>
										<div
											className={`w-8 h-8 rounded-full flex items-center justify-center ${
												paymentMethod === "cash" ? "bg-brand/10 text-brand" : "bg-gray-100 text-gray-500"
											}`}
										>
											<Truck className='h-4 w-4 text-brand-dark-500' />
										</div>
										<div className='flex-1'>
											<p
												className={`font-medium text-sm ${
													paymentMethod === "cash" ? "text-brand-darker" : "text-gray-700"
												}`}
											>
												Cash on Delivery
											</p>
											<p className='text-xs text-gray-500'>Pay when you receive</p>
										</div>
										{paymentMethod === "cash" && (
											<Badge variant='outline' className='bg-brand/10 text-brand-dark-700 border-brand ml-auto'>
												<Check className='mr-1 h-3 w-3' /> Selected
											</Badge>
										)}
									</button>

									<button
										className={`p-3 rounded-lg border-2 transition-all duration-200 flex items-center gap-2 ${
											paymentMethod === "online"
												? "border-brand bg-brand/5 ring-1 ring-brand"
												: "border-gray-200 hover:border-brand/50"
										}`}
										onClick={() => setPaymentMethod("online")}
										type='button'
									>
										<div
											className={`w-8 h-8 rounded-full flex items-center justify-center ${
												paymentMethod === "online" ? "bg-brand/10 text-brand" : "bg-gray-100 text-gray-500"
											}`}
										>
											<CreditCard className='h-4 w-4 text-brand-black' />
										</div>
										<div className='flex-1'>
											<p
												className={`font-medium text-sm ${
													paymentMethod === "online" ? "text-brand-darker" : "text-gray-700"
												}`}
											>
												Online Payment
											</p>
											<p className='text-xs text-gray-500'>Card, bKash, Nagad</p>
										</div>
										{paymentMethod === "online" && (
											<Badge variant='outline' className='bg-brand/10 text-brand-black border-brand ml-auto'>
												<Check className='mr-1 h-3 w-3' /> Selected
											</Badge>
										)}
									</button>
								</div>
							</CardContent>
						</Card>
						{/* Billing Details Form */}
						<Card className='shadow-sm border-0'>
							<CardHeader className='pb-3'>
								<CardTitle>Billing Details</CardTitle>
								<CardDescription>Enter your information for delivery</CardDescription>
							</CardHeader>
							<CardContent>
								<form onSubmit={handleSubmit} className='space-y-5'>
									<div className='space-y-2'>
										<Label htmlFor='fullName'>
											Full Name <span className='text-red-500'>*</span>
										</Label>
										<Input
											id='fullName'
											placeholder='Enter your full name'
											value={formData.fullName}
											onChange={handleInputChange}
											className={errors.fullName ? "border-red-500" : ""}
										/>
										{errors.fullName && <p className='text-sm text-red-500'>{errors.fullName}</p>}
									</div>

									<div className='space-y-2'>
										<Label htmlFor='phoneNumber'>
											Phone Number <span className='text-red-500'>*</span>
										</Label>
										<div className='flex flex-col sm:flex-row gap-2'>
											<Input
												id='phoneNumber'
												placeholder='Enter your phone number'
												value={formData.phoneNumber}
												onChange={handleInputChange}
												className={`${errors.phoneNumber ? "border-red-500" : ""} w-full`}
											/>
											{(!userData || userData.phone !== formData.phoneNumber) && !isPhoneVerified ? (
												<Button
													onClick={() => {
														handleVerifyPhone(formData.phoneNumber);
													}}
													disabled={isSendingOtp}
													type='button'
													className='bg-brand hover:bg-brand/90 text-brand-black sm:w-auto w-full mt-1 sm:mt-0'
												>
													Verify
												</Button>
											) : (
												<Button
													type='button'
													// variant='success'
													className='bg-green-600 hover:bg-green-500 text-white sm:w-auto w-full mt-1 sm:mt-0'
												>
													Verified
												</Button>
											)}
										</div>
										{errors.phoneNumber ? (
											<p className='text-sm text-red-500'>{errors.phoneNumber}</p>
										) : (
											<p className='text-xs text-gray-500'>Enter a valid Bangladeshi number (e.g., 01712345678)</p>
										)}
										{errors.phoneVerify && <p className='text-sm text-red-500'>{errors.phoneVerify}</p>}
									</div>

									<div className='space-y-2'>
										<Label htmlFor='address'>
											Full Address <span className='text-red-500'>*</span>
										</Label>
										<Textarea
											id='address'
											placeholder='Enter your complete address'
											value={formData.address}
											onChange={handleInputChange}
											className={errors.address ? "border-red-500" : ""}
										/>
										{errors.address ? (
											<p className='text-sm text-red-500'>{errors.address}</p>
										) : (
											<p className='text-xs text-gray-500'>Include house/flat number, road, area, and city</p>
										)}
									</div>

									<div className='flex flex-col sm:flex-row items-start sm:items-center justify-between sm:space-x-3 space-y-4 sm:space-y-0 pt-4 border-t'>
										<div className='flex flex-col items-start sm:items-center space-x-2'>
											<div className=' flex items-center gap-2'>
												<Checkbox
													id='terms'
													checked={formData.termsAccepted}
													onCheckedChange={handleCheckboxChange}
													className={errors.termsAccepted ? "border-red-500" : ""}
												/>
												<div>
													<Label htmlFor='terms' className='text-sm font-normal text-brand-dark-800'>
														I agree to the{" "}
														<Link href='#' className='text-brand-dark-500 font-bold hover:text-brand-darker underline'>
															Terms
														</Link>{" "}
														&{" "}
														<Link href='#' className='text-brand-dark-500 font-bold hover:text-brand-darker underline'>
															Privacy Policy
														</Link>
													</Label>
												</div>
											</div>
											{errors.termsAccepted && <p className='text-sm text-red-500'>{errors.termsAccepted}</p>}
										</div>

										<Button
											type='submit'
											className='w-full sm:w-auto px-6 bg-brand hover:bg-brand/90 text-brand-black'
											disabled={paymentMethod === "online" || isSubmitting}
										>
											{isSubmitting ? "Processing..." : "Confirm Order"}
										</Button>
									</div>
								</form>
							</CardContent>
						</Card>
					</div>

					{/* Order Summary */}
					<div className='lg:col-span-4'>
						<Card className='sticky top-6 shadow-sm border-0'>
							<CardHeader className='pb-3'>
								<CardTitle>Your Order</CardTitle>
								<CardDescription>Review your items before confirming</CardDescription>
							</CardHeader>
							<CardContent className='space-y-5'>
								{/* Products List */}
								<div className='space-y-4 max-h-[350px] overflow-y-auto pr-2'>
									{enrichedCartItems?.map((item) => (
										<div key={item?.name} className='flex gap-3 pb-4 border-b'>
											<div className='relative h-20 w-16 bg-gray-50 rounded-lg p-1 border'>
												<Image
													src={(item?.images && item.images[0]) || "/placeholder.svg"}
													alt={item?.name || "Image"}
													fill
													className='object-contain'
												/>
											</div>
											<div className='flex-1'>
												<h3 className='font-medium text-gray-900 text-sm'>{item.name}</h3>
												<p className='text-xs text-gray-500 mb-2'>{item.description}</p>
												<div className='flex items-center justify-between'>
													<div className='flex items-center border rounded-md bg-gray-50'>
														<button
															// onClick={() => updateItemQuantity(item._id, -1)}
															onClick={() => {
																if (buyNowItem) {
																	setBuyNowItem({ ...buyNowItem, quantity: buyNowItem?.quantity - 1 });
																} else updateItemQuantity(item._id, -1);
															}}
															disabled={item.quantity == 1}
															className='px-2 py-1 text-gray-600 hover:text-brand'
															type='button'
														>
															-
														</button>
														<span className='px-3 py-1 font-medium text-gray-900 text-xs'>{item.quantity}</span>
														<button
															// onClick={() => updateItemQuantity(item._id, 1)}
															onClick={() => {
																if (buyNowItem) {
																	setBuyNowItem({ ...buyNowItem, quantity: buyNowItem?.quantity + 1 });
																} else updateItemQuantity(item._id, 1);
															}}
															className='px-2 py-1 text-gray-600 hover:text-brand'
															type='button'
														>
															+
														</button>
													</div>
													<div className='text-right'>
														<div className='text-xs text-gray-500'>
															৳{item.price} x {item.quantity}
														</div>
														<div className='font-medium text-gray-900'>৳{item.total}</div>
													</div>
												</div>
											</div>
										</div>
									))}
								</div>

								{/* Coupon Code */}
								<div>
									<Label htmlFor='checkout-coupon' className='text-sm font-medium mb-2 block'>
										Coupon Code
									</Label>
									<div className='flex gap-2'>
										<Input
											id='checkout-coupon'
											placeholder='Enter coupon code'
											value={couponCode}
											onChange={(e) => setCouponCode(e.target.value)}
										/>
										<Button
											type='button'
											variant='outline'
											className='whitespace-nowrap border-brand text-brand-black hover:bg-brand/5'
										>
											Apply
										</Button>
									</div>
								</div>

								<Separator />

								{/* Price Summary */}
								<div className='space-y-3'>
									<div className='flex justify-between text-gray-600 text-sm'>
										<span>Subtotal</span>
										<span className='text-gray-900 font-medium'>৳{subtotal}</span>
									</div>
									<div className='flex justify-between items-center text-gray-600 text-sm'>
										<span>Delivery Charge</span>
										<div className='flex items-center gap-1'>
											<InfoIcon className='h-4 w-4 text-brand' />
											<span className='text-brand-dark-700'>will be added</span>
										</div>
									</div>
									<div className='flex justify-between text-gray-600 text-sm'>
										<span>Total Discount:</span>
										<span>৳{discount}</span>
									</div>
									<Separator />
									<div className='flex justify-between pt-1'>
										<span className='font-bold text-gray-900'>Total</span>
										<span className='font-bold text-brand-darker text-lg'>৳{total}</span>
									</div>
								</div>

								<div className='mt-4'>
									<Link href='/cart'>
										<Button
											variant='outline'
											className='w-full border-brand text-brand-black hover:bg-brand/5'
											type='button'
										>
											<ArrowLeft className='mr-2 h-4 w-4' />
											Back to Cart
										</Button>
									</Link>
								</div>
							</CardContent>
						</Card>
					</div>
				</div>
			</MaxWidthWrapper>
			{/* modals */}
			<OtpModal
				open={isOtpModalOpen}
				onOpenChange={setIsOtpModalOpen}
				phone={formData.phoneNumber}
				setIsPhoneVerified={setIsPhoneVerified}
			/>
		</div>
	);
}

interface OtpModalProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	phone: string;
	setIsPhoneVerified: (open: boolean) => void;
}
function OtpModal({ open, onOpenChange, phone, setIsPhoneVerified }: OtpModalProps) {
	const [otpValues, setOtpValues] = useState(["", "", "", "", "", ""]);
	const otpInputRefs = useRef<Array<HTMLInputElement | null>>([null, null, null, null, null, null]);
	const { toast } = useToast();
	const [sendOtp, { isLoading: isSendingOtp }] = useSendOtpMutation();
	const [verifyOtp, { isLoading: isVerifyingOtp }] = useVerifyOtpMutation();

	function handleOtpChange(index: number, value: string) {
		// Only allow numbers
		if (value && !/^\d+$/.test(value)) return;

		const newOtpValues = [...otpValues];
		newOtpValues[index] = value;
		setOtpValues(newOtpValues);

		// Auto-focus next input
		if (value && index < 5) {
			otpInputRefs.current[index + 1]?.focus();
		}
	}

	function handleKeyDown(index: number, e: React.KeyboardEvent<HTMLInputElement>) {
		// Handle backspace to go to previous input
		if (e.key === "Backspace" && !otpValues[index] && index > 0) {
			otpInputRefs.current[index - 1]?.focus();
		}
	}

	function handlePaste(e: React.ClipboardEvent<HTMLInputElement>) {
		e.preventDefault();
		const pastedData = e.clipboardData.getData("text/plain").trim();

		// Check if pasted content is a 6-digit number
		if (/^\d{6}$/.test(pastedData)) {
			const digits = pastedData.split("");
			setOtpValues(digits);

			// Focus the last input
			otpInputRefs.current[5]?.focus();
		}
	}

	async function handleVerifyOtp() {
		try {
			const otpCode = otpValues.join("");

			// Call verify OTP API
			const response = await verifyOtp({ phone, code: otpCode }).unwrap();
			// If successful, close the modal and show success message
			if (response.success) {
				toast({
					title: "Phone number verified successfully.",
					variant: "default"
				});
				setIsPhoneVerified(true);
				onOpenChange(false);
			}
		} catch (error: any) {
			// Handle error
			const errorMessage = error.response?.data?.message || "Failed to verify OTP. Please try again.";
			toast({
				title: errorMessage,
				variant: "destructive"
			});
		}
	}

	async function handleResendOtp() {
		try {
			// Call signup API again to resend OTP
			const response = await sendOtp({ phone }).unwrap();

			if (response.success) {
				toast({
					title: "Password changed successfully!",
					description: "Your password changed successfully"
				});
			}
		} catch (error: any) {
			// Handle error
			const errorMessage = error.response?.data?.message || "Failed to resend OTP. Please try again.";
			toast({
				title: errorMessage,
				variant: "destructive"
			});
		}
	}

	function cancelOtpVerification() {
		setOtpValues(["", "", "", "", "", ""]);
	}

	// Focus first OTP input when verification screen appears
	useEffect(() => {
		if (open) {
			otpInputRefs.current[0]?.focus();
		}
	}, [open]);

	return (
		<Dialog
			open={open}
			onOpenChange={() => {
				cancelOtpVerification();
				onOpenChange(false);
			}}
		>
			<DialogContent className='sm:max-w-[350px] md:max-w-[500px] p-0 overflow-hidden border-0 bg-brand-dark-900'>
				<div className='grid md:grid-cols-5 border-0'>
					<div className='md:col-span-5 p-6 bg-white'>
						<DialogHeader className='space-y-1 px-0 pt-0'>
							<DialogTitle className='text-2xl font-bold text-brand-black'>Verify Your Phone</DialogTitle>
							<p className='text-sm text-muted-foreground text-brand-black'>Enter the 6-digit code sent to {phone}</p>
						</DialogHeader>

						<div className='mt-6'>
							<div className='space-y-6'>
								<div className='flex justify-center gap-2'>
									{otpValues.map((value, index) => (
										<Input
											key={index}
											// @ts-ignore
											ref={(el) => (otpInputRefs.current[index] = el)}
											type='text'
											inputMode='numeric'
											maxLength={1}
											value={value}
											onChange={(e) => handleOtpChange(index, e.target.value)}
											onKeyDown={(e) => handleKeyDown(index, e)}
											onPaste={index === 0 ? handlePaste : undefined}
											className='w-12 h-12 text-center text-lg'
										/>
									))}
								</div>

								<div className='flex flex-col gap-3'>
									<Button
										onClick={handleVerifyOtp}
										className='w-full bg-purple-600 hover:bg-purple-700'
										disabled={otpValues.some((v) => !v) || isVerifyingOtp}
									>
										{isVerifyingOtp ? "Verifying..." : "Verify Phone"}
									</Button>

									<div className='flex items-center justify-center gap-1 text-sm'>
										<span className='text-muted-foreground'>Didn&apos;t receive the code?</span>
										<Button variant='link' className='p-0 h-auto' onClick={handleResendOtp} disabled={isSendingOtp}>
											Resend OTP
										</Button>
									</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</DialogContent>
		</Dialog>
	);
}
