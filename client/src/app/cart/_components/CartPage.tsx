"use client";

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON>, Loader, Loader2, <PERSON>B<PERSON>, Tag, Trash2 } from "lucide-react";
import Link from "next/link";
import { useState } from "react";

import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { <PERSON>, CardContent, <PERSON>Footer, CardHeader } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import MaxWidthWrapper from "@/components/ui/max-width-wrapper";
import { Separator } from "@/components/ui/separator";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";

import { useToast } from "@/hooks/use-toast";
import { useCart } from "@/hooks/useCart";
import { cn } from "@/lib/utils";
import { useGetMinimalProductsQuery } from "@/redux/apis/productApis";
import Image from "next/image";

export default function CartPage() {
	const { cartItems, removeItem, updateItemQuantity } = useCart();
	// Extract all product IDs from cart
	const productIds = cartItems?.map((item) => item.id).join(",");

	// Fetch product details based on those IDs
	const { data, isFetching } = useGetMinimalProductsQuery([{ name: "_ids", value: productIds }], {
		skip: cartItems.length === 0
	});

	console.log({ cartItems, data }, "fromcart");
	const { toast } = useToast();

	// Form states
	const [promoCode, setPromoCode] = useState("");
	const [giftVoucher, setGiftVoucher] = useState("");
	const [promoError, setPromoError] = useState("");
	const [voucherError, setVoucherError] = useState("");
	const [isApplyingPromo, setIsApplyingPromo] = useState(false);
	const [isApplyingVoucher, setIsApplyingVoucher] = useState(false);
	const products = data?.data || [];

	const totals = products.reduce(
		(acc, product) => {
			const item = cartItems.find((ci) => ci.id === product._id);
			if (!item) return acc;

			const qty = item.quantity || 1;
			acc.totalPrice += product.price * qty;
			acc.totalActualPrice += product.actualPrice * qty;

			return acc;
		},
		{ totalPrice: 0, totalActualPrice: 0 }
	);
	const enrichedCartItems = products.map((product) => {
		const matchingCartItem = cartItems.find((ci) => ci.id === product._id);
		return {
			...product,
			quantity: matchingCartItem?.quantity || 1,
			total: product.price * (matchingCartItem?.quantity || 1)
		};
	});

	const discount = totals.totalActualPrice - totals.totalPrice;
	const subtotal = totals.totalActualPrice;
	const total = totals.totalPrice;

	// Validate and apply promo code
	const handleApplyPromo = () => {
		setPromoError("");

		if (!promoCode.trim()) {
			setPromoError("Please enter a promo code");
			return;
		}

		if (promoCode.length < 5) {
			setPromoError("Promo code must be at least 5 characters");
			return;
		}

		setIsApplyingPromo(true);

		// Simulate API call
		setTimeout(() => {
			setIsApplyingPromo(false);
			toast({
				title: "Promo code invalid",
				description: "The promo code you entered is not valid or has expired.",
				variant: "destructive"
			});
		}, 1500);
	};

	// Validate and apply gift voucher
	const handleApplyVoucher = () => {
		setVoucherError("");

		if (!giftVoucher.trim()) {
			setVoucherError("Please enter a voucher code");
			return;
		}

		if (!/^[A-Z0-9]{8,}$/.test(giftVoucher)) {
			setVoucherError("Voucher must be at least 8 alphanumeric characters");
			return;
		}

		setIsApplyingVoucher(true);

		// Simulate API call
		setTimeout(() => {
			setIsApplyingVoucher(false);
			toast({
				title: "Voucher applied!",
				description: "Your gift voucher has been applied to your order."
			});
		}, 1500);
	};

	return (
		<section className='bg-gray-50 min-h-screen py-10'>
			<MaxWidthWrapper className='py-3 sm:py-6'>
				<div className='flex flex-wrap items-center gap-2 mb-4 sm:mb-6'>
					<Link href='/' className='text-gray-500 hover:text-brand-black flex items-center gap-1 text-sm mr-1'>
						<ArrowLeft className='h-4 w-4' />
						Continue Shopping
					</Link>
					<Separator orientation='vertical' className='h-4 hidden sm:block' />
					<h1 className='text-xl font-bold text-brand-black w-full sm:w-auto mt-2 sm:mt-0 order-1 sm:order-none'>
						Your Shopping Cart
					</h1>
					{cartItems.length > 0 && (
						<Badge variant='outline' className='ml-0 sm:ml-2 bg-brand text-brand-black'>
							{cartItems.length} {cartItems.length === 1 ? "item" : "items"}
						</Badge>
					)}
				</div>

				<div className='grid grid-cols-1 lg:grid-cols-12 gap-4 md:gap-6'>
					<div className='lg:col-span-8'>
						<Card className='border-none shadow-sm'>
							<CardHeader className='pb-2 pt-3 sm:pt-4 px-3 sm:px-4 md:px-6'>
								<div className='flex justify-between items-center'>
									<h2 className='text-lg font-medium text-brand-black'>Cart Items</h2>
									{cartItems.length > 0 && (
										<Button
											variant='ghost'
											size='sm'
											className='text-brand-black hover:bg-brand'
											onClick={() => {
												toast({
													title: "Feature coming soon",
													description: "Save for later functionality will be available soon."
												});
											}}
										>
											Save all for later
										</Button>
									)}
								</div>
							</CardHeader>
							<CardContent className='px-3 sm:px-4 md:px-6 py-2 sm:py-4'>
								{isFetching && (
									<div className='py-12 text-center'>
										<Loader className='h-12 w-12 mx-auto text-brand-black mb-4 animate-spin' />
										<p className='text-brand-black mb-6'>Your cart is loading ...</p>
									</div>
								)}
								{(data?.data?.length === 0 || !data || !data?.data) && !isFetching && (
									<div className='py-12 text-center'>
										<ShoppingBag className='h-12 w-12 mx-auto text-brand-black mb-4' />
										<p className='text-brand-black mb-6'>Your cart is empty</p>
										<Link href='/'>
											<Button className='bg-brand text-brand-black hover:bg-brand/70'>Browse Products</Button>
										</Link>
									</div>
								)}
								{data?.data?.length > 0 && (
									<div className='space-y-6'>
										{enrichedCartItems?.map((item) => (
											<div
												key={item?.name}
												className='flex flex-col sm:flex-row items-start sm:items-center gap-4 py-4 border border-gray-100 last:border-b-0 group hover:bg-gray-50 rounded-lg p-4 transition-colors shadow-sm'
											>
												<div className='relative h-24 w-24 sm:h-24 sm:w-20 bg-white rounded-lg p-2 border overflow-hidden'>
													<Image
														src={(item?.images && item.images[0]) || "/placeholder.svg"}
														alt={item.name}
														fill
														className='object-contain transition-transform group-hover:scale-105'
													/>
												</div>
												<div className='flex-1 w-full mt-2 sm:mt-0'>
													<h3 className='font-medium text-brand-black text-base sm:text-lg'>{item.name}</h3>
													<div className='text-sm text-gray-500 mt-2'>
														<span className='text-brand-dark-500 font-bold'>{item.description}</span>
													</div>

													<div className='flex items-center justify-between mt-4 sm:hidden'>
														<div className='flex items-center border rounded-md bg-white shadow-sm h-10'>
															<button
																onClick={() => updateItemQuantity(item._id, -1)}
																className='px-4 py-2 text-gray-600 hover:text-brand transition-colors min-w-[40px]'
																disabled={item.quantity <= 1}
															>
																-
															</button>
															<span className='px-4 py-2 font-medium text-brand-black'>{item.quantity}</span>
															<button
																onClick={() => updateItemQuantity(item._id, 1)}
																className='px-4 py-2 text-gray-600 hover:text-brand transition-colors min-w-[40px]'
															>
																+
															</button>
														</div>
														<div className='text-right'>
															<div className='font-medium text-brand-black'>৳{item.price}</div>
															<div className='font-bold text-brand-darker text-lg'>৳{item.total}</div>
														</div>
													</div>
												</div>

												<div className='hidden sm:flex items-center border rounded-md bg-white shadow-sm h-10'>
													<button
														onClick={() => updateItemQuantity(item._id, -1)}
														className={cn(
															"px-3 py-1 text-gray-600 hover:text-brand transition-colors min-w-[36px]",
															item.quantity <= 1 && "opacity-50 cursor-not-allowed"
														)}
														disabled={item.quantity <= 1}
													>
														-
													</button>
													<span className='px-3 py-1 font-medium text-brand-black'>{item.quantity}</span>
													<button
														onClick={() => updateItemQuantity(item._id, 1)}
														className='px-3 py-1 text-gray-600 hover:text-brand transition-colors min-w-[36px]'
													>
														+
													</button>
												</div>

												<div className='hidden sm:block text-right min-w-[100px]'>
													<div className='font-medium text-brand-black'>৳{item.price}</div>
													<div className='font-bold text-brand-darker text-lg'>৳{item.total}</div>
												</div>

												<TooltipProvider>
													<Tooltip>
														<TooltipTrigger asChild>
															<button
																onClick={() => removeItem(item._id)}
																className='p-2 text-gray-400 hover:text-red-500 transition-colors rounded-full hover:bg-red-50 absolute top-2 right-2 sm:static'
															>
																<Trash2 className='h-5 w-5' />
															</button>
														</TooltipTrigger>
														<TooltipContent>
															<p>Remove from cart</p>
														</TooltipContent>
													</Tooltip>
												</TooltipProvider>
											</div>
										))}
									</div>
								)}
							</CardContent>
						</Card>
					</div>

					<div className='lg:col-span-4'>
						<Card className='border-none shadow-sm sticky top-6'>
							<CardHeader className='pb-2 pt-4 px-4 md:px-6'>
								<h2 className='text-lg font-medium text-brand-black'>Order Summary</h2>
							</CardHeader>

							<CardContent className='px-4 md:px-6'>
								{/* Promo Code */}
								<div className='space-y-5 mb-6'>
									<div>
										<div className='flex items-center gap-2 mb-2'>
											<Tag className='h-4 w-4 text-brand-dark-300' />
											<label htmlFor='coupon' className='text-sm font-medium text-gray-700'>
												Promo / Coupon Code
											</label>
										</div>
										<div className='flex flex-col sm:flex-row gap-2'>
											<div className='flex-1'>
												<Input
													id='coupon'
													placeholder='Enter promo code'
													value={promoCode}
													onChange={(e) => {
														setPromoCode(e.target.value);
														if (promoError) setPromoError("");
													}}
													className={cn(
														"h-10 rounded-lg border-gray-200 focus:border-brand focus:ring-brand text-brand-black",
														promoError && "border-red-300 focus:border-red-500 focus:ring-red-500"
													)}
												/>
												{promoError && <p className='text-red-500 text-xs mt-1'>{promoError}</p>}
											</div>
											<Button
												className='h-10 whitespace-nowrap rounded-lg border border-brand bg-white text-brand-black hover:bg-brand/5 w-full sm:w-auto mt-1 sm:mt-0'
												onClick={handleApplyPromo}
												disabled={isApplyingPromo}
											>
												{isApplyingPromo ? (
													<>
														<Loader2 className='h-4 w-4 mr-2 animate-spin' />
														Applying
													</>
												) : (
													"Apply"
												)}
											</Button>
										</div>
									</div>

									{/* Gift Voucher */}
									<div>
										<div className='flex items-center gap-2 mb-2'>
											<Gift className='h-4 w-4 text-brand-dark-300' />
											<label htmlFor='voucher' className='text-sm font-medium text-gray-700'>
												Gift Voucher
											</label>
										</div>
										<div className='flex flex-col sm:flex-row gap-2'>
											<div className='flex-1'>
												<Input
													id='voucher'
													placeholder='Enter gift voucher'
													value={giftVoucher}
													onChange={(e) => {
														setGiftVoucher(e.target.value.toUpperCase());
														if (voucherError) setVoucherError("");
													}}
													className={cn(
														"h-10 rounded-lg border-gray-200 focus:border-brand focus:ring-brand",
														voucherError && "border-red-300 focus:border-red-500 focus:ring-red-500"
													)}
												/>
												{voucherError && <p className='text-red-500 text-xs mt-1'>{voucherError}</p>}
											</div>
											<Button
												className='h-10 whitespace-nowrap rounded-lg border border-brand bg-white text-brand-black hover:bg-brand/5 w-full sm:w-auto mt-1 sm:mt-0'
												onClick={handleApplyVoucher}
												disabled={isApplyingVoucher}
											>
												{isApplyingVoucher ? (
													<>
														<Loader2 className='h-4 w-4 mr-2 animate-spin' />
														Applying
													</>
												) : (
													"Apply"
												)}
											</Button>
										</div>
									</div>
								</div>

								{/* Price Summary */}
								<div className='space-y-3 border-t pt-4'>
									<div className='flex justify-between text-gray-600 text-sm'>
										<span>Subtotal:</span>
										<span className='text-brand-dark-400 font-bold'>৳{subtotal}</span>
									</div>
									<div className='flex justify-between text-gray-600 text-sm'>
										<span>Delivery Charge:</span>
										<span className='text-brand-dark-400 font-bold italic'>(will be added)</span>
									</div>
									<div className='flex justify-between text-green-600 text-sm'>
										<span>Total Discount:</span>
										<span>৳{discount}</span>
									</div>
									<Separator className='my-2' />
									<div className='flex justify-between pt-2'>
										<span className='font-bold text-brand-black'>Total:</span>
										<span className='font-bold text-brand-darker text-xl'>৳{total}</span>
									</div>
								</div>
							</CardContent>

							<CardFooter className='px-4 md:px-6 pt-4 pb-6'>
								<div className='grid grid-cols-1 xs:grid-cols-2 gap-3 w-full'>
									<Link href='/' className='w-full'>
										<Button
											variant='outline'
											className='w-full border-brand text-brand-black hover:bg-brand/5 hover:text-brand-darker'
										>
											Continue Shopping
										</Button>
									</Link>
									<Link href='/checkout' className='w-full'>
										<Button
											className='w-full bg-brand text-brand-black transition-colors hover:bg-brand/90'
											disabled={cartItems.length === 0}
										>
											Checkout
										</Button>
									</Link>
								</div>
							</CardFooter>
						</Card>
					</div>
				</div>
			</MaxWidthWrapper>
		</section>
	);
}
