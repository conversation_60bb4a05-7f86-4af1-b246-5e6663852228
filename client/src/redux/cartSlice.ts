import { createSlice, PayloadAction } from "@reduxjs/toolkit";

// export interface CartItem extends IProduct {
// 	quantity: number;
// 	total: number;
// }
export interface CartItem {
	id: string;
	quantity: number;
	variantId?: string;
}

interface CartState {
	items: CartItem[];
	buyNowItem: CartItem | null;
}

const initialState: CartState = {
	items: [],
	buyNowItem: null
};
const cartSlice = createSlice({
	name: "cart",
	initialState,
	reducers: {
		addToCart: (state, action: PayloadAction<CartItem>) => {
			const existingItem = state.items.find((item) => item.id === action.payload.id);
			if (existingItem) {
				existingItem.quantity += action.payload.quantity || 1;
				// existingItem.total = Number(existingItem.price) * existingItem.quantity;
			} else {
				state.items.push({
					...action.payload,
					quantity: 1
					// total: Number(action.payload.price)
				});
			}
		},
		removeFromCart: (state, action: PayloadAction<string>) => {
			state.items = state.items.filter((item) => item.id !== action.payload);
		},
		updateQuantity: (state, action: PayloadAction<{ id: string; change: number }>) => {
			const { id, change } = action.payload;
			const item = state.items.find((item) => item.id === id);

			if (item) {
				item.quantity = Math.max(1, item.quantity + change);
				// item.total = Number(item.price) * item.quantity;
			}
		},
		clearCart: (state) => {
			state.items = [];
		},
		// ✅ Buy Now logic
		setBuyNowItem: (state, action: PayloadAction<CartItem>) => {
			state.buyNowItem = action.payload;
		},
		clearBuyNowItem: (state) => {
			state.buyNowItem = null;
		}
	}
});

export const { addToCart, removeFromCart, updateQuantity, clearCart, setBuyNowItem, clearBuyNowItem } =
	cartSlice.actions;
export default cartSlice.reducer;
