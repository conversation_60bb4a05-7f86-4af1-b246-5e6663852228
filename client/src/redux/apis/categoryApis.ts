import { TQueryParams } from "@/interfaces";
import { baseApi } from "../baseApi";

const categoryApis = baseApi.injectEndpoints({
	endpoints: (builder) => ({
		// Get All  Categories
		getCategories: builder.query({
			query: () => {
				return {
					url: "/primary-categories/all",
					method: "GET"
				};
			},
			// Provides a tag for the list of categories
			providesTags: ["Categories"]
		}),
		getPrimaryCategories: builder.query({
			query: (args: TQueryParams[]) => {
				const params = args
					? args.map(({ name, value }) => `${encodeURIComponent(name)}=${encodeURIComponent(value)}`).join("&")
					: "";
				return {
					url: `/primary-categories?${params}`,
					method: "GET"
				};
			},
			providesTags: ["Categories"]
		})
	})
});

export const { useGetCategoriesQuery, useGetPrimaryCategoriesQuery } = categoryApis;
