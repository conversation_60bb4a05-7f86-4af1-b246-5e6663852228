/* eslint-disable @typescript-eslint/no-explicit-any */
import { TQueryParams } from "@/interfaces";
import { baseApi } from "../baseApi";

export interface IAd {
	inquiries: number;
	views: number;
	_id?: string;
	slug: string;
	title: string;
	description: string;
	category: string;
	condition: string;
	price: number;
	images: string[];
	location: string;
	user: {
		name: string;
		phone: string;
		createdAt: string;
	};
	createdAt?: Date;
	updatedAt?: Date;
}

export interface ICreateAdData {
	title: string;
	description: string;
	category: string;
	condition: string;
	price: number;
	images: string[];
	location: string;
}

const adsApis = baseApi.injectEndpoints({
	endpoints: (builder) => ({
		// Mutation: Create ad
		createAd: builder.mutation<any, ICreateAdData>({
			query: (data) => ({
				url: "/ads/create",
				method: "POST",
				body: data
			}),
			invalidatesTags: [{ type: "Ads", id: "LIST" }]
		}),

		// Query: Get all ads
		getAllAds: builder.query<{ success: boolean; data: IAd[]; meta: any }, TQueryParams[] | void>({
			query: (args) => {
				const params = args
					? args.map(({ name, value }) => `${encodeURIComponent(name)}=${encodeURIComponent(value)}`).join("&")
					: "";
				return {
					url: `/ads${params ? `?${params}` : ""}`,
					method: "GET"
				};
			},
			providesTags: (result) =>
				result
					? [
							{ type: "Ads", id: "LIST" },
							...result.data.map(({ _id }) => ({
								type: "Ads" as const,
								id: _id
							}))
					  ]
					: [{ type: "Ads", id: "LIST" }]
		}),

		// Query: Get ad by ID
		getAd: builder.query<{ success: boolean; data: IAd }, string>({
			query: (id) => ({
				url: `/ads/${id}`,
				method: "GET"
			}),
			providesTags: (_result, _error, id) => [{ type: "Ads", id }]
		}),

		// Query: Get my ads
		getMyAds: builder.query<{ success: boolean; data: IAd[] }, void>({
			query: () => ({
				url: "/ads/my-ads",
				method: "GET"
			})
		}),

		// Query: Get featured ads
		getFeaturedAds: builder.query<{ success: boolean; data: IAd[] }, void>({
			query: () => ({
				url: "/ads/featured",
				method: "GET"
			})
		}),

		// Query: Get recent ads
		getRecentAds: builder.query<{ success: boolean; data: IAd[] }, void>({
			query: () => ({
				url: "/ads/recent",
				method: "GET"
			})
		}),
		// Mutation: Update ad
		updateAd: builder.mutation<any, { id: string; data: Partial<ICreateAdData> }>({
			query: ({ id, data }) => ({
				url: `/ads/${id}`,
				method: "PUT",
				body: data
			}),
			invalidatesTags: (_result, _error, { id }) => [
				{ type: "Ads", id: "LIST" },
				{ type: "Ads", id }
			]
		}),

		// Mutation: Delete ad
		deleteAd: builder.mutation<any, string>({
			query: (id) => ({
				url: `/ads/${id}`,
				method: "DELETE"
			}),
			invalidatesTags: [{ type: "Ads", id: "LIST" }]
		})
	})
});

export const {
	useCreateAdMutation,
	useGetAllAdsQuery,
	useGetAdQuery,
	useUpdateAdMutation,
	useDeleteAdMutation,
	useGetMyAdsQuery,
	useGetFeaturedAdsQuery,
	useGetRecentAdsQuery
} = adsApis;
