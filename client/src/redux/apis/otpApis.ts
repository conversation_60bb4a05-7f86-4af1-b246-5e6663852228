import { baseApi } from "../baseApi";

const authApi = baseApi.injectEndpoints({
	endpoints: (builder) => ({
		sendOtp: builder.mutation({
			query: (data) => {
				return {
					url: "/otps/resend",
					method: "POST",
					body: data
				};
			}
		}),
		verifyOtp: builder.mutation({
			query: (data) => {
				return {
					url: "/otps/verify",
					method: "POST",
					body: data
				};
			}
		})
	})
});

export const { useSendOtpMutation, useVerifyOtpMutation } = authApi;
