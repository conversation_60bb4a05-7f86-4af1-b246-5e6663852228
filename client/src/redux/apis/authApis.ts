import { baseApi } from "../baseApi";

const authApi = baseApi.injectEndpoints({
	endpoints: (builder) => ({
		login: builder.mutation({
			query: (data) => {
				return {
					url: "/users/login",
					method: "POST",
					body: data
				};
			}
		}),
		getMe: builder.query({
			query: () => {
				return {
					url: "/users/get-me",
					method: "GET"
				};
			}
		}),
		updateMe: builder.mutation({
			query: (data) => {
				return {
					url: "/users/update-me",
					method: "PATCH",
					body: data
				};
			}
		}),
		changePassword: builder.mutation({
			query: (data) => {
				return {
					url: "/users/change-password",
					method: "PATCH",
					body: data
				};
			}
		})
	})
});

export const { useLoginMutation, useGetMeQuery, useUpdateMeMutation, useChangePasswordMutation } = authApi;
