import { baseApi } from "../baseApi";

const orderApi = baseApi.injectEndpoints({
	endpoints: (builder) => ({
		getUserOrders: builder.query({
			query: () => {
				return {
					url: "/orders/user",
					method: "GET"
				};
			},
			providesTags: ["Orders"]
		}),
		getOrderById: builder.query({
			query: (id) => {
				return {
					url: `/orders/${id}`,
					method: "GET"
				};
			},
			providesTags: (result, error, id) => [{ type: "Order", id }]
		}),
		createOrder: builder.mutation({
			query: (orderData) => {
				return {
					url: "/orders",
					method: "POST",
					body: orderData
				};
			},
			invalidatesTags: ["Orders"]
		})
	})
});

export const { useGetUserOrdersQuery, useGetOrderByIdQuery, useCreateOrderMutation } = orderApi;
