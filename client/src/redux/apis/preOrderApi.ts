/* eslint-disable @typescript-eslint/no-explicit-any */
import { baseApi } from "../baseApi";

const preOrderApis = baseApi.injectEndpoints({
	endpoints: (builder) => ({
		// Mutation: Create PreOrder
		createPreOrder: builder.mutation({
			query: (data: FormData) => ({
				url: "/pre-orders/create",
				method: "POST",
				body: data
			})
		})
	})
});

export const { useCreatePreOrderMutation } = preOrderApis;
