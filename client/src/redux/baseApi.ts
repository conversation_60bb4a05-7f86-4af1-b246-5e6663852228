import config from "@/config";
import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

const baseQuery = fetchBaseQuery({
	baseUrl: config.serverURL,
	credentials: "include",
	prepareHeaders: (headers, { getState }) => {
		const token = (getState() as { auth: { token: string } }).auth.token;
		if (token) {
			headers.set("authorization", `${token}`);
		}
		return headers;
	}
});

export const baseApi = createApi({
	reducerPath: "baseApi",
	baseQuery: baseQuery,
	tagTypes: ["Categories", "Products", "Product", "Brands", "ProductField", "Banner", "Orders", "Order", "Ads"],
	endpoints: () => ({})
});
