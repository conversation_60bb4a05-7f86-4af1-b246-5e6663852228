export type TQueryParams = {
	name: string;
	value: string;
};

export type TMeta = {
	page: number;
	limit: number;
	totalItems: number;
	totalPages: number;
};

export type TErrorSources = {
	path: string | number;
	message: string;
}[];

export type TErrorResponse = {
	statusCode: number;
	message: string;
	errorSources: TErrorSources;
};

export interface IOffer {
	_id?: string;
	name: string;
	slug: string;
	description: string;
	startDate: Date;
	endDate: Date;
	profilePicture: string;
	banner: string;
	products: string[];
	createAt?: Date;
	updateAt?: Date;
}

export interface IBrand {
	_id?: string;
	name: string;
	image: string;
	slug: string;
	createdAt?: string;
	updatedAt?: string;
}
export interface IProduct {
	_id: string;
	name: string;
	price: number;
	description: string;
	brand: IBrand;
	additionalInfo: IAdditionalInfo[];
	primaryCategory: IPrimaryCategory;
	secondaryCategory: ISecondaryCategory;
	tertiaryCategory: ITertiaryCategory;
	images: string[];
	stock: number;
	isActive: boolean;
	variants: IVariant[];
	slug: string;
	actualPrice: number;
	createdAt: string;
	updatedAt: string;
}
export interface IAdditionalInfo {
	key: string;
	value: string;
	description: string;
	_id: string;
}

export interface IPrimaryCategory {
	_id: string;
	name: string;
	image: string;
	slug: string;
}

export interface ISecondaryCategory {
	_id: string;
	name: string;
	image: string;
	slug: string;
}

export interface ITertiaryCategory {
	_id: string;
	name: string;
	image: string;
	slug: string;
}

export interface IVariant {
	color: IColor;
	storage: string;
	price: number;
	stock: number;
	images: string[];
	region: string;
	condition: string;
	_id: string;
}

export interface IColor {
	name: string;
	value: string;
}
