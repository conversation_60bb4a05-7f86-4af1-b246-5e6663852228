export function addToRecentlyViewed(productId: string) {
	const key = "recentlyViewed";
	let viewed: string[] = JSON.parse(localStorage.getItem(key) || "[]");

	// Remove if already exists to re-add at front
	viewed = viewed.filter((id: string) => id !== productId);
	viewed.unshift(productId);

	// Limit to last 10 viewed items
	if (viewed.length > 10) {
		viewed = viewed.slice(0, 10);
	}

	localStorage.setItem(key, JSON.stringify(viewed));
}

export function getRecentlyViewed(): string[] {
	const key = "recentlyViewed";
	try {
		return JSON.parse(localStorage.getItem(key) || "[]");
	} catch {
		return [];
	}
}
