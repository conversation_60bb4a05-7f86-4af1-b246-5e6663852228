type Variant = Record<string, any>;

export function extractVariantOptions(variants: Variant[]) {
	const optionMap: Record<string, Set<any>> = {};

	variants.forEach((variant) => {
		for (const key in variant) {
			if (["price", "stock", "images", "_id"].includes(key)) continue; // skip non-options

			if (!optionMap[key]) {
				optionMap[key] = new Set();
			}

			if (typeof variant[key] === "object" && variant[key]?.name) {
				optionMap[key].add(JSON.stringify(variant[key]));
			} else {
				optionMap[key].add(variant[key]);
			}
		}
	});

	// Convert sets to arrays and handle JSON parsing if needed
	const finalOptions: Record<string, any[]> = {};
	for (const key in optionMap) {
		finalOptions[key] = Array.from(optionMap[key]).map((value) => {
			try {
				return JSON.parse(value);
			} catch {
				return value;
			}
		});
	}

	return finalOptions;
}
