import {
	addToCart,
	CartItem,
	clearBuyNowItem,
	clearCart,
	removeFromCart,
	setBuyNowItem,
	updateQuantity
} from "@/redux/cartSlice";
import { useAppDispatch, useAppSelector } from "@/redux/hooks";
import { useToast } from "./use-toast";

export function useCart() {
	const dispatch = useAppDispatch();
	const { toast } = useToast();
	const cartItems = useAppSelector((state) => state.cart.items);
	const buyNowItem = useAppSelector((state) => state.cart.buyNowItem);

	const addItem = (product: CartItem) => {
		dispatch(addToCart(product));
		toast({
			title: "Added to cart",
			description: `Item has been added to your cart`
		});
	};

	const removeItem = (id: string) => {
		dispatch(removeFromCart(id));
		toast({
			title: "Removed from cart",
			description: "Item has been removed from your cart"
		});
	};

	const updateItemQuantity = (id: string, change: number) => {
		dispatch(updateQuantity({ id, change }));
	};

	const emptyCart = () => {
		dispatch(clearCart());
		toast({
			title: "Cart cleared",
			description: "All items have been removed from your cart"
		});
	};

	const setBuyNow = (item: CartItem) => {
		dispatch(setBuyNowItem(item));
	};

	const clearBuyNow = () => {
		dispatch(clearBuyNowItem());
	};

	// const subtotal = cartItems.reduce((sum, item) => sum + item.total, 0);
	const itemCount = cartItems.reduce((count, item) => count + item.quantity, 0);

	return {
		cartItems,
		buyNowItem,
		addItem,
		removeItem,
		updateItemQuantity,
		emptyCart,
		// subtotal,
		setBuyNow,
		clearBuyNow,
		itemCount
	};
}
