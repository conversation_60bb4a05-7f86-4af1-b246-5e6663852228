{"name": "exchanger-client", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev --turbopack", "build": "next build", "start": "next start", "lint": "next lint", "prettier": "prettier --ignore-path .gitignore --write \"./src/**/*.+(js|ts|json)\"", "prettier:fix": "npx prettier --write src"}, "dependencies": {"@hookform/resolvers": "^3.10.0", "@radix-ui/react-accordion": "^1.2.2", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-aspect-ratio": "^1.1.1", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-checkbox": "^1.1.3", "@radix-ui/react-collapsible": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-radio-group": "^1.3.2", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-separator": "^1.1.1", "@radix-ui/react-slider": "^1.3.2", "@radix-ui/react-slot": "^1.2.0", "@radix-ui/react-switch": "^1.1.2", "@radix-ui/react-tabs": "^1.1.7", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-tooltip": "^1.1.6", "@reduxjs/toolkit": "^2.5.0", "@tanstack/react-table": "^8.20.6", "@types/lodash": "^4.17.14", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "1.0.0", "date-fns": "^4.1.0", "embla-carousel-autoplay": "^8.6.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.4.7", "jodit-react": "^5.2.14", "lodash": "^4.17.21", "lodash.debounce": "^4.0.8", "lucide-react": "^0.468.0", "motion": "^11.18.1", "next": "15.1.2", "next-themes": "^0.4.4", "react": "^19.0.0", "react-day-picker": "8.10.1", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-icons": "^5.4.0", "react-redux": "^9.2.0", "react-share": "^5.2.2", "redux": "^5.0.1", "redux-persist": "^6.0.0", "sonner": "^1.7.2", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.1"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@tailwindcss/typography": "^0.5.16", "@types/node": "^20.17.14", "@types/react": "^19.0.7", "@types/react-dom": "^19.0.3", "eslint": "^9.18.0", "eslint-config-next": "15.1.2", "postcss": "^8.5.1", "tailwindcss": "^3.4.17", "typescript": "^5.7.3"}}