import { FlatCompat } from "@eslint/eslintrc";
import { dirname } from "path";
import { fileURLToPath } from "url";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const compat = new FlatCompat({
	baseDirectory: __dirname
});

const eslintConfig = [
	...compat.extends("next/core-web-vitals", "next/typescript"),

	{
		files: ["**/*.ts", "**/*.tsx"],
		languageOptions: {
			parserOptions: {
				project: "./tsconfig.json"
			}
		},
		rules: {
			"@typescript-eslint/no-unused-vars": "off", // or "warn"
			"@typescript-eslint/no-explicit-any": "off", // or "warn"
			"@typescript-eslint/ban-ts-comment": "off", // or "warn"
			"@next/next/no-html-link-for-pages": "off", // or "warn"
			"react/no-unescaped-entities": "off"
		}
	}
];

export default eslintConfig;
