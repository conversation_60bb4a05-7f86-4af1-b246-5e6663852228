import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
	return twMerge(clsx(inputs));
}

export function doubleDigitListNum(index: number) {
	return index < 9 ? `0${index + 1}` : index + 1;
}

export function dateToLocaleString(date: string) {
	return new Date(date).toLocaleDateString("en-GB", {
		year: "numeric",
		month: "short",
		day: "numeric"
	});
}

export function RestoreName(name: string) {
	return name.replace(/-/g, " ").replace(/\b\w/g, (char) => char.toUpperCase());
}

export const formatCurrency = (amount: number): string => {
	return new Intl.NumberFormat("en-US", {
		style: "currency",
		currency: "BDT"
	}).format(amount);
};

// Update the dateToLocaleString function to handle both string and Date objects
export const dateToLocaleStringV2 = (date: string | Date): string => {
	if (!date) return "N/A";

	// Convert to Date object if it's a string
	const dateObj = typeof date === "string" ? new Date(date) : date;

	// Check if the date is valid
	if (isNaN(dateObj.getTime())) return "Invalid Date";

	// Format the date
	return dateObj.toLocaleString("en-US", {
		year: "numeric",
		month: "short",
		day: "numeric",
		hour: "2-digit",
		minute: "2-digit"
	});
};
