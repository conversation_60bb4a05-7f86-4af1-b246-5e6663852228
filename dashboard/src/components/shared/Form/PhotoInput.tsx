import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import Image from "next/image";
import React from "react";

type TPhotoInputProps = {
	// eslint-disable-next-line @typescript-eslint/no-explicit-any
	form: any;
	name?: string;
	boxSize?: string;
	idealRatio?: string;
};

const PhotoInput = ({ form, name = "image", boxSize, idealRatio }: TPhotoInputProps) => {
	const fileInputRef = React.useRef<HTMLInputElement | null>(null);
	const [imagePreview, setImagePreview] = React.useState<string | null>(null);

	const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
		const file = e.target.files?.[0];
		if (file) {
			form.setValue(name, file);
			setImagePreview(URL.createObjectURL(file));
		}
	};

	const handleRemoveImage = () => {
		form.setValue(name, null);
		setImagePreview(null);
		if (fileInputRef.current) {
			fileInputRef.current.value = "";
		}
	};

	return (
		<div className='my-4'>
			<div
				className={cn(
					"flex items-center space-x-4 h-44 w-44 mx-auto justify-center border border-gray-300 rounded-md border-dashed",
					boxSize
				)}
			>
				{imagePreview ? (
					<div className='relative h-full w-full'>
						{/* <ImageWithFallback
							height={100}
							width={100}
							src={imagePreview}
							alt='Selected Preview'
							className='rounded-md border border-gray-300 object-cover w-full h-full'
						/> */}
						<Image
							src={imagePreview}
							alt='Selected Preview'
							layout='fill'
							objectFit='cover'
							className='rounded-md border border-gray-300'
						/>
						<Button
							type='button'
							onClick={handleRemoveImage}
							variant={"ghost"}
							className='absolute top-1 right-1 text-red-500 h-[24px] w-[24px] flex items-center justify-center text-xl'
							aria-label='Remove Image'
						>
							&times;
						</Button>
					</div>
				) : (
					<Button
						variant='ghost'
						type='button'
						className='h-full w-full flex flex-col items-center justify-center'
						onClick={() => fileInputRef.current?.click()}
					>
						<span>Upload Image</span>
						<span className='text-gray-500 text-xs'>Ideal size: 1-5 MB</span>
						<span className='text-xs text-gray-500'>Formats: JPG, PNG</span>
						<span className='text-xs text-gray-500'>Ideal Ratio: {idealRatio ? idealRatio : "1:1"}</span>
					</Button>
				)}
			</div>
			<input
				type='file'
				id='image'
				accept='image/*'
				ref={fileInputRef}
				onChange={handleImageChange}
				className='hidden'
			/>
			{form.formState.errors.image && (
				<p className='text-red-500 text-sm mt-1'>{form.formState.errors.image?.message?.toString()}</p>
			)}
		</div>
	);
};

export default PhotoInput;
