"use client";

import ImageWithFallback from "@/components/shared/ImageWithFallback";
import { Button } from "@/components/ui/button";
import Loading from "@/components/ui/loading";
import { useToast } from "@/hooks/use-toast";
import { useUpdateBrandsImageMutation } from "@/redux/apis/brandsApis";
import { useUpdatePrimaryCategoryImageMutation } from "@/redux/apis/primaryCategoryApis";
import { useUpdateSecondaryCategoryImageMutation } from "@/redux/apis/secondaryCategoryApis";
import { useUpdateTertiaryCategoryImageMutation } from "@/redux/apis/tertiaryCategoryApis";
import { useRef, useState } from "react";

interface UpdateCategoryImageProps {
	id: string; // Category ID
	imageItemType: "primaryCategory" | "secondaryCategory" | "tertiaryCategory" | "brand" | "offer"; // Type of category
}

const UpdateSingleImage = ({ id, imageItemType }: UpdateCategoryImageProps) => {
	// API mutations
	const [updatePrimaryImage, { isLoading: isUpdatingPrimaryCategory }] = useUpdatePrimaryCategoryImageMutation();
	const [updateSecondaryImage, { isLoading: isUpdatingSecondaryCategory }] = useUpdateSecondaryCategoryImageMutation();
	const [updateTertiaryImage, { isLoading: isUpdatingTertiaryCategory }] = useUpdateTertiaryCategoryImageMutation();
	const [updateBrandImage, { isLoading: isUpdatingBrand }] = useUpdateBrandsImageMutation();

	// Determine if any image update is in progress
	const isUpdating =
		isUpdatingPrimaryCategory || isUpdatingSecondaryCategory || isUpdatingTertiaryCategory || isUpdatingBrand;

	// State to manage image preview
	const [imagePreview, setImagePreview] = useState<string | null>(null);

	// Ref for the file input element
	const fileInputRef = useRef<HTMLInputElement | null>(null);

	// Toast utility for displaying messages
	const { toast } = useToast();

	/**
	 * Handle file input change and upload the selected image.
	 * @param e File input change event
	 */
	const handleImageChange = async (e: React.ChangeEvent<HTMLInputElement>) => {
		const file = e.target.files?.[0];

		if (file) {
			// Generate a preview URL
			setImagePreview(URL.createObjectURL(file));

			// Validate file size
			const maxSizeInMB = 5; // Maximum file size in MB
			if (file.size > maxSizeInMB * 1024 * 1024) {
				toast({
					title: "File too large",
					description: `Please select a file smaller than ${maxSizeInMB} MB.`,
					variant: "destructive"
				});
				return;
			}

			// Validate file type
			const validFileTypes = ["image/jpeg", "image/png"];
			if (!validFileTypes.includes(file.type)) {
				toast({
					title: "Invalid file format",
					description: "Only JPG and PNG formats are supported.",
					variant: "destructive"
				});
				return;
			}

			// Start uploading the file
			const formData = new FormData();
			formData.append("image", file);

			try {
				let res;

				switch (imageItemType) {
					case "primaryCategory":
						res = await updatePrimaryImage({ id, data: formData }).unwrap();
						break;
					case "secondaryCategory":
						res = await updateSecondaryImage({ id, data: formData }).unwrap();
						break;
					case "tertiaryCategory":
						res = await updateTertiaryImage({ id, data: formData }).unwrap();
						break;
					case "brand":
						res = await updateBrandImage({ id, data: formData }).unwrap();
						break;
					default:
						throw new Error("Invalid category type");
				}

				// Show success message
				if (res.success) {
					toast({
						description: "Image updated successfully."
					});
				}
			} catch (error: unknown) {
				// Handle errors and show appropriate messages
				toast({
					title: "Error",
					description: (error as { data?: { message?: string } })?.data?.message ?? "Something went wrong!",
					variant: "destructive"
				});
			}
		}
	};

	return (
		<div className='mt-4'>
			{/* Image Preview or Upload Button */}
			<div className='flex items-center space-x-4 h-44 w-44 mx-auto justify-center border border-gray-300 rounded-md border-dashed relative'>
				{imagePreview ? (
					<div className='relative h-full w-full'>
						<ImageWithFallback
							height={100}
							width={100}
							src={imagePreview}
							alt='Selected Preview'
							className='rounded-md border border-gray-300 object-cover w-full h-full'
						/>
					</div>
				) : (
					<Button
						variant='ghost'
						type='button'
						className='h-full w-full flex flex-col items-center justify-center'
						onClick={() => fileInputRef.current?.click()}
						disabled={isUpdating}
					>
						<span>Update Image</span>
						<span className='text-gray-500 text-xs'>Max size: 5MB</span>
						<span className='text-xs text-gray-500'>Formats: JPG, PNG</span>
						<span className='text-xs text-gray-500'>Min size: 100x100</span>
					</Button>
				)}
				{isUpdating && (
					<div className='absolute h-full w-full flex items-center justify-center bg-gray-200 bg-opacity-50 rounded-md right-0  z-30 '>
						<Loading showText={false} />
					</div>
				)}
			</div>

			{/* Hidden File Input */}
			<input
				type='file'
				id='image'
				accept='image/*'
				ref={fileInputRef}
				onChange={handleImageChange}
				className='hidden'
			/>
		</div>
	);
};

export default UpdateSingleImage;
