"use client";
import { Card } from "@/components/ui/card";
import { cn } from "@/lib/utils";
import {
	Area,
	Bar,
	CartesianGrid,
	Legend,
	AreaChart as <PERSON><PERSON>rts<PERSON>rea<PERSON><PERSON>,
	Bar<PERSON>hart as <PERSON><PERSON>rts<PERSON><PERSON><PERSON><PERSON>,
	ResponsiveContainer,
	Tooltip,
	type TooltipProps,
	XAxis,
	YAxis
} from "recharts";

// Custom tooltip component
export function CustomTooltip({
	active,
	payload,
	label,
	formatter
}: TooltipProps<any, any> & { formatter?: (value: number) => string }) {
	if (active && payload && payload.length) {
		return (
			<Card className='p-3 shadow-lg border border-border bg-background text-foreground'>
				<p className='text-sm font-medium mb-1'>{label}</p>
				{payload.map((entry, index) => (
					<div key={`tooltip-${index}`} className='flex items-center gap-2'>
						<div className='w-3 h-3 rounded-full' style={{ backgroundColor: entry.color }} />
						<p className='text-sm'>
							{entry.name}: {formatter ? formatter(entry.value) : entry.value}
						</p>
					</div>
				))}
			</Card>
		);
	}

	return null;
}

// Area Chart Component
interface AreaChartProps {
	data: any[];
	xAxisKey: string;
	dataKey: string;
	height?: number;
	className?: string;
	valueFormatter?: (value: number) => string;
	color?: string;
	showGrid?: boolean;
	showLegend?: boolean;
}

export function AreaChartComponent({
	data,
	xAxisKey,
	dataKey,
	height = 300,
	className,
	valueFormatter,
	color = "hsl(var(--chart-1))",
	showGrid = true,
	showLegend = false
}: AreaChartProps) {
	return (
		<div className={cn("w-full", className)}>
			<ResponsiveContainer width='100%' height={height}>
				<RechartsAreaChart data={data} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
					{showGrid && <CartesianGrid strokeDasharray='3 3' vertical={false} stroke='hsl(var(--border))' />}
					<XAxis
						dataKey={xAxisKey}
						stroke='hsl(var(--muted-foreground))'
						fontSize={12}
						tickLine={false}
						axisLine={{ stroke: "hsl(var(--border))" }}
					/>
					<YAxis
						stroke='hsl(var(--muted-foreground))'
						fontSize={12}
						tickLine={false}
						axisLine={{ stroke: "hsl(var(--border))" }}
						tickFormatter={valueFormatter}
					/>
					<Tooltip content={<CustomTooltip formatter={valueFormatter} />} />
					{showLegend && <Legend />}
					<Area type='monotone' dataKey={dataKey} stroke={color} fill={color} fillOpacity={0.2} name={dataKey} />
				</RechartsAreaChart>
			</ResponsiveContainer>
		</div>
	);
}

// Bar Chart Component
interface BarChartProps {
	data: any[];
	xAxisKey: string;
	dataKey: string;
	height?: number;
	className?: string;
	valueFormatter?: (value: number) => string;
	color?: string;
	showGrid?: boolean;
	showLegend?: boolean;
}

export function BarChartComponent({
	data,
	xAxisKey,
	dataKey,
	height = 300,
	className,
	valueFormatter,
	color = "hsl(var(--chart-1))",
	showGrid = true,
	showLegend = false
}: BarChartProps) {
	return (
		<div className={cn("w-full", className)}>
			<ResponsiveContainer width='100%' height={height}>
				<RechartsBarChart data={data} margin={{ top: 10, right: 30, left: 0, bottom: 5 }}>
					{showGrid && <CartesianGrid strokeDasharray='3 3' vertical={false} stroke='hsl(var(--border))' />}
					<XAxis
						dataKey={xAxisKey}
						stroke='hsl(var(--muted-foreground))'
						fontSize={12}
						tickLine={false}
						axisLine={{ stroke: "hsl(var(--border))" }}
					/>
					<YAxis
						stroke='hsl(var(--muted-foreground))'
						fontSize={12}
						tickLine={false}
						axisLine={{ stroke: "hsl(var(--border))" }}
						tickFormatter={valueFormatter}
					/>
					<Tooltip content={<CustomTooltip formatter={valueFormatter} />} />
					{showLegend && <Legend />}
					<Bar dataKey={dataKey} fill={color} radius={[4, 4, 0, 0]} name={dataKey} />
				</RechartsBarChart>
			</ResponsiveContainer>
		</div>
	);
}
