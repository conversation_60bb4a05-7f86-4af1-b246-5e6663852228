"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, ResponsiveContainer, Toolt<PERSON> } from "recharts";

interface OrdersByStatusChartProps {
	data: { status: string; count: number }[];
}

const COLORS = ["#8884d8", "#82ca9d", "#ffc658", "#ff8042", "#00C49F", "#FFBB28"];

export function OrdersByStatusChart({ data }: OrdersByStatusChartProps) {
	return (
		<Card>
			<CardHeader>
				<CardTitle>Orders by Status</CardTitle>
			</CardHeader>
			<CardContent className='h-[300px]'>
				<ResponsiveContainer width='100%' height='100%'>
					<PieChart>
						<Pie data={data} dataKey='count' nameKey='status' cx='50%' cy='50%' outerRadius={100} label>
							{data.map((entry, index) => (
								<Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
							))}
						</Pie>
						<Tooltip />
					</PieChart>
				</ResponsiveContainer>
			</CardContent>
		</Card>
	);
}
