import { <PERSON>, Card<PERSON>ontent, <PERSON>Header, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { useGetSalesStatsQuery } from "@/redux/apis/statsApi";
import { Suspense } from "react";
import { DailySalesChart } from "./charts/DailySalesChart";

export default function DashboardSalesTab() {
	const { data: sd, isLoading } = useGetSalesStatsQuery({});

	if (isLoading || !sd) {
		return (
			<div className='grid grid-cols-4 gap-4'>
				{[...Array(4)].map((_, i) => (
					<Card key={i}>
						<CardHeader>
							<Skeleton className='h-5 w-24' />
						</CardHeader>
						<CardContent>
							<Skeleton className='h-8 w-full' />
						</CardContent>
					</Card>
				))}
			</div>
		);
	}
	const data = sd?.data;

	const { overview, dailySales } = data;

	return (
		<div className='space-y-6'>
			<div className='grid grid-cols-5 gap-6'>
				<Card>
					<CardHeader>
						<CardTitle>Total Orders</CardTitle>
					</CardHeader>
					<CardContent className='text-3xl font-semibold'>{overview.totalOrders}</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle>Pending Orders</CardTitle>
					</CardHeader>
					<CardContent className='text-3xl font-semibold'>{overview.pendingOrders}</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle>Delivered Orders</CardTitle>
					</CardHeader>
					<CardContent className='text-3xl font-semibold'>{overview.deliveredOrders}</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle>Cancelled Orders</CardTitle>
					</CardHeader>
					<CardContent className='text-3xl font-semibold'>{overview.cancelledOrders}</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle>Shipped Products</CardTitle>
					</CardHeader>
					<CardContent className='text-3xl font-semibold'>{overview.shippedOrders}</CardContent>
				</Card>
			</div>
			<div className='grid gap-4 grid-cols-1 md:grid-cols-2'>
				<Suspense fallback={<Skeleton className='h-[300px]' />}>
					<Card>
						<CardHeader>
							<CardTitle>Daily Sales</CardTitle>
						</CardHeader>
						<CardContent>
							<DailySalesChart data={dailySales} />
						</CardContent>
					</Card>
				</Suspense>

				{/* <Suspense fallback={<Skeleton className='h-[300px]' />}>
					<Card>
						<CardHeader>
							<CardTitle>Orders by Status</CardTitle>
						</CardHeader>
						<CardContent>
							<OrdersByStatusChart data={ordersByStatus} />
						</CardContent>
					</Card>
				</Suspense> */}
			</div>
		</div>
	);
}
