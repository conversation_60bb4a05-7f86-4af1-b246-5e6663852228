// DailySalesChart.tsx
"use client";

import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { CartesianGrid, Line, LineChart, ResponsiveContainer, <PERSON>lt<PERSON>, <PERSON><PERSON><PERSON>s, <PERSON><PERSON>xi<PERSON> } from "recharts";

interface DailySalesChartProps {
	data: { _id: string; total: number; count: number }[];
}

export function DailySalesChart({ data }: DailySalesChartProps) {
	return (
		<Card>
			<CardHeader>
				<CardTitle>Daily Sales</CardTitle>
			</CardHeader>
			<CardContent className='h-[300px]'>
				<ResponsiveContainer width='100%' height='100%'>
					<LineChart data={data} margin={{ top: 10, right: 30, left: 0, bottom: 0 }}>
						<CartesianGrid strokeDasharray='3 3' />
						<XAxis dataKey='_id' tick={{ fontSize: 12 }} />
						<YAxis tick={{ fontSize: 12 }} />
						<Tooltip formatter={(value: number) => `৳${value.toFixed(2)}`} />
						<Line type='monotone' dataKey='total' stroke='#8884d8' strokeWidth={2} name='Revenue' />
					</LineChart>
				</ResponsiveContainer>
			</CardContent>
		</Card>
	);
}
