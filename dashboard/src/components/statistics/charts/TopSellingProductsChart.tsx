"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { <PERSON>, <PERSON><PERSON><PERSON>, CartesianGrid, ResponsiveContainer, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>A<PERSON><PERSON> } from "recharts";

interface TopSellingProduct {
	name: string;
	totalSold: number;
}

interface OverviewChartProps {
	topSellingProducts: TopSellingProduct[];
}

export default function TopSellingProductsChart({ topSellingProducts }: OverviewChartProps) {
	return (
		<Card className='max-w-full'>
			<CardHeader>
				<CardTitle>Top Selling Products</CardTitle>
			</CardHeader>
			<CardContent style={{ height: 300 }}>
				<ResponsiveContainer width='100%' height='100%'>
					<BarChart data={topSellingProducts} margin={{ top: 20, right: 30, left: 20, bottom: 50 }}>
						<CartesianGrid strokeDasharray='3 3' />
						<XAxis dataKey='name' angle={-45} textAnchor='end' interval={0} height={60} tick={{ fontSize: 12 }} />
						<YAxis allowDecimals={false} />
						<Tooltip />
						<Bar dataKey='totalSold' fill='#3b82f6' radius={[4, 4, 0, 0]} />
					</BarChart>
				</ResponsiveContainer>
			</CardContent>
		</Card>
	);
}
