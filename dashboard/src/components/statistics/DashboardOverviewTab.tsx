"use client";

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { formatCurrency } from "@/lib/utils"; // Your currency formatter
import { useGetOverviewStatsQuery } from "@/redux/apis/statsApi";
import TopSellingProductsChart from "./charts/TopSellingProductsChart";

type ProductSummary = {
	_id: string;
	name: string;
	slug: string;
	images: string[];
	totalSold: number;
};

type OverviewData = {
	totalOrders: number;
	totalRevenue: number;
	totalProducts: number;
	pendingOrders: number;
	deliveredOrders: number;
	cancelledOrders: number;
	topSellingProducts: ProductSummary[];
};

export default function DashboardOverviewTab() {
	const { data: ov, isLoading } = useGetOverviewStatsQuery({});
	const data: OverviewData = ov?.data;
	console.log(ov, "ovvvvvv");

	if (isLoading || !data) {
		return (
			<div className='grid grid-cols-4 gap-4'>
				{[...Array(4)].map((_, i) => (
					<Card key={i}>
						<CardHeader>
							<Skeleton className='h-5 w-24' />
						</CardHeader>
						<CardContent>
							<Skeleton className='h-8 w-full' />
						</CardContent>
					</Card>
				))}
			</div>
		);
	}

	return (
		<div className='space-y-6'>
			<div className='grid grid-cols-6 gap-6'>
				<Card>
					<CardHeader>
						<CardTitle>Total Orders</CardTitle>
					</CardHeader>
					<CardContent className='text-3xl font-semibold'>{data.totalOrders}</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle>Pending Orders</CardTitle>
					</CardHeader>
					<CardContent className='text-3xl font-semibold'>{data.pendingOrders}</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle>Delivered Orders</CardTitle>
					</CardHeader>
					<CardContent className='text-3xl font-semibold'>{data.deliveredOrders}</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle>Cancelled Orders</CardTitle>
					</CardHeader>
					<CardContent className='text-3xl font-semibold'>{data.cancelledOrders}</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle>Total Revenue</CardTitle>
					</CardHeader>
					<CardContent className='text-3xl font-semibold'>{formatCurrency(data.totalRevenue)}</CardContent>
				</Card>

				<Card>
					<CardHeader>
						<CardTitle>Total Products</CardTitle>
					</CardHeader>
					<CardContent className='text-3xl font-semibold'>{data.totalProducts}</CardContent>
				</Card>
			</div>
			<TopSellingProductsChart topSellingProducts={data?.topSellingProducts} />
		</div>
	);
}
