"use client";

import DashboardInventoryTab from "@/components/statistics/DashboardInventoryTab";
import DashboardOverviewTab from "@/components/statistics/DashboardOverviewTab";
import DashboardSalesTab from "@/components/statistics/DashboardSalesTab";
import { Tabs, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";

const Dashboard = () => {
	// const { data, isLoading } = useGetOverviewStatsQuery({});

	// if (isLoading) {
	// 	return (
	// 		<div className='flex items-center justify-center h-screen'>
	// 			<div className='animate-spin h-8 w-8 border-4 border-primary border-t-transparent rounded-full'></div>
	// 		</div>
	// 	);
	// }

	// if (!data?.data) {
	// 	return (
	// 		<Alert variant='destructive'>
	// 			<AlertCircle className='h-4 w-4' />
	// 			<AlertTitle>Error</AlertTitle>
	// 			<AlertDescription>Failed to load dashboard data. Please try again later.</AlertDescription>
	// 		</Alert>
	// 	);
	// }

	// const { sales, topSellingProducts, inventory } = data.data;

	// // Format revenue trend data for the chart
	// const revenueChartData = sales.revenueTrend.map((item: any) => ({
	// 	date: new Date(item._id).toLocaleDateString("en-US", { month: "short", day: "numeric" }),
	// 	revenue: item.revenue
	// }));

	// // Format top selling products for the chart
	// const productChartData = topSellingProducts.map((product: any) => ({
	// 	name: product.name.length > 15 ? product.name.substring(0, 15) + "..." : product.name,
	// 	revenue: product.revenue,
	// 	units: product.unitsSold
	// }));

	// <TabsContent value='overviewf' className='space-y-6'>
	// 				{/* Key Metrics */}
	// 				<div className='grid gap-4 md:grid-cols-2 lg:grid-cols-3'>
	// 					<Card>
	// 						<CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
	// 							<CardTitle className='text-sm font-medium'>Total Revenue</CardTitle>
	// 							<DollarSign className='h-4 w-4 text-muted-foreground' />
	// 						</CardHeader>
	// 						<CardContent>
	// 							<div className='text-2xl font-bold'>{formatCurrency(sales.totalRevenue)}</div>
	// 							<div className='flex items-center text-xs text-muted-foreground mt-1'>
	// 								<ArrowUpRight className='mr-1 h-3 w-3 text-emerald-500' />
	// 								<span className='text-emerald-500 font-medium'>+12.5%</span> from last month
	// 							</div>
	// 						</CardContent>
	// 					</Card>

	// 					<Card>
	// 						<CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
	// 							<CardTitle className='text-sm font-medium'>Total Orders</CardTitle>
	// 							<ShoppingCart className='h-4 w-4 text-muted-foreground' />
	// 						</CardHeader>
	// 						<CardContent>
	// 							<div className='text-2xl font-bold'>{sales.totalOrders}</div>
	// 							<p className='text-xs text-muted-foreground mt-1'>Lifetime orders</p>
	// 						</CardContent>
	// 					</Card>

	// 					<Card>
	// 						<CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
	// 							<CardTitle className='text-sm font-medium'>Average Order Value</CardTitle>
	// 							<BarChart3 className='h-4 w-4 text-muted-foreground' />
	// 						</CardHeader>
	// 						<CardContent>
	// 							<div className='text-2xl font-bold'>{formatCurrency(sales.averageOrderValue)}</div>
	// 							<p className='text-xs text-muted-foreground mt-1'>Per order average</p>
	// 						</CardContent>
	// 					</Card>
	// 				</div>

	// 				{/* Revenue Trend Chart */}
	// 				<Card>
	// 					<CardHeader>
	// 						<CardTitle>Revenue Trend</CardTitle>
	// 						<CardDescription>Daily revenue over time</CardDescription>
	// 					</CardHeader>
	// 					<CardContent>
	// 						<AreaChartComponent
	// 							data={revenueChartData}
	// 							xAxisKey='date'
	// 							dataKey='revenue'
	// 							height={300}
	// 							valueFormatter={(value) => formatCurrency(value)}
	// 							color='hsl(142.1, 76.2%, 36.3%)' // emerald-600
	// 						/>
	// 					</CardContent>
	// 				</Card>

	// 				{/* Top Selling Products */}
	// 				<Card>
	// 					<CardHeader>
	// 						<CardTitle>Top Selling Products</CardTitle>
	// 						<CardDescription>Products with highest revenue</CardDescription>
	// 					</CardHeader>
	// 					<CardContent>
	// 						<BarChartComponent
	// 							data={productChartData}
	// 							xAxisKey='name'
	// 							dataKey='revenue'
	// 							height={300}
	// 							valueFormatter={(value) => formatCurrency(value)}
	// 							color='hsl(221.2, 83.2%, 53.3%)' // blue-600
	// 						/>
	// 					</CardContent>
	// 				</Card>

	// 				{/* Low Stock Alert */}
	// 				<Card>
	// 					<CardHeader className='flex flex-row items-center'>
	// 						<div>
	// 							<CardTitle>Low Stock Products</CardTitle>
	// 							<CardDescription>Products that need restocking</CardDescription>
	// 						</div>
	// 						<Badge variant='destructive' className='ml-auto'>
	// 							{inventory.lowStockProducts.length} items
	// 						</Badge>
	// 					</CardHeader>
	// 					<CardContent>
	// 						<ScrollArea className='h-[200px]'>
	// 							<div className='space-y-4'>
	// 								{inventory.lowStockProducts.map((product: any) => (
	// 									<div key={product._id} className='flex items-center justify-between'>
	// 										<div className='space-y-1'>
	// 											<p className='text-sm font-medium leading-none'>{product.name}</p>
	// 											<p className='text-sm text-muted-foreground'>ID: {product._id.substring(0, 8)}...</p>
	// 										</div>
	// 										<div className='flex items-center'>
	// 											<Package className='mr-2 h-4 w-4 text-muted-foreground' />
	// 											<span className='font-medium text-destructive'>{product.stock} left</span>
	// 										</div>
	// 									</div>
	// 								))}
	// 							</div>
	// 						</ScrollArea>
	// 					</CardContent>
	// 				</Card>
	// 			</TabsContent>
	return (
		<div className='p-6 space-y-6'>
			<div className='flex flex-col space-y-2'>
				<h1 className='text-3xl font-bold tracking-tight'>Dashboard Overview</h1>
				<p className='text-muted-foreground'>View your store performance and key metrics at a glance.</p>
			</div>

			<Tabs defaultValue='overview' className='space-y-6'>
				<TabsList>
					<TabsTrigger value='overview'>Overview</TabsTrigger>
					<TabsTrigger value='sales'>Sales</TabsTrigger>
					<TabsTrigger value='inventory'>Inventory</TabsTrigger>
				</TabsList>
				<TabsContent value='overview' className='space-y-4'>
					<DashboardOverviewTab />
				</TabsContent>
				<TabsContent value='sales' className='space-y-4'>
					<DashboardSalesTab />
				</TabsContent>

				<TabsContent value='inventory' className='space-y-4'>
					<DashboardInventoryTab />
				</TabsContent>
			</Tabs>
		</div>
	);
};

export default Dashboard;
