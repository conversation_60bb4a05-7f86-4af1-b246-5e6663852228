export type TOrderStatus = "pending" | "shipped" | "delivered" | "canceled";
export interface TProductForOrder {
	name: string;
	slug: string;
	images: string[];
	variants: {
		_id: string;
		color?: { name?: string; value?: string };
		storage?: string;
		region?: string;
		condition?: string;
		price: number;
		stock: number;
		images: string[];
	}[];
}
export interface TOrderItem {
	productId: TProductForOrder;
	variantId?: string;
	quantity: number;
	price: number;
	productName?: string; // Added for display purposes
	productImage?: string; // Added for display purposes
}

export interface TOrder {
	_id: string;
	customerInfo: {
		name: string;
		phone: string;
		address: string;
	};
	orderNumber: string;
	customerName: string;
	orderDate: Date;
	status: TOrderStatus;
	totalAmount: number;
	items: TOrderItem[];
	createdAt: Date;
	updatedAt: Date;
}
