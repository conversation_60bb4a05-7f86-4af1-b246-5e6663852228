import { z } from "zod";

export const productSchema = z
	.object({
		name: z
			.string({
				required_error: "Name is required!"
			})
			.min(3, "Name must be at least 3 characters.")
			.max(50, "Name must be at most 50 characters."),
		description: z
			.string({
				required_error: "Description is required!"
			})
			.min(10, "Description must be at least 10 characters."),
		warrantyInfo: z.string().optional(),
		price: z.coerce
			.number({
				required_error: "Enter a current price!",
				invalid_type_error: "Enter a valid number!"
			})
			.nonnegative("Price must be a positive number.")
			.refine((price) => price > 0, {
				message: "Enter a price!"
			}),
		actualPrice: z.coerce
			.number({
				required_error: "Actual price is required!",
				invalid_type_error: "Enter a valid number!"
			})
			.nonnegative("Actual price must be a positive number."),
		stock: z.coerce.number({
			required_error: "Stock is required!",
			invalid_type_error: "Enter a valid number!"
		}),
		images: z
			.any({
				required_error: "Image is required!"
			})
			.array()
			.min(1, "At least one image is required!"),

		brand: z
			.string({
				required_error: "Select a brand!"
			})
			.min(3, "Select a brand!"),
		primaryCategory: z
			.string({
				required_error: "Primary category is required!"
			})
			.min(3, "Select a primary category!"),
		secondaryCategory: z
			.string({
				required_error: "Select a secondary category!"
			})
			.min(3, "Select a secondary category!"),
		tertiaryCategory: z
			.string()
			.optional()
			.transform((val) => (val?.trim() === "" ? undefined : val))
	})
	.superRefine((data) => {
		if (!data.tertiaryCategory) {
			delete data.tertiaryCategory; // Remove if empty
		}
	});

export const updateProductSchema = z
	.object({
		name: z
			.string({
				required_error: "Name is required!"
			})
			.min(3, "Name must be at least 3 characters.")
			.max(50, "Name must be at most 50 characters."),
		description: z
			.string({
				required_error: "Description is required!"
			})
			.min(10, "Description must be at least 10 characters."),
		warrantyInfo: z.string().optional(),
		price: z.coerce
			.number({
				required_error: "Enter a current price!",
				invalid_type_error: "Enter a valid number!"
			})
			.nonnegative("Price must be a positive number.")
			.refine((price) => price > 0, {
				message: "Enter a price!"
			}),
		actualPrice: z.coerce
			.number({
				required_error: "Actual price is required!",
				invalid_type_error: "Enter a valid number!"
			})
			.nonnegative("Actual price must be a positive number."),
		stock: z.coerce.number({
			required_error: "Stock is required!",
			invalid_type_error: "Enter a valid number!"
		}),

		brand: z
			.string({
				required_error: "Select a brand!"
			})
			.min(3, "Select a brand!"),
		primaryCategory: z
			.string({
				required_error: "Primary category is required!"
			})
			.min(3, "Select a primary category!"),
		secondaryCategory: z
			.string({
				required_error: "Select a secondary category!"
			})
			.min(3, "Select a secondary category!"),
		tertiaryCategory: z
			.string()
			.optional()
			.transform((val) => (val?.trim() === "" ? undefined : val))
	})
	.superRefine((data) => {
		if (!data.tertiaryCategory) {
			delete data.tertiaryCategory; // Remove if empty
		}
	});
