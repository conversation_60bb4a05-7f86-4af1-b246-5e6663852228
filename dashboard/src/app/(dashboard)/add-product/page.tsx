"use client";

import EInput from "@/components/shared/Form/EInput";
import TextEditor from "@/components/shared/Form/TextEditor";
import PageTitle from "@/components/shared/PageTitle";
import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import { useToast } from "@/hooks/use-toast";
import { useCreateProductMutation } from "@/redux/apis/productApis";
import { zodResolver } from "@hookform/resolvers/zod";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import AdditionalDetails from "./components/AdditionalDetails";
import BrandSelector from "./components/BrandSelector";
import ImageUploader from "./components/ImageUploader";
import PrimaryCategorySelector from "./components/PrimaryCategorySelector";
import SecondaryCategorySelector from "./components/SecondaryCategorySelector";
import TertiaryCategorySelector from "./components/TertiaryCategorySelector";
import { productSchema } from "./validation";

interface AddedField {
	key: string; // slug of the field
	value: string;
	description: string;
}

const AddProductPage = () => {
	const [createProduct, { isLoading: isCreating }] = useCreateProductMutation();
	const [resetTrigger, setResetTrigger] = useState(false);
	const { toast } = useToast();

	// State for the added fields.
	const [addedFields, setAddedFields] = useState<AddedField[]>([]);
	// State for filterable select fields IDs (for select-type fields only).
	const [filterableFields, setFilterableFields] = useState<string[]>([]);

	// Form setup with validation schema and default values
	const form = useForm<z.infer<typeof productSchema>>({
		resolver: zodResolver(productSchema),
		defaultValues: {
			name: "",
			price: 0,
			actualPrice: 0,
			stock: 0,
			description: "",
			warrantyInfo: "",
			brand: "",
			primaryCategory: "",
			secondaryCategory: ""
		}
	});

	const onSubmit = async (data: z.infer<typeof productSchema>) => {
		const formData = new FormData();

		if (data.images && data.images.length > 0) {
			data.images.forEach((image: File) => {
				formData.append("images", image);
			});
		}
		formData.append(
			"data",
			JSON.stringify({
				...data,
				filterableFields,
				additionalInfo: addedFields
			})
		);

		try {
			const res = await createProduct(formData).unwrap();

			if (res.success) {
				toast({
					description: "Product added successfully!"
				});
				form.reset();
				setResetTrigger((prev) => !prev);
			}
		} catch (error: unknown) {
			toast({
				title: "Error",
				description: (error as { data?: { message?: string } })?.data?.message ?? "Something went wrong!",
				variant: "destructive"
			});
		}
	};

	return (
		<Form {...form}>
			<PageTitle title='Add Product' />
			<form onSubmit={form.handleSubmit(onSubmit)}>
				{/* name, prices and stock inputs */}
				<div className='flex flex-row gap-4 flex-wrap lg:flex-nowrap'>
					<EInput form={form} name='name' placeholder='Product name' />

					<div className='flex flex-row gap-4 flex-wrap md:flex-nowrap'>
						<EInput form={form} name='actualPrice' placeholder='Actual Price' type='number' />
						<EInput form={form} name='price' placeholder='Current Price' type='number' />
						<EInput form={form} name='stock' placeholder='Stock' type='number' />
					</div>
				</div>

				<TextEditor form={form} name='description' placeholder='Description' />
				<EInput form={form} name='warrantyInfo' placeholder='Warranty Information' />

				{/* Brand, categories selectors */}
				<div className='flex flex-col md:flex-row justify-between gap-4 mt-6'>
					<div className='flex flex-col md:flex-row gap-4 flex-wrap lg:flex-nowrap w-full'>
						<BrandSelector form={form} />
						<PrimaryCategorySelector form={form} />
					</div>

					<div className='flex flex-col md:flex-row gap-4 flex-wrap lg:flex-nowrap w-full'>
						<SecondaryCategorySelector form={form} />
						<TertiaryCategorySelector form={form} />
					</div>
				</div>

				<AdditionalDetails
					addedFields={addedFields}
					setFilterableFields={setFilterableFields}
					setAddedFields={setAddedFields}
				/>

				<ImageUploader form={form} resetTrigger={resetTrigger} />

				<Button type='submit' className='w-full mt-12' disabled={isCreating} aria-busy={isCreating}>
					{isCreating ? "Adding..." : "Add Product"}
				</Button>
			</form>
		</Form>
	);
};

export default AddProductPage;
