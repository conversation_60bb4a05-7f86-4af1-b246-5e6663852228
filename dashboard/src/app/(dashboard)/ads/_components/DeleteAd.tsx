"use client";
import ConfirmationDialog from "@/components/shared/ConfirmationDialog";
import { useToast } from "@/hooks/use-toast";
import { useDeleteAdMutation } from "@/redux/apis/adsApi";
import { Trash } from "lucide-react";

interface DeleteAdProps {
	id: string; // ID of the offer to delete
}

const DeleteAd = ({ id }: DeleteAdProps) => {
	// Mutations for deleting categories based on their type
	const [deleteOffer, { isLoading }] = useDeleteAdMutation();

	// Toast utility for displaying success or error messages
	const { toast } = useToast();

	/**
	 * Handles offer deletion based on the offer type
	 */
	const handleDelete = async () => {
		try {
			const res = await deleteOffer(id).unwrap();

			// Check if the deletion was successful
			if (res.success) {
				toast({
					description: "Offer deleted successfully."
				});
			} else {
				// If the deletion failed, show an error toast
				toast({
					title: "Deletion failed",
					description: "Something went wrong! Please try again.",
					variant: "destructive"
				});
			}
			// eslint-disable-next-line @typescript-eslint/no-explicit-any
		} catch (error: any) {
			// Extract error messages and display them in a toast
			const errorMessage =
				error?.data?.errorSources?.map((source: { message: string }) => source.message).join(", ") ||
				"An unexpected error occurred. Please try again.";

			toast({
				title: "Update failed",
				description: errorMessage,
				variant: "destructive"
			});
		}
	};

	return (
		<ConfirmationDialog
			onAgree={handleDelete}
			triggerIcon={<Trash />}
			title='Are you sure you want to delete this offer?'
			description='Once deleted, you will not be able to recover this offer. All products under this offer will be deleted as well.'
			isLoading={isLoading} // Disable button while deletion is in progress
		/>
	);
};

export default DeleteAd;
