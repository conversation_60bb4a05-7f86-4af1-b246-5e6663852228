"use client";

import AdColumn from "@/columns/AdColumn";
import PreOrderColumn from "@/columns/PreOrderColumn";
import DateRangePicker from "@/components/shared/DateRangePicker";
import ETableBody from "@/components/shared/ETableBody";
import ETableHeader from "@/components/shared/ETableHeader";
import SearchBox from "@/components/shared/Form/SearchBox";
import PageTitle from "@/components/shared/PageTitle";
import PaginationCompo from "@/components/shared/PaginationCompo";
import TableColumnDropdownMenu from "@/components/shared/TableColumnDropdownMenu";
import { Table } from "@/components/ui/table";
import { useTable } from "@/hooks/useTable";
import { useGetAllAdsQuery } from "@/redux/apis/adsApi";
import * as React from "react";

const Ads = () => {
	const [search, setSearch] = React.useState<string>("");
	const [page, setPage] = React.useState<number>(1);
	const [date, setDate] = React.useState<{ from: Date | undefined; to: Date | undefined } | undefined>({
		from: undefined,
		to: undefined
	});
	const queryParams = [
		{
			name: "search",
			value: search
		},
		{
			name: "page",
			value: page.toString()
		}
	];

	// If date is selected, add startDate and endDate to the query params
	if (date?.from && date?.to) {
		queryParams.push({
			name: "startDate",
			value: date.from.toISOString()
		});

		// Adjust endDate to the last millisecond of the selected day
		const endDate = new Date(date.to);
		endDate.setUTCHours(23, 59, 59, 999);

		queryParams.push({
			name: "endDate",
			value: endDate.toISOString()
		});
	}

	const { data, isLoading } = useGetAllAdsQuery(queryParams);

	const table = useTable({
		data: data?.data ?? [],
		columns: AdColumn
	});

	return (
		<section>
			<PageTitle title='Ads' />
			{/* search field and filter options */}
			<div className='flex items-center pb-4 gap-2 flex-col sm:flex-row'>
				<div className='flex flex-col-reverse md:flex-row gap-2 w-full flex-1 '>
					<SearchBox setSearch={setSearch} />
					<DateRangePicker className='' date={date} setDate={setDate} />
				</div>

				<div className='flex space-x-2 justify-end w-full flex-1'>
					<TableColumnDropdownMenu table={table} />
				</div>
			</div>

			<div className='rounded-md border'>
				<Table>
					<ETableHeader table={table} />
					<ETableBody table={table} columns={PreOrderColumn} isLoading={isLoading} />
				</Table>
			</div>

			<PaginationCompo
				page={data?.meta.page ?? 1}
				totalPages={data?.meta.totalPages ?? 1}
				setPage={setPage}
				className='py-4'
			/>
		</section>
	);
};

export default Ads;
