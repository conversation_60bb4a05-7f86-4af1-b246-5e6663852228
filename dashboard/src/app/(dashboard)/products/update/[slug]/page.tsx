/* eslint-disable @typescript-eslint/no-unused-vars */
"use client";

import AdditionalDetails from "@/app/(dashboard)/add-product/components/AdditionalDetails";
import BrandSelector from "@/app/(dashboard)/add-product/components/BrandSelector";
import PrimaryCategorySelector from "@/app/(dashboard)/add-product/components/PrimaryCategorySelector";
import SecondaryCategorySelector from "@/app/(dashboard)/add-product/components/SecondaryCategorySelector";
import TertiaryCategorySelector from "@/app/(dashboard)/add-product/components/TertiaryCategorySelector";
import { updateProductSchema } from "@/app/(dashboard)/add-product/validation";
import EmptyPage from "@/components/shared/EmptyPage";
import EInput from "@/components/shared/Form/EInput";
import TextEditor from "@/components/shared/Form/TextEditor";
import PageTitle from "@/components/shared/PageTitle";
import { Button } from "@/components/ui/button";
import { Form } from "@/components/ui/form";
import Loading from "@/components/ui/loading";
import { useToast } from "@/hooks/use-toast";
import { useGetProductBySlugQuery, useUpdateProductMutation } from "@/redux/apis/productApis";
import { zodResolver } from "@hookform/resolvers/zod";
import { useParams, useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import ManageProductImages from "../../components/ManageProductImages";

interface AddedField {
	key: string; // slug of the field
	value: string;
	description: string;
}

const UpdateProduct = () => {
	// slug form params
	const { slug } = useParams();
	const { data, isLoading, isError } = useGetProductBySlugQuery(slug as string);
	const navigate = useRouter();

	const { toast } = useToast();

	// update mutation
	const [updateProduct, { isLoading: isUpdating }] = useUpdateProductMutation();

	// State for the added fields.
	const [addedFields, setAddedFields] = useState<AddedField[]>([]);

	// remove unwanted fields

	// State for filterable select fields IDs (for select-type fields only).
	const [filterableFields, setFilterableFields] = useState<string[]>([]);

	// Form setup with validation schema and default values
	const form = useForm<z.infer<typeof updateProductSchema>>({
		resolver: zodResolver(updateProductSchema),
		defaultValues: {}
	});

	// Effect to update form values when data is available
	useEffect(() => {
		if (data?.data) {
			const {
				images,
				variants,
				updatedAt,
				isActive,
				filterableFields: oldFilterableFields,
				additionalInfo,
				createdAt,
				_v,
				...product
			} = data.data;

			const updatedDefaults = {
				...product,
				brand: product?.brand?._id,
				primaryCategory: product?.primaryCategory?._id,
				secondaryCategory: product?.secondaryCategory?._id
			};
			setFilterableFields(oldFilterableFields?.map((field: { _id: string }) => field._id));

			setAddedFields(additionalInfo);

			if (product?.tertiaryCategory?._id) {
				updatedDefaults.tertiaryCategory = product?.tertiaryCategory?._id;
			}

			form.reset(updatedDefaults);
		}
	}, [data, form]);

	const onSubmit = async (formData: z.infer<typeof updateProductSchema>) => {
		try {
			const res = await updateProduct({
				id: data?.data._id as string,
				data: {
					...formData,
					filterableFields,
					additionalInfo: addedFields
				}
			}).unwrap();

			if (res.success) {
				toast({
					description: "Product added successfully!"
				});
				navigate.push("/products");
			}
		} catch (error: unknown) {
			toast({
				title: "Error",
				description: (error as { data?: { message?: string } })?.data?.message ?? "Something went wrong!",
				variant: "destructive"
			});
		}
	};

	if (isLoading) return <Loading />;
	if (isError) return <EmptyPage />;

	return (
		<div>
			<div className='flex justify-between items-center'>
				<PageTitle title='Update Product' />

				<ManageProductImages images={data?.data.images} productId={data?.data._id} />
			</div>
			<Form {...form}>
				<form onSubmit={form.handleSubmit(onSubmit)}>
					{/* name, prices and stock inputs */}
					<div className='flex flex-row gap-4 flex-wrap lg:flex-nowrap'>
						<EInput form={form} name='name' placeholder='Product name' />

						<div className='flex flex-row gap-4 flex-wrap md:flex-nowrap'>
							<EInput form={form} name='actualPrice' placeholder='Actual Price' type='number' />
							<EInput form={form} name='price' placeholder='Current Price' type='number' />
							<EInput form={form} name='stock' placeholder='Stock' type='number' />
						</div>
					</div>

					<TextEditor form={form} name='description' placeholder='Description' />
					<EInput form={form} name='warrantyInfo' placeholder='Warranty Information' />

					{/* Brand, categories selectors */}
					<div className='flex flex-col md:flex-row justify-between gap-4 mt-6'>
						<div className='flex flex-col md:flex-row gap-4 flex-wrap lg:flex-nowrap w-full'>
							<BrandSelector form={form} />
							<PrimaryCategorySelector form={form} />
						</div>

						<div className='flex flex-col md:flex-row gap-4 flex-wrap lg:flex-nowrap w-full'>
							<SecondaryCategorySelector form={form} />
							<TertiaryCategorySelector form={form} />
						</div>
					</div>

					<AdditionalDetails
						addedFields={addedFields}
						setFilterableFields={setFilterableFields}
						setAddedFields={setAddedFields}
					/>

					<Button type='submit' className='w-full mt-12' disabled={isUpdating} aria-busy={isUpdating}>
						{isUpdating ? "Updating" : "Update"}
					</Button>
				</form>
			</Form>
		</div>
	);
};

export default UpdateProduct;
