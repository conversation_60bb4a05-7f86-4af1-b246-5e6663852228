import { z } from "zod";

// Helper function for file validation
const fileValidation = (fieldName: string) =>
	z
		.instanceof(File, { message: `${fieldName} must be a valid file` })
		.optional()
		.refine((file) => !file || file.size <= 5 * 1024 * 1024, { message: `${fieldName} must be less than 5MB` })
		.refine((file) => !file || ["image/jpeg", "image/jpg", "image/png", "image/webp"].includes(file.type), {
			message: `${fieldName} must be a valid image file (JPEG, PNG, or WebP)`
		});

export const createOfferSchema = z.object({
	name: z
		.string({
			required_error: "Name is required!"
		})
		.min(3, "Name must be at least 3 characters.")
		.max(50, "Name must be at most 50 characters."),
	description: z
		.string({
			required_error: "Description is required!"
		})
		.min(10, "Description must be at least 10 characters."),
	startAt: z.date({ required_error: "Start date is required" }),
	endAt: z.date({ required_error: "End date is required" }),
	products: z.array(z.string()),
	// Add validation for profilePicture
	profilePicture: fileValidation("Profile picture"),
	// Add validation for banner
	banner: fileValidation("Banner image")
});

export const updateOfferSchema = z.object({
	name: z
		.string({
			required_error: "Name is required!"
		})
		.min(3, "Name must be at least 3 characters.")
		.max(50, "Name must be at most 50 characters."),
	description: z
		.string({
			required_error: "Description is required!"
		})
		.min(10, "Description must be at least 10 characters."),
	startAt: z.date({ required_error: "Start date is required" }),
	endAt: z.date({ required_error: "End date is required" }),
	products: z.array(z.string()),
	// Add validation for profilePicture
	profilePicture: fileValidation("Profile picture"),
	// Add validation for banner
	banner: fileValidation("Banner image")
});

// Add date validation to ensure endAt is after startAt
export const dateValidatedCreateOfferSchema = createOfferSchema.refine(
	(data) => {
		if (!data.startAt || !data.endAt) return true;
		return new Date(data.endAt) > new Date(data.startAt);
	},
	{
		message: "End date must be after start date",
		path: ["endAt"]
	}
);

export const dateValidatedUpdateOfferSchema = updateOfferSchema.refine(
	(data) => {
		if (!data.startAt || !data.endAt) return true;
		return new Date(data.endAt) > new Date(data.startAt);
	},
	{
		message: "End date must be after start date",
		path: ["endAt"]
	}
);
