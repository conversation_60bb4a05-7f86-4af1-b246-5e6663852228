"use client";

import EInput from "@/components/shared/Form/EInput";
import PhotoInput from "@/components/shared/Form/PhotoInput";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ooter, <PERSON><PERSON><PERSON>eader, <PERSON>alogTitle } from "@/components/ui/dialog";
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Textarea } from "@/components/ui/textarea";
import { useToast } from "@/hooks/use-toast";
import { useCreateOfferMutation } from "@/redux/apis/offerApi";
import { useGetMinimalProductsQuery } from "@/redux/apis/productApis";
import { zodResolver } from "@hookform/resolvers/zod";
import { Loader2 } from "lucide-react";
import { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import type { z } from "zod";
import type { TProduct } from "../interfaces";
import { createOfferSchema } from "../validation";

const AddOffer = () => {
	const [createOffer, { isLoading: isCreatingOffer }] = useCreateOfferMutation();
	const { data: productsData, isLoading: isLoadingProducts } = useGetMinimalProductsQuery([
		{
			name: "search",
			value: ""
		}
	]);
	const [isDialogOpen, setIsDialogOpen] = useState(false);
	const { toast } = useToast();

	// Format a date as YYYY-MM-DD for input[type="date"]
	const formatDateForInput = (date: Date) => {
		return date.toISOString().split("T")[0];
	};

	// Get today and a week from today
	const today = new Date();
	const nextWeek = new Date();
	nextWeek.setDate(today.getDate() + 7);

	// State for date inputs
	const [startDateStr, setStartDateStr] = useState(formatDateForInput(today));
	const [endDateStr, setEndDateStr] = useState(formatDateForInput(nextWeek));

	const form = useForm<z.infer<typeof createOfferSchema>>({
		resolver: zodResolver(createOfferSchema),
		defaultValues: {
			name: "",
			description: "",
			startAt: today,
			endAt: nextWeek,
			products: [],
			profilePicture: undefined,
			banner: undefined
		}
	});

	// Update form values when date strings change
	useEffect(() => {
		if (startDateStr) {
			const date = new Date(startDateStr);
			form.setValue("startAt", date);
		}
	}, [startDateStr, form]);

	useEffect(() => {
		if (endDateStr) {
			const date = new Date(endDateStr);
			form.setValue("endAt", date);
		}
	}, [endDateStr, form]);

	const onSubmit = async (data: z.infer<typeof createOfferSchema>) => {
		const formData = new FormData();

		console.log("Form Data:", data);

		// Convert string dates to Date objects
		const startDate = new Date(startDateStr);
		const endDate = new Date(endDateStr);

		// Append profile picture if available
		if (data.profilePicture) {
			formData.append("profilePicture", data.profilePicture);
		}

		// Append banner if available
		if (data.banner) {
			formData.append("banner", data.banner);
		}

		formData.append(
			"data",
			JSON.stringify({
				name: data.name,
				description: data.description,
				startDate: startDate.toISOString(),
				endDate: endDate.toISOString(),
				products: data.products
			})
		);

		try {
			const res = await createOffer(formData).unwrap();

			if (res.success) {
				form.reset();
				setStartDateStr(formatDateForInput(new Date()));
				const newEndDate = new Date();
				newEndDate.setDate(newEndDate.getDate() + 7);
				setEndDateStr(formatDateForInput(newEndDate));
				toast({
					description: "Offer added successfully!"
				});
				setIsDialogOpen(false);
			}
		} catch (error: unknown) {
			toast({
				title: "Error",
				description: (error as { data?: { message?: string } })?.data?.message ?? "Something went wrong!",
				variant: "destructive"
			});
		}
	};

	return (
		<Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
			{/* Trigger Button */}
			<Button variant='outline' onClick={() => setIsDialogOpen(true)}>
				Add New
			</Button>

			{/* Dialog Content */}
			<DialogContent className='sm:max-w-[700px] max-h-[80vh] overflow-y-auto' aria-label='Add Offer Dialog'>
				<DialogHeader>
					<DialogTitle>Add Offer</DialogTitle>
				</DialogHeader>
				<div className='grid gap-4 py-4'>
					<Form {...form}>
						<form onSubmit={form.handleSubmit(onSubmit)} className='space-y-4' aria-label='Offer Form'>
							{/* Profile Picture Input */}
							<FormItem>
								<FormLabel>Profile Picture</FormLabel>
								<PhotoInput form={form} name='profilePicture' />
								<FormMessage />
							</FormItem>

							{/* Banner Image Input */}
							<FormItem>
								<FormLabel>Banner Image</FormLabel>
								<PhotoInput form={form} name='banner' />
								<FormMessage />
							</FormItem>

							{/* Name Input */}
							<EInput form={form} name='name' placeholder='Offer name' aria-label='Offer Name Input' />

							{/* Description Input */}
							<FormField
								control={form.control}
								name='description'
								render={({ field }) => (
									<FormItem>
										<FormLabel>Description</FormLabel>
										<FormControl>
											<Textarea placeholder='Enter offer description' className='resize-none' {...field} />
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>

							{/* Date Inputs - Using simple date inputs instead of Calendar */}
							<div className='grid grid-cols-1 md:grid-cols-2 gap-4'>
								{/* Start Date Input */}
								<FormItem>
									<FormLabel>Start Date</FormLabel>
									<Input
										type='date'
										value={startDateStr}
										onChange={(e) => {
											setStartDateStr(e.target.value);
											// If end date is before new start date, update end date
											if (endDateStr < e.target.value) {
												const newDate = new Date(e.target.value);
												newDate.setDate(newDate.getDate() + 7);
												setEndDateStr(formatDateForInput(newDate));
											}
										}}
										min={formatDateForInput(new Date())}
									/>
									<FormMessage />
								</FormItem>

								{/* End Date Input */}
								<FormItem>
									<FormLabel>End Date</FormLabel>
									<Input
										type='date'
										value={endDateStr}
										onChange={(e) => setEndDateStr(e.target.value)}
										min={startDateStr}
									/>
									<FormMessage />
								</FormItem>
							</div>

							{/* Date Range Preview */}
							<div className='text-sm text-muted-foreground'>
								<p>
									Offer will run for{" "}
									{Math.ceil(
										(new Date(endDateStr).getTime() - new Date(startDateStr).getTime()) / (1000 * 60 * 60 * 24)
									)}{" "}
									days
								</p>
							</div>

							{/* Products Selection */}
							<FormField
								control={form.control}
								name='products'
								render={() => (
									<FormItem>
										<FormLabel>Products</FormLabel>
										<ScrollArea className='h-[200px] border rounded-md p-4'>
											{isLoadingProducts ? (
												<div className='flex justify-center items-center h-full'>
													<Loader2 className='h-6 w-6 animate-spin text-muted-foreground' />
												</div>
											) : (
												<div className='space-y-4'>
													{productsData?.data?.map((product: TProduct) => (
														<FormField
															key={product._id}
															control={form.control}
															name='products'
															render={({ field }) => {
																return (
																	<FormItem key={product._id} className='flex flex-row items-start space-x-3 space-y-0'>
																		<FormControl>
																			<Checkbox
																				checked={field.value?.includes(product._id)}
																				onCheckedChange={(checked) => {
																					return checked
																						? field.onChange([...field.value, product._id])
																						: field.onChange(field.value?.filter((value) => value !== product._id));
																				}}
																			/>
																		</FormControl>
																		<div className='space-y-1 leading-none'>
																			<FormLabel className='text-sm font-medium'>{product.name}</FormLabel>
																			<div className='text-xs text-muted-foreground flex gap-2'>
																				<span>${product.actualPrice}</span>
																				{product.actualPrice < product.price && (
																					<span className='line-through'>${product.price}</span>
																				)}
																			</div>
																		</div>
																	</FormItem>
																);
															}}
														/>
													))}
													{(!productsData?.data || productsData.data.length === 0) && (
														<div className='text-center text-muted-foreground py-4'>No products available</div>
													)}
												</div>
											)}
										</ScrollArea>
										<FormMessage />
									</FormItem>
								)}
							/>

							{/* Footer Buttons */}
							<DialogFooter className='mt-6'>
								<Button type='button' variant='outline' onClick={() => setIsDialogOpen(false)} className='mr-2'>
									Cancel
								</Button>
								<Button type='submit' disabled={isCreatingOffer} aria-busy={isCreatingOffer} aria-label='Submit Button'>
									{isCreatingOffer ? (
										<>
											<Loader2 className='mr-2 h-4 w-4 animate-spin' />
											Adding...
										</>
									) : (
										"Add Offer"
									)}
								</Button>
							</DialogFooter>
						</form>
					</Form>
				</div>
			</DialogContent>
		</Dialog>
	);
};

export default AddOffer;
