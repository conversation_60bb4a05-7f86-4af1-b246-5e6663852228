"use client";

import EInput from "@/components/shared/Form/EInput";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Footer, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { Form } from "@/components/ui/form";
import { useToast } from "@/hooks/use-toast";
import { useUpdateOfferMutation } from "@/redux/apis/offerApi";
import { zodResolver } from "@hookform/resolvers/zod";
import { Pencil } from "lucide-react";
import { useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import UpdateSingleImage from "../../../../components/shared/UpdateSingleImage";
import { TOffer } from "../interfaces";
import { updateOfferSchema } from "../validation";

const UpdateOffer = ({ offer }: { offer: TOffer }) => {
	const [isDialogOpen, setIsDialogOpen] = useState(false);
	const [updateOffer, { isLoading }] = useUpdateOfferMutation();

	// Toast utility for showing messages
	const { toast } = useToast();

	// Form setup with validation schema and default values
	const form = useForm<z.infer<typeof updateOfferSchema>>({
		resolver: zodResolver(updateOfferSchema),
		defaultValues: {
			name: offer.name
		}
	});

	/**
	 * Handle offer update submission.
	 * @param data Form data containing the new offer name.
	 */
	const onSubmit = async (data: z.infer<typeof updateOfferSchema>) => {
		try {
			const res = await updateOffer({
				id: offer._id,
				data: { name: data.name }
			}).unwrap();

			if (res.success) {
				form.reset();
				toast({
					description: "Offer has been updated successfully."
				});
			}

			setIsDialogOpen(false);
		} catch (error) {
			const errorMessage =
				(error as { data?: { errorSources?: { message: string }[] } }).data?.errorSources
					?.map((errorSource) => errorSource.message)
					.join(", ") ?? "Something went wrong!";
			toast({
				title: "Offer Update Failed",
				description: errorMessage,
				variant: "destructive"
			});
		}
	};

	return (
		<Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
			{/* Trigger Button */}
			<Button variant='ghost' size='icon' onClick={() => setIsDialogOpen(true)}>
				<Pencil />
			</Button>

			{/* Dialog Content */}
			<DialogContent className='sm:max-w-[425px]' aria-label='Update Brand Dialog'>
				<DialogHeader>
					<DialogTitle>Update {offer?.name}</DialogTitle>
				</DialogHeader>

				<div className='grid gap-4 py-4'>
					{/* Brand Image Update Component */}
					<UpdateSingleImage id={offer._id} imageItemType={"offer"} />

					{/* Form for updating brand name */}
					<Form {...form}>
						<form onSubmit={form.handleSubmit(onSubmit)}>
							<EInput form={form} name='name' placeholder='Brand name' />
							<DialogFooter className='mt-4'>
								<Button type='submit' disabled={isLoading}>
									{isLoading ? "Updating..." : "Submit"}
								</Button>
							</DialogFooter>
						</form>
					</Form>
				</div>
			</DialogContent>
		</Dialog>
	);
};

export default UpdateOffer;
