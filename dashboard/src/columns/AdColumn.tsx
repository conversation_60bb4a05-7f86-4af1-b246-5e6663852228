"use client";

import DeleteAd from "@/app/(dashboard)/ads/_components/DeleteAd";
import { Button } from "@/components/ui/button";
import { IAd } from "@/interfaces";
import { dateToLocaleString, doubleDigitListNum } from "@/lib/utils";
import type { ColumnDef } from "@tanstack/react-table";
import { ArrowUpDown } from "lucide-react";

const AdColumn: ColumnDef<IAd>[] = [
	{
		accessorKey: "#",
		header: "#",
		cell: ({ row }) => {
			return <div>{doubleDigitListNum(row.index)}</div>;
		}
	},
	{
		accessorKey: "name",
		header: ({ column }) => {
			return (
				<Button variant='ghost' onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
					Name
					<ArrowUpDown className='ml-2 h-4 w-4' />
				</Button>
			);
		},
		cell: ({ row }) => <div className='capitalize min-w-32'>{row.original.user.name}</div>
	},
	{
		accessorKey: "phone",
		header: "Phone",
		cell: ({ row }) => {
			return <div className='min-w-24'>{row.original.user.phone}</div>;
		}
	},
	{
		accessorKey: "title",
		header: "Title",
		cell: ({ row }) => {
			return <div className='min-w-32'>{row.original.title}</div>;
		}
	},
	{
		accessorKey: "location",
		header: "Location",
		cell: ({ row }) => {
			return <div className='min-w-40 max-w-56 truncate'>{row.original.location}</div>;
		}
	},
	{
		accessorKey: "createdAt",
		header: "Created At",
		cell: ({ row }) => {
			return <div className='min-w-28'>{dateToLocaleString(row.original.createdAt)}</div>;
		}
	},
	{
		id: "actions",
		header: "Actions",
		enableHiding: false,
		cell: ({ row }) => {
			return (
				<div className='flex gap-1'>
					<DeleteAd id={row.original._id} />
				</div>
			);
		}
	}
];

export default AdColumn;
