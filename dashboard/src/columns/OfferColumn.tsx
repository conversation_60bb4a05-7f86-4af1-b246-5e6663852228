import DeleteOffer from "@/app/(dashboard)/offers/components/DeleteOffer";
import UpdateOffer from "@/app/(dashboard)/offers/components/UpdateOffer";
import { TOffer, TProduct } from "@/app/(dashboard)/offers/interfaces";
import ImageWithFallback from "@/components/shared/ImageWithFallback";
import { Button } from "@/components/ui/button";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { dateToLocaleString, doubleDigitListNum } from "@/lib/utils";
import { ColumnDef } from "@tanstack/react-table";
import { ArrowUpDown, ChevronDown, ListFilter, Trash2 } from "lucide-react";
// Helper function to format price
const OfferColumn: ColumnDef<TOffer>[] = [
	{
		accessorKey: "#",
		header: "#",
		cell: ({ row }) => {
			return <div>{doubleDigitListNum(row.index)}</div>;
		}
	},
	{
		accessorKey: "image",
		header: "Image",
		cell: ({ row }) => {
			const image = row.original.profilePicture;
			return (
				<ImageWithFallback
					src={image}
					alt={`Image of ${row.original.name}`}
					height={80}
					width={80}
					className='rounded-md w-16 object-cover'
				/>
			);
		}
	},
	{
		accessorKey: "name",
		header: ({ column }) => {
			return (
				<Button variant='ghost' onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
					Name
					<ArrowUpDown />
				</Button>
			);
		},
		cell: ({ row }) => <div className='capitalize min-w-32'> {row.getValue("name")} </div>
	},
	{
		accessorKey: "startedAt",
		header: "Started At",
		cell: ({ row }) => {
			return <div className='min-w-20'>{dateToLocaleString(row.original.startDate)}</div>;
		}
	},
	{
		accessorKey: "endAt",
		header: "End At",
		cell: ({ row }) => {
			return <div className='min-w-20'>{dateToLocaleString(row.original.endDate)}</div>;
		}
	},
	{
		accessorKey: "products",
		header: "Products",
		cell: ({ row }) => {
			const products = row.original.products || [];

			// const handleDeleteProduct = (productId: string, e: React.MouseEvent) => {
			// 	// Prevent the dropdown from closing when clicking delete
			// 	e.stopPropagation();
			// 	const updatedProducts = products.filter((product) => product._id != productId);
			// 	// Extract the remaining product IDs
			// 	const remainingProductIds = updatedProducts.map((product) => product._id);
			// 	// Implement your delete product logic here
			// 	console.log(`Delete product with ID: ${productId}, remainingProductIds`);
			// };

			return (
				<div className='min-w-24'>
					<DropdownMenu>
						<DropdownMenuTrigger asChild>
							<Button variant='outline' size='sm' className='w-full justify-between'>
								<span className='flex items-center gap-1'>
									<ListFilter className='h-4 w-4' />
									<span>
										{products.length} Product{products.length !== 1 ? "s" : ""}
									</span>
								</span>
								<ChevronDown className='h-4 w-4 opacity-50' />
							</Button>
						</DropdownMenuTrigger>
						<DropdownMenuContent align='start' className='w-56 max-h-80 overflow-y-auto'>
							{products.length > 0 ? (
								products.map((product: TProduct) => (
									<DropdownMenuItem key={product._id} className='flex flex-col items-start py-3 cursor-default'>
										<div className='flex w-full justify-between items-center'>
											<span className='font-medium truncate mr-2'>{product.name}</span>
											<Button
												variant='ghost'
												size='icon'
												className='h-7 w-7 ml-auto'
												// onClick={(e) => handleDeleteProduct(product._id, e)}
											>
												<Trash2 className='h-4 w-4 text-destructive' />
												<span className='sr-only'>Delete product</span>
											</Button>
										</div>
										<div className='flex gap-3 mt-1 text-sm'>
											{product.price < product.actualPrice ? (
												<>
													<span className='text-destructive font-medium'> ৳{product.actualPrice}</span>
													<span className='text-muted-foreground line-through'> ৳{product.price}</span>
													<span className='text-xs text-muted-foreground bg-muted px-1.5 rounded-sm'>
														{Math.round((1 - product.price / product.actualPrice) * 100)}% off
													</span>
												</>
											) : (
												<span className='font-medium'> ৳{product.price}</span>
											)}
										</div>
									</DropdownMenuItem>
								))
							) : (
								<DropdownMenuItem disabled className='text-muted-foreground text-center'>
									No products available
								</DropdownMenuItem>
							)}
						</DropdownMenuContent>
					</DropdownMenu>
				</div>
			);
		}
	},
	{
		id: "actions",
		header: "Actions",
		enableHiding: false,
		cell: ({ row }) => {
			return (
				<div className='flex gap-1'>
					<DeleteOffer id={row.original._id} />
					<UpdateOffer offer={row.original} />
				</div>
			);
		}
	}
];

export default OfferColumn;
