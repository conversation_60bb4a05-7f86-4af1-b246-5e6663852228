"use client";

import { TPreOrder } from "@/app/(dashboard)/pre-orders/interfaces/interfaces";
// import DeletePreOrder from "@/app/(dashboard)/pre-orders/components/DeletePreOrder"
// import UpdatePreOrder from "@/app/(dashboard)/pre-orders/components/UpdatePreOrder"

import ImageWithFallback from "@/components/shared/ImageWithFallback";
import { Button } from "@/components/ui/button";
import { dateToLocaleString, doubleDigitListNum } from "@/lib/utils";
import type { ColumnDef } from "@tanstack/react-table";
import { ArrowUpDown, ExternalLink } from "lucide-react";

const PreOrderColumn: ColumnDef<TPreOrder>[] = [
	{
		accessorKey: "#",
		header: "#",
		cell: ({ row }) => {
			return <div>{doubleDigitListNum(row.index)}</div>;
		}
	},
	{
		accessorKey: "image",
		header: "Image",
		cell: ({ row }) => {
			const image = row.original.image;
			return (
				<ImageWithFallback
					src={image || "/placeholder.svg"}
					alt={`Image for ${row.original.name}'s pre-order`}
					height={80}
					width={80}
					className='rounded-md w-16 object-cover'
				/>
			);
		}
	},
	{
		accessorKey: "name",
		header: ({ column }) => {
			return (
				<Button variant='ghost' onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
					Name
					<ArrowUpDown className='ml-2 h-4 w-4' />
				</Button>
			);
		},
		cell: ({ row }) => <div className='capitalize min-w-32'>{row.original.name}</div>
	},
	{
		accessorKey: "phone",
		header: "Phone",
		cell: ({ row }) => {
			return <div className='min-w-24'>{row.original.phone}</div>;
		}
	},
	{
		accessorKey: "email",
		header: "Email",
		cell: ({ row }) => {
			return <div className='min-w-32'>{row.original.email}</div>;
		}
	},
	{
		accessorKey: "address",
		header: "Address",
		cell: ({ row }) => {
			return <div className='min-w-40 max-w-56 truncate'>{row.original.address}</div>;
		}
	},
	{
		accessorKey: "productInformation",
		header: "Product Info",
		cell: ({ row }) => {
			return (
				<div className='min-w-40 max-w-56'>
					<Button
						variant='outline'
						size='sm'
						className='flex items-center gap-1'
						onClick={() => {
							// Open a modal or dialog to show full product information
							alert(row.original.productInformation);
						}}
					>
						<span className='truncate max-w-32'>
							{row.original.productInformation.substring(0, 20)}
							{row.original.productInformation.length > 20 ? "..." : ""}
						</span>
						<ExternalLink className='h-3 w-3' />
					</Button>
				</div>
			);
		}
	},
	{
		accessorKey: "createdAt",
		header: "Created At",
		cell: ({ row }) => {
			return <div className='min-w-28'>{dateToLocaleString(row.original.createdAt)}</div>;
		}
	}
	// {
	// 	id: "actions",
	// 	header: "Actions",
	// 	enableHiding: false,
	// 	cell: ({ row }) => {
	// 		return (
	// 			<div className='flex gap-1'>
	// 				{/* <DeletePreOrder id={row.original._id} />
	// 				<UpdatePreOrder preOrder={row.original} /> */}
	// 				actions will go here
	// 			</div>
	// 		);
	// 	}
	// }
];

export default PreOrderColumn;
