"use client";

import {
	TOrder,
	TOrderItem,
	TOrderStatus,
	TProductForOrder
} from "@/app/(dashboard)/orders/interfaces/order-interface";
import { But<PERSON> } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger
} from "@/components/ui/dropdown-menu";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { dateToLocaleStringV2, doubleDigitListNum } from "@/lib/utils";
import { useUpdateOrderStatusMutation } from "@/redux/apis/orders";

import type { ColumnDef } from "@tanstack/react-table";
import { ArrowUpDown, ChevronDown, Eye, Loader2 } from "lucide-react";
import { useState } from "react";

const OrderColumn: ColumnDef<TOrder>[] = [
	{
		accessorKey: "#",
		header: "#",
		cell: ({ row }) => {
			return <div>{doubleDigitListNum(row.index)}</div>;
		}
	},
	{
		accessorKey: "orderNumber",
		header: ({ column }) => {
			return (
				<Button variant='ghost' onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
					Order #
					<ArrowUpDown className='ml-2 h-4 w-4' />
				</Button>
			);
		},
		cell: ({ row }) => <div className='font-medium min-w-32'>{row.original.orderNumber}</div>
	},
	{
		accessorKey: "customerInfo",
		header: "Customer",
		cell: ({ row }) => {
			const customerInfo = row.original?.customerInfo || {};

			return (
				<Dialog>
					<DialogTrigger asChild>
						<Button title='Click to see customer info' variant='outline' size='sm' className='flex items-center gap-1'>
							<span>{customerInfo?.phone || "N/A"}</span>
							<Eye className='h-3 w-3' />
						</Button>
					</DialogTrigger>
					<DialogContent className='w-[750px] max-w-[750px]'>
						<DialogHeader>
							<DialogTitle>Customer Info</DialogTitle>
						</DialogHeader>
						<div className='max-h-[400px] overflow-auto'>
							<div>Name: {customerInfo?.name || "N/A"}</div>
							<div>Phone: {customerInfo?.phone || "N/A"}</div>
							<div>Address: {customerInfo?.address || "N/A"}</div>
						</div>
					</DialogContent>
				</Dialog>
			);
		}
	},
	// {
	// 	accessorKey: "customerName",
	// 	header: ({ column }) => {
	// 		return (
	// 			<Button variant='ghost' onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
	// 				Customer Name
	// 				<ArrowUpDown className='ml-2 h-4 w-4' />
	// 			</Button>
	// 		);
	// 	},
	// 	cell: ({ row }) => <div className='capitalize min-w-32'>{row.original?.customerInfo?.name || "N/A"}</div>
	// },
	// {
	// 	accessorKey: "customerPhone",
	// 	header: ({ column }) => {
	// 		return (
	// 			<Button variant='ghost' onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
	// 				Customer Phone
	// 				<ArrowUpDown className='ml-2 h-4 w-4' />
	// 			</Button>
	// 		);
	// 	},
	// 	cell: ({ row }) => <div className='capitalize min-w-32'>{row?.original?.customerInfo?.phone || "N/A"}</div>
	// },
	{
		accessorKey: "status",
		header: "Status",
		cell: ({ row }) => {
			const status = row.original.status;
			const [updateOrderStatus] = useUpdateOrderStatusMutation();
			const [isOpen, setIsOpen] = useState(false);
			const [isLoading, setIsLoading] = useState(false);

			const statusColors = {
				pending: "border-yellow-300 bg-yellow-50 text-yellow-700 hover:bg-yellow-100 hover:text-yellow-800",
				shipped: "border-blue-300 bg-blue-50 text-blue-700 hover:bg-blue-100 hover:text-blue-800",
				delivered: "border-green-300 bg-green-50 text-green-700 hover:bg-green-100 hover:text-green-800",
				canceled: "border-red-300 bg-red-50 text-red-700 hover:bg-red-100 hover:text-red-800"
			};

			const handleStatusChange = async (newStatus: TOrderStatus) => {
				try {
					setIsLoading(true);
					await updateOrderStatus({
						orderId: row.original._id,
						status: newStatus
					}).unwrap();
					setIsOpen(false);
				} catch (error) {
					console.error("Failed to update order status:", error);
				} finally {
					setIsLoading(false);
				}
			};

			return (
				<DropdownMenu open={isOpen} onOpenChange={setIsOpen}>
					<DropdownMenuTrigger asChild>
						<Button
							variant='outline'
							size='sm'
							className={`min-w-24 ${statusColors[status as keyof typeof statusColors]}`}
							disabled={isLoading}
						>
							{isLoading ? (
								<>
									<Loader2 className='mr-2 h-4 w-4 animate-spin' />
									Updating...
								</>
							) : (
								<>
									{status.charAt(0).toUpperCase() + status.slice(1)}
									<ChevronDown className='ml-2 h-4 w-4' />
								</>
							)}
						</Button>
					</DropdownMenuTrigger>
					<DropdownMenuContent align='end'>
						<DropdownMenuItem onClick={() => handleStatusChange("pending")}>Pending</DropdownMenuItem>
						<DropdownMenuItem onClick={() => handleStatusChange("shipped")}>Shipped</DropdownMenuItem>
						<DropdownMenuItem onClick={() => handleStatusChange("delivered")}>Delivered</DropdownMenuItem>
						<DropdownMenuItem onClick={() => handleStatusChange("canceled")}>Canceled</DropdownMenuItem>
					</DropdownMenuContent>
				</DropdownMenu>
			);
		}
	},
	{
		accessorKey: "totalAmount",
		header: ({ column }) => {
			return (
				<Button variant='ghost' onClick={() => column.toggleSorting(column.getIsSorted() === "asc")}>
					Total
					<ArrowUpDown className='ml-2 h-4 w-4' />
				</Button>
			);
		},
		cell: ({ row }) => {
			return <div className='min-w-24 font-medium'>৳{row.original.totalAmount}</div>;
		}
	},
	{
		accessorKey: "items",
		header: "Items",
		cell: ({ row }) => {
			const items: TOrderItem[] = row.original.items;
			const getOrderedVariant = (product: TProductForOrder, variantId: string) => {
				if (!product || typeof product === "string" || !variantId) return null;
				return product.variants?.find((v) => v._id === variantId) || null;
			};

			return (
				<Dialog>
					<DialogTrigger asChild>
						<Button variant='outline' size='sm' className='flex items-center gap-1'>
							<span>
								{items.length} {items.length === 1 ? "item" : "items"}
							</span>
							<Eye className='h-3 w-3' />
						</Button>
					</DialogTrigger>
					<DialogContent className='w-[750px] max-w-[750px]'>
						<DialogHeader>
							<DialogTitle>Order Items - {row.original.orderNumber}</DialogTitle>
						</DialogHeader>
						<div className='max-h-[400px] overflow-auto'>
							<Table>
								<TableHeader>
									<TableRow>
										<TableHead>Product</TableHead>
										<TableHead>Variant</TableHead>
										<TableHead>Quantity</TableHead>
										<TableHead>Price</TableHead>
										<TableHead>Total</TableHead>
									</TableRow>
								</TableHeader>
								<TableBody>
									{items.map((item, index) => {
										const product = item.productId;
										const variant = getOrderedVariant(product, item?.variantId as string);
										return (
											<TableRow key={index}>
												<TableCell className='w-[200px]'>
													<div className='flex items-start gap-3'>
														<img
															src={product?.images?.[0]}
															alt='Product Image'
															className='w-12 h-12 object-cover rounded-md'
														/>
														<div>
															<div className='font-medium'>{product?.name}</div>
															<div className='text-xs text-muted-foreground'>{product?.slug}</div>
														</div>
													</div>
												</TableCell>

												<TableCell>
													{variant ? (
														<div className='text-sm space-y-1'>
															{variant.color?.name && (
																<div>
																	<span className='font-medium'>Color:</span> {variant.color.name}
																</div>
															)}
															{variant.storage && (
																<div>
																	<span className='font-medium'>Storage:</span> {variant.storage}
																</div>
															)}
															{variant.condition && (
																<div>
																	<span className='font-medium'>Condition:</span> {variant.condition}
																</div>
															)}
															{variant.region && (
																<div>
																	<span className='font-medium'>Region:</span> {variant.region}
																</div>
															)}
														</div>
													) : (
														<span className='text-xs italic text-muted-foreground'>Base product</span>
													)}
												</TableCell>

												<TableCell>{item.quantity}</TableCell>
												<TableCell>৳{item.price}</TableCell>
												<TableCell>৳{item.price * item.quantity}</TableCell>
											</TableRow>
										);
									})}
								</TableBody>
							</Table>
						</div>
						<div className='flex justify-end font-medium'>Total: ৳{row.original.totalAmount}</div>
					</DialogContent>
				</Dialog>
			);
		}
	},
	{
		accessorKey: "orderDate",
		header: "Order Date",
		cell: ({ row }) => {
			return <div className='min-w-28'>{dateToLocaleStringV2(row.original.orderDate)}</div>;
		}
	},
	{
		accessorKey: "createdAt",
		header: "Created At",
		cell: ({ row }) => {
			return <div className='min-w-28'>{dateToLocaleStringV2(row.original.createdAt)}</div>;
		}
	}
];

export default OrderColumn;
