import { BadgeDollarSign, Megaphone } from "lucide-react";
import { BsBoxSeam, BsFolder2Open } from "react-icons/bs";
import { CgAddR } from "react-icons/cg";
import { CiImageOn } from "react-icons/ci";
import { LuShoppingCart } from "react-icons/lu";
import { MdAutoGraph, MdTextFields } from "react-icons/md";
import { PiBuildingOffice } from "react-icons/pi";

export const dashboardSidebarLinks = [
	{
		title: "Overview",
		path: "/",
		icon: <MdAutoGraph />
	},
	{
		title: "Banners",
		path: "/banners",
		icon: <CiImageOn />
	},
	{
		title: "Categories",
		path: "/categories",
		icon: <BsFolder2Open />
	},
	{
		title: "Brands",
		path: "/brands",
		icon: <PiBuildingOffice />
	},
	{
		title: "Products",
		path: "/products",
		icon: <BsBoxSeam />
	},

	{
		title: "Add Product",
		path: "/add-product",
		icon: <CgAddR />
	},

	{
		title: "Orders",
		path: "/orders",
		icon: <LuShoppingCart />
	},
	{
		title: "Pre Orders",
		path: "/pre-orders",
		icon: <LuShoppingCart />
	},
	{
		title: "Ads",
		path: "/ads",
		icon: <Megaphone />
	},
	{
		title: "Product Fields",
		path: "/product-fields",
		icon: <MdTextFields />
	},
	{
		title: "Offers",
		path: "/offers",
		icon: <BadgeDollarSign />
	}
];
