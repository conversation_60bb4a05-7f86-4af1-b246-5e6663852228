export type TQueryParams = {
	name: string;
	value: string;
};

export type TMeta = {
	page: number;
	limit: number;
	totalItems: number;
	totalPages: number;
};

export type TErrorSources = {
	path: string | number;
	message: string;
}[];

export type TErrorResponse = {
	statusCode: number;
	message: string;
	errorSources: TErrorSources;
};

export interface IAd {
	_id: string;
	slug: string;
	title: string;
	description: string;
	category: string;
	condition: string;
	price: number;
	images: string[];
	location: string;
	user: {
		_id: string;
		name: string;
		phone: string;
	};
	createdAt: string;
	updatedAt: Date | string;
}
