import config from "@/config";
import { createApi, fetchBaseQuery } from "@reduxjs/toolkit/query/react";

const baseQuery = fetchBaseQuery({
	baseUrl: config.serverURL,
	credentials: "include",
	prepareHeaders: (headers, { getState }) => {
		const token = (getState() as { auth: { token: string } }).auth.token;
		if (token) {
			headers.set("authorization", `${token}`);
		}
		return headers;
	}
});

export const baseApi = createApi({
	reducerPath: "baseApi",
	baseQuery: baseQuery,
	tagTypes: [
		"PrimaryCategory",
		"SecondaryCategory",
		"TertiaryCategory",
		"Products",
		"Product",
		"Brands",
		"ProductField",
		"Banner",
		"Offer",
		"PreOrders",
		"Ads",
		"Orders",
		"OverviewStats",
		"SalesStats",
		"InventoryStats"
	],
	endpoints: () => ({})
});
