import { baseApi } from "../baseApi";

const preOrderApis = baseApi.injectEndpoints({
	endpoints: (builder) => ({
		// Mutation: Create pre order
		createPreOrder: builder.mutation({
			query: (data: FormData) => ({
				url: "/pre-orders/create",
				method: "POST",
				body: data
			}),
			invalidatesTags: ["PreOrders"]
		}),

		// Query: get all pre orders
		getPreOrders: builder.query({
			query: () => ({
				url: `/pre-orders`,
				method: "GET"
			}),
			providesTags: ["PreOrders"]
		})
	})
});

export const { useCreatePreOrderMutation, useGetPreOrdersQuery } = preOrderApis;
