import { baseApi } from "../baseApi";

const adsApis = baseApi.injectEndpoints({
	endpoints: (builder) => ({
		getAllAds: builder.query({
			query: () => ({
				url: `/ads`,
				method: "GET"
			}),
			providesTags: ["Ads"]
		}),

		// Mutation: Delete ad
		deleteAd: builder.mutation({
			query: (id: string) => ({
				url: `/ads/${id}`,
				method: "DELETE"
			}),
			invalidatesTags: ["Ads"]
		})
	})
});

export const { useGetAllAdsQuery, useDeleteAdMutation } = adsApis;
