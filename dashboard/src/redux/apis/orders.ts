import { TOrderStatus } from "@/app/(dashboard)/orders/interfaces/order-interface";
import { baseApi } from "../baseApi";

const orderApis = baseApi.injectEndpoints({
	endpoints: (builder) => ({
		// Mutation: Create order
		createOrder: builder.mutation({
			query: (data) => ({
				url: "/orders/create",
				method: "POST",
				body: data
			}),
			invalidatesTags: ["Orders"]
		}),

		// Query: get all orders
		getOrders: builder.query({
			query: (params) => {
				if (params && params.length > 0) {
					const queryString = params.map((param: any) => `${param.name}=${encodeURIComponent(param.value)}`).join("&");
					return {
						url: `/orders?${queryString}`,
						method: "GET"
					};
				}
				return {
					url: `/orders`,
					method: "GET"
				};
			},
			providesTags: ["Orders"]
		}),

		// Mutation: Update order status
		updateOrderStatus: builder.mutation({
			query: ({ orderId, status }: { orderId: string; status: TOrderStatus }) => ({
				url: `/orders/status/${orderId}`,
				method: "PATCH",
				body: { status }
			}),
			invalidatesTags: ["Orders"]
		})
	})
});

export const { useCreateOrderMutation, useGetOrdersQuery, useUpdateOrderStatusMutation } = orderApis;
