/* eslint-disable @typescript-eslint/no-explicit-any */
import { TQueryParams } from "@/interfaces";
import { baseApi } from "../baseApi";

const productApis = baseApi.injectEndpoints({
	endpoints: (builder) => ({
		// Mutation: Create product
		createProduct: builder.mutation({
			query: (data: FormData) => ({
				url: "/products/create",
				method: "POST",
				body: data
			}),
			invalidatesTags: [{ type: "Products", id: "ProductLIST" }]
		}),

		// Query: Get products
		getProducts: builder.query({
			query: (args: TQueryParams[]) => {
				const params = args
					? args.map(({ name, value }) => `${encodeURIComponent(name)}=${encodeURIComponent(value)}`).join("&")
					: "";
				return {
					url: `/products?${params}`,
					method: "GET"
				};
			},
			providesTags: (result) =>
				result
					? [
							{ type: "Products", id: "ProductLIST" },
							...result.data.map(({ _id }: { _id: string }) => ({
								type: "Products",
								id: _id
							}))
					  ]
					: [{ type: "Products", id: "ProductLIST" }]
		}),

		// Query: Get products
		getMinimalProducts: builder.query({
			query: (args: TQueryParams[]) => {
				const params = args
					? args.map(({ name, value }) => `${encodeURIComponent(name)}=${encodeURIComponent(value)}`).join("&")
					: "";
				return {
					url: `/products/minimal?${params}`,
					method: "GET"
				};
			},
			providesTags: (result) =>
				result
					? [
							{ type: "Products", id: "ProductLIST" },
							...result.data.map(({ _id }: { _id: string }) => ({
								type: "Products",
								id: _id
							}))
					  ]
					: [{ type: "Products", id: "ProductLIST" }]
		}),

		getProductBySlug: builder.query({
			query: (slug: string) => ({
				url: `/products/slug/${slug}`,
				method: "GET"
			}),
			providesTags: ["Product"]
		}),

		// Mutation: Delete product
		deleteProduct: builder.mutation({
			query: (id: string) => ({
				url: `/products/${id}`,
				method: "DELETE"
			}),
			invalidatesTags: [{ type: "Products", id: "ProductLIST" }]
		}),

		// Mutation:  Toggle product status
		toggleProductStatus: builder.mutation({
			query: (id: string) => ({
				url: `/products/toggle-status/${id}`,
				method: "PATCH"
			}),
			invalidatesTags: [{ type: "Products", id: "ProductLIST" }]
		}),

		// Mutation: Update product
		updateProduct: builder.mutation({
			query: ({ data, id }: { data: Record<string, unknown>; id: string }) => ({
				url: `/products/${id}`,
				method: "PATCH",
				body: data
			}),
			invalidatesTags: ["Product", { type: "Products", id: "ProductLIST" }]
		}),

		// Mutation: Update product
		updateProductImages: builder.mutation({
			query: ({ data, productId }: { data: FormData; productId: string }) => ({
				url: `/products/update-images/${productId}`,
				method: "PATCH",
				body: data
			}),
			invalidatesTags: ["Product"]
		}),

		//! variant management apis
		createVariant: builder.mutation({
			query: ({ data, productId }) => ({
				url: `/products/create-variant/${productId}`,
				method: "POST",
				body: data
			}),
			invalidatesTags: [{ type: "Products", id: "ProductLIST" }, "Product"]
		}),

		deleteVariant: builder.mutation({
			query: ({ productId, variantId }: { productId: string; variantId: string }) => ({
				url: `/products/delete-variant/${productId}/${variantId}`,
				method: "DELETE"
			}),
			invalidatesTags: [{ type: "Products", id: "ProductLIST" }, "Product"]
		}),

		updateVariant: builder.mutation({
			query: ({ data, productId, variantId }: { data: any; productId: string; variantId: string }) => ({
				url: `/products/update-variant/${productId}/${variantId}`,
				method: "PATCH",
				body: data
			}),
			invalidatesTags: ["Product"] // Invalidates the product cache
		}),

		updateVariantImages: builder.mutation({
			query: ({ data, productId, variantId }: { data: any; productId: string; variantId: string }) => ({
				url: `/products/update-variant-images/${productId}/${variantId}`,
				method: "PATCH",
				body: data
			}),
			invalidatesTags: ["Product"] // Invalidates the product cache
		})
	})
});

export const {
	useCreateProductMutation,
	useGetProductsQuery,
	useGetMinimalProductsQuery,
	useDeleteProductMutation,
	useToggleProductStatusMutation,
	useGetProductBySlugQuery,
	useUpdateProductMutation,
	useUpdateProductImagesMutation,

	// variant management hooks
	useCreateVariantMutation,
	useDeleteVariantMutation,
	useUpdateVariantMutation,
	useUpdateVariantImagesMutation
} = productApis;
