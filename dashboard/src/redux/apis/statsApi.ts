import { baseApi } from "../baseApi";

const statsApis = baseApi.injectEndpoints({
	endpoints: (builder) => ({
		// Query: get all pre orders
		getOverviewStats: builder.query({
			query: () => ({
				url: `/admins/dashboard/overview`,
				method: "GET"
			}),
			providesTags: ["OverviewStats"]
		}),
		getSalesStats: builder.query({
			query: () => ({
				url: `/admins/dashboard/sales`,
				method: "GET"
			}),
			providesTags: ["SalesStats"]
		}),
		getInventoryStats: builder.query({
			query: () => ({
				url: `/admins/dashboard/inventory`,
				method: "GET"
			}),
			providesTags: ["InventoryStats"]
		})
	})
});

export const { useGetOverviewStatsQuery, useGetSalesStatsQuery, useGetInventoryStatsQuery } = statsApis;
