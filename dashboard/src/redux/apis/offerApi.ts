import { baseApi } from "../baseApi";

const offerApis = baseApi.injectEndpoints({
	endpoints: (builder) => ({
		// Mutation: Create banner
		createOffer: builder.mutation({
			query: (data: FormData) => ({
				url: "/offers/create",
				method: "POST",
				body: data
			}),
			invalidatesTags: ["Offer"]
		}),

		// Query: Get Secondary Categories with Dynamic Filters
		getOffers: builder.query({
			query: () => ({
				url: `/offers`,
				method: "GET"
			}),
			providesTags: ["Offer"]
		}),

		// Query: Get Secondary Categories with Dynamic Filters
		getOffersWithProducts: builder.query({
			query: () => ({
				url: `/offers/with-products`,
				method: "GET"
			}),
			providesTags: ["Offer"]
		}),

		// Mutation: Delete banner
		deleteOffer: builder.mutation({
			query: (id: string) => ({
				url: `/offers/${id}`,
				method: "DELETE"
			}),
			invalidatesTags: ["Offer"]
		}),

		toggleOfferStatus: builder.mutation({
			query: (id: string) => ({ url: `/offers/toggle/${id}`, method: "PUT" }),
			invalidatesTags: ["Offer"]
		}),

		// Mutation: Update banner Details
		updateOffer: builder.mutation({
			query: ({ id, data }: { id: string; data: unknown }) => ({
				url: `/offers/${id}`,
				method: "PATCH",
				body: data
			}),
			invalidatesTags: ["Offer"]
		})
	})
});

export const {
	useCreateOfferMutation,
	useGetOffersQuery,
	useGetOffersWithProductsQuery,
	useDeleteOfferMutation,
	useToggleOfferStatusMutation,
	useUpdateOfferMutation
} = offerApis;
